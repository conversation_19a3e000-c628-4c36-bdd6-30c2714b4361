{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useInputHistory.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25276, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 25276, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 672, "endOffset": 8274, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 757, "endOffset": 792, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 939, "endOffset": 1358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1014, "endOffset": 1211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1253, "endOffset": 1303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1388, "endOffset": 2622, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1464, "endOffset": 2097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1543, "endOffset": 1762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1808, "endOffset": 1880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1984, "endOffset": 2038, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2168, "endOffset": 2616, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2247, "endOffset": 2452, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2498, "endOffset": 2557, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2650, "endOffset": 5976, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2709, "endOffset": 3215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2788, "endOffset": 3001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3047, "endOffset": 3156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3273, "endOffset": 3782, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3352, "endOffset": 3568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3614, "endOffset": 3723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3876, "endOffset": 4372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3999, "endOffset": 4200, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4246, "endOffset": 4298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4457, "endOffset": 5115, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4586, "endOffset": 4787, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4833, "endOffset": 4885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4990, "endOffset": 5044, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5200, "endOffset": 5970, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5279, "endOffset": 5484, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5530, "endOffset": 5582, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5687, "endOffset": 5739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5844, "endOffset": 5896, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6006, "endOffset": 8270, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6065, "endOffset": 6880, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.initialProps", "ranges": [{"startOffset": 6384, "endOffset": 6443, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6533, "endOffset": 6585, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6710, "endOffset": 6821, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6963, "endOffset": 7470, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7042, "endOffset": 7254, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7300, "endOffset": 7411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7559, "endOffset": 8264, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7687, "endOffset": 7903, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7949, "endOffset": 8001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8138, "endOffset": 8192, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useInputHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 21}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "useInputHistory", "ranges": [{"startOffset": 590, "endOffset": 2698, "count": 21}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 937, "endOffset": 1008, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1081, "endOffset": 1232, "count": 2}, {"startOffset": 1159, "endOffset": 1200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1330, "endOffset": 1905, "count": 9}, {"startOffset": 1357, "endOffset": 1370, "count": 1}, {"startOffset": 1370, "endOffset": 1406, "count": 8}, {"startOffset": 1406, "endOffset": 1419, "count": 1}, {"startOffset": 1419, "endOffset": 1483, "count": 7}, {"startOffset": 1483, "endOffset": 1558, "count": 5}, {"startOffset": 1558, "endOffset": 1684, "count": 2}, {"startOffset": 1651, "endOffset": 1684, "count": 0}, {"startOffset": 1684, "endOffset": 1882, "count": 7}, {"startOffset": 1882, "endOffset": 1904, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2144, "endOffset": 2508, "count": 6}, {"startOffset": 2171, "endOffset": 2184, "count": 1}, {"startOffset": 2184, "endOffset": 2214, "count": 5}, {"startOffset": 2214, "endOffset": 2227, "count": 3}, {"startOffset": 2227, "endOffset": 2373, "count": 2}, {"startOffset": 2373, "endOffset": 2486, "count": 0}, {"startOffset": 2486, "endOffset": 2507, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}]}