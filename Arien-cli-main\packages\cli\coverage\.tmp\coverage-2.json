{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/DiffRenderer.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39953, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39953, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1314, "endOffset": 17595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1437, "endOffset": 1482, "count": 13}], "isBlockCoverage": true}, {"functionName": "sanitizeOutput", "ranges": [{"startOffset": 1510, "endOffset": 1597, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1688, "endOffset": 2870, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2961, "endOffset": 4143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4238, "endOffset": 5290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5402, "endOffset": 6898, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6960, "endOffset": 8092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8136, "endOffset": 9008, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9067, "endOffset": 10397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10492, "endOffset": 11881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11969, "endOffset": 14641, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13441, "endOffset": 14630, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14706, "endOffset": 16224, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16303, "endOffset": 17591, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1033", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 11}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 13}, {"startOffset": 491, "endOffset": 499, "count": 0}], "isBlockCoverage": true}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1212, "endOffset": 1276, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1305, "endOffset": 1371, "count": 11}], "isBlockCoverage": true}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1398, "endOffset": 3012, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1796, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1626, "endOffset": 1790, "count": 2}, {"startOffset": 1668, "endOffset": 1701, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1871, "endOffset": 2077, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1903, "endOffset": 2071, "count": 9}, {"startOffset": 1979, "endOffset": 2070, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2144, "endOffset": 2180, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2270, "endOffset": 2335, "count": 13}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/DiffRenderer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 13}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseDiffWithLineNumbers", "ranges": [{"startOffset": 1272, "endOffset": 2953, "count": 12}, {"startOffset": 1546, "endOffset": 2933, "count": 146}, {"startOffset": 1618, "endOffset": 1864, "count": 17}, {"startOffset": 1864, "endOffset": 1883, "count": 129}, {"startOffset": 1883, "endOffset": 2229, "count": 65}, {"startOffset": 1919, "endOffset": 1945, "count": 52}, {"startOffset": 1946, "endOffset": 1978, "count": 39}, {"startOffset": 1979, "endOffset": 2007, "count": 29}, {"startOffset": 2008, "endOffset": 2046, "count": 19}, {"startOffset": 2047, "endOffset": 2080, "count": 19}, {"startOffset": 2081, "endOffset": 2112, "count": 19}, {"startOffset": 2113, "endOffset": 2148, "count": 19}, {"startOffset": 2149, "endOffset": 2188, "count": 16}, {"startOffset": 2198, "endOffset": 2207, "count": 49}, {"startOffset": 2207, "endOffset": 2229, "count": 16}, {"startOffset": 2229, "endOffset": 2261, "count": 64}, {"startOffset": 2261, "endOffset": 2411, "count": 16}, {"startOffset": 2411, "endOffset": 2929, "count": 48}, {"startOffset": 2443, "endOffset": 2593, "count": 10}, {"startOffset": 2593, "endOffset": 2929, "count": 38}, {"startOffset": 2625, "endOffset": 2836, "count": 25}, {"startOffset": 2836, "endOffset": 2929, "count": 13}, {"startOffset": 2869, "endOffset": 2929, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3004, "endOffset": 5269, "count": 13}, {"startOffset": 3136, "endOffset": 3170, "count": 12}, {"startOffset": 3172, "endOffset": 3564, "count": 1}, {"startOffset": 3564, "endOffset": 3659, "count": 12}, {"startOffset": 3659, "endOffset": 4406, "count": 1}, {"startOffset": 4406, "endOffset": 4659, "count": 11}, {"startOffset": 4659, "endOffset": 5081, "count": 4}, {"startOffset": 4810, "endOffset": 4817, "count": 3}, {"startOffset": 4829, "endOffset": 4836, "count": 1}, {"startOffset": 4873, "endOffset": 4914, "count": 3}, {"startOffset": 4915, "endOffset": 4921, "count": 1}, {"startOffset": 5081, "endOffset": 5241, "count": 7}, {"startOffset": 5241, "endOffset": 5268, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4451, "endOffset": 4614, "count": 25}, {"startOffset": 4481, "endOffset": 4504, "count": 19}, {"startOffset": 4505, "endOffset": 4529, "count": 8}, {"startOffset": 4530, "endOffset": 4570, "count": 7}, {"startOffset": 4571, "endOffset": 4614, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4705, "endOffset": 4734, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4740, "endOffset": 4762, "count": 6}], "isBlockCoverage": true}, {"functionName": "renderDiffContent", "ranges": [{"startOffset": 5297, "endOffset": 11501, "count": 7}, {"startOffset": 5678, "endOffset": 6425, "count": 0}, {"startOffset": 6500, "endOffset": 6739, "count": 45}, {"startOffset": 6538, "endOffset": 6547, "count": 0}, {"startOffset": 6650, "endOffset": 6653, "count": 0}, {"startOffset": 6775, "endOffset": 6805, "count": 0}, {"startOffset": 6855, "endOffset": 6969, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5438, "endOffset": 5531, "count": 59}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5589, "endOffset": 5635, "count": 59}, {"startOffset": 5614, "endOffset": 5635, "count": 46}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7264, "endOffset": 11261, "count": 45}, {"startOffset": 7369, "endOffset": 7395, "count": 35}, {"startOffset": 7397, "endOffset": 7471, "count": 35}, {"startOffset": 7453, "endOffset": 7460, "count": 0}, {"startOffset": 7471, "endOffset": 7576, "count": 10}, {"startOffset": 7558, "endOffset": 7565, "count": 0}, {"startOffset": 7614, "endOffset": 7654, "count": 38}, {"startOffset": 7655, "endOffset": 7739, "count": 38}, {"startOffset": 7741, "endOffset": 8524, "count": 5}, {"startOffset": 8727, "endOffset": 8929, "count": 10}, {"startOffset": 8780, "endOffset": 8785, "count": 0}, {"startOffset": 8902, "endOffset": 8909, "count": 0}, {"startOffset": 8940, "endOffset": 9192, "count": 10}, {"startOffset": 8993, "endOffset": 8998, "count": 0}, {"startOffset": 9203, "endOffset": 9404, "count": 25}, {"startOffset": 9260, "endOffset": 9265, "count": 0}, {"startOffset": 9377, "endOffset": 9384, "count": 0}, {"startOffset": 9415, "endOffset": 9447, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLanguageFromExtension", "ranges": [{"startOffset": 11536, "endOffset": 11892, "count": 3}, {"startOffset": 11882, "endOffset": 11889, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 49}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 1}], "isBlockCoverage": true}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 48}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 53}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 53}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 7}], "isBlockCoverage": true}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/CodeColorizer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 5}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "renderHastNode", "ranges": [{"startOffset": 1534, "endOffset": 3600, "count": 27}, {"startOffset": 1617, "endOffset": 1960, "count": 14}, {"startOffset": 1960, "endOffset": 1993, "count": 13}, {"startOffset": 1993, "endOffset": 3072, "count": 7}, {"startOffset": 2046, "endOffset": 2051, "count": 0}, {"startOffset": 2315, "endOffset": 2332, "count": 0}, {"startOffset": 3072, "endOffset": 3582, "count": 6}, {"startOffset": 3158, "endOffset": 3184, "count": 1}, {"startOffset": 3184, "endOffset": 3215, "count": 5}, {"startOffset": 3582, "endOffset": 3599, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2381, "endOffset": 2750, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3216, "endOffset": 3576, "count": 14}], "isBlockCoverage": true}, {"functionName": "colorizeCode", "ranges": [{"startOffset": 3601, "endOffset": 8195, "count": 4}, {"startOffset": 3963, "endOffset": 4258, "count": 0}, {"startOffset": 6381, "endOffset": 8193, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHighlightedLines", "ranges": [{"startOffset": 4292, "endOffset": 4413, "count": 6}, {"startOffset": 4312, "endOffset": 4345, "count": 1}, {"startOffset": 4346, "endOffset": 4376, "count": 5}, {"startOffset": 4377, "endOffset": 4413, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4699, "endOffset": 6132, "count": 6}, {"startOffset": 4917, "endOffset": 4931, "count": 5}, {"startOffset": 4932, "endOffset": 4938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6838, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1777", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 11}, {"startOffset": 495, "endOffset": 503, "count": 0}], "isBlockCoverage": true}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1813, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1884, "endOffset": 2611, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2632, "endOffset": 7251, "count": 11}, {"startOffset": 2889, "endOffset": 2894, "count": 0}, {"startOffset": 2989, "endOffset": 3015, "count": 9}, {"startOffset": 3073, "endOffset": 3150, "count": 0}, {"startOffset": 3845, "endOffset": 3878, "count": 9}, {"startOffset": 3931, "endOffset": 3960, "count": 2}, {"startOffset": 3961, "endOffset": 3982, "count": 2}, {"startOffset": 3983, "endOffset": 4000, "count": 9}, {"startOffset": 4124, "endOffset": 4127, "count": 0}, {"startOffset": 4541, "endOffset": 4691, "count": 2}, {"startOffset": 4641, "endOffset": 4691, "count": 0}, {"startOffset": 4692, "endOffset": 4711, "count": 9}, {"startOffset": 5933, "endOffset": 5963, "count": 2}, {"startOffset": 5964, "endOffset": 6465, "count": 2}, {"startOffset": 6210, "endOffset": 6214, "count": 0}, {"startOffset": 6510, "endOffset": 6543, "count": 2}, {"startOffset": 6544, "endOffset": 7044, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRows", "ranges": [{"startOffset": 3154, "endOffset": 3670, "count": 56}, {"startOffset": 3248, "endOffset": 3269, "count": 0}, {"startOffset": 3328, "endOffset": 3438, "count": 0}, {"startOffset": 3587, "endOffset": 3669, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4241, "endOffset": 4426, "count": 11}, {"startOffset": 4279, "endOffset": 4316, "count": 2}, {"startOffset": 4316, "endOffset": 4362, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4375, "endOffset": 4421, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4758, "endOffset": 5745, "count": 48}, {"startOffset": 5246, "endOffset": 5545, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4894, "endOffset": 5244, "count": 206}], "isBlockCoverage": true}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 7253, "endOffset": 10048, "count": 56}, {"startOffset": 7393, "endOffset": 7596, "count": 0}, {"startOffset": 7620, "endOffset": 8168, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRowChild", "ranges": [{"startOffset": 8264, "endOffset": 9905, "count": 445}, {"startOffset": 8339, "endOffset": 8360, "count": 0}, {"startOffset": 8399, "endOffset": 8430, "count": 194}, {"startOffset": 8432, "endOffset": 9058, "count": 251}, {"startOffset": 8488, "endOffset": 8513, "count": 0}, {"startOffset": 8564, "endOffset": 8569, "count": 0}, {"startOffset": 8638, "endOffset": 8714, "count": 60}, {"startOffset": 8714, "endOffset": 9037, "count": 191}, {"startOffset": 8806, "endOffset": 9029, "count": 0}, {"startOffset": 9058, "endOffset": 9125, "count": 194}, {"startOffset": 9125, "endOffset": 9200, "count": 0}, {"startOffset": 9200, "endOffset": 9260, "count": 194}, {"startOffset": 9260, "endOffset": 9430, "count": 28}, {"startOffset": 9430, "endOffset": 9486, "count": 166}, {"startOffset": 9486, "endOffset": 9615, "count": 0}, {"startOffset": 9615, "endOffset": 9722, "count": 166}, {"startOffset": 9722, "endOffset": 9736, "count": 152}, {"startOffset": 9737, "endOffset": 9774, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9357, "endOffset": 9401, "count": 28}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9850, "endOffset": 9894, "count": 265}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9988, "endOffset": 10027, "count": 152}], "isBlockCoverage": true}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 10049, "endOffset": 14127, "count": 56}, {"startOffset": 10184, "endOffset": 10218, "count": 5}, {"startOffset": 10220, "endOffset": 10258, "count": 0}, {"startOffset": 10533, "endOffset": 11190, "count": 5}, {"startOffset": 10962, "endOffset": 11067, "count": 0}, {"startOffset": 11190, "endOffset": 11271, "count": 51}, {"startOffset": 11271, "endOffset": 11325, "count": 0}, {"startOffset": 11325, "endOffset": 14097, "count": 51}, {"startOffset": 14097, "endOffset": 14125, "count": 57}, {"startOffset": 14125, "endOffset": 14126, "count": 51}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10369, "endOffset": 10497, "count": 191}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10615, "endOffset": 10928, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10704, "endOffset": 10920, "count": 5}, {"startOffset": 10746, "endOffset": 10821, "count": 0}], "isBlockCoverage": true}, {"functionName": "addWrappingPartToLines", "ranges": [{"startOffset": 11383, "endOffset": 11796, "count": 57}, {"startOffset": 11447, "endOffset": 11514, "count": 51}, {"startOffset": 11514, "endOffset": 11741, "count": 6}, {"startOffset": 11686, "endOffset": 11735, "count": 0}], "isBlockCoverage": true}, {"functionName": "addToWrappingPart", "ranges": [{"startOffset": 11799, "endOffset": 12055, "count": 279}, {"startOffset": 11873, "endOffset": 11929, "count": 222}, {"startOffset": 11931, "endOffset": 11996, "count": 213}, {"startOffset": 11996, "endOffset": 12051, "count": 66}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12079, "endOffset": 13998, "count": 60}, {"startOffset": 13955, "endOffset": 13994, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12178, "endOffset": 13915, "count": 60}, {"startOffset": 12230, "endOffset": 12273, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12340, "endOffset": 13907, "count": 284}, {"startOffset": 12371, "endOffset": 12378, "count": 5}, {"startOffset": 12378, "endOffset": 12505, "count": 279}, {"startOffset": 12505, "endOffset": 12529, "count": 6}, {"startOffset": 12531, "endOffset": 12646, "count": 6}, {"startOffset": 12603, "endOffset": 12636, "count": 0}, {"startOffset": 12646, "endOffset": 12688, "count": 279}, {"startOffset": 12688, "endOffset": 13790, "count": 0}, {"startOffset": 13790, "endOffset": 13899, "count": 279}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1778", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}