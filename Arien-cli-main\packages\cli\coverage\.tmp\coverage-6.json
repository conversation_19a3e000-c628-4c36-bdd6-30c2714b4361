{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/slashCommandProcessor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 139278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 139278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 503, "endOffset": 580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 559, "endOffset": 576, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 1544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 715, "endOffset": 732, "count": 0}], "isBlockCoverage": false}, {"functionName": "get env", "ranges": [{"startOffset": 739, "endOffset": 782, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 952, "endOffset": 1093, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1217, "endOffset": 1234, "count": 0}], "isBlockCoverage": false}, {"functionName": "get env", "ranges": [{"startOffset": 1239, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1411, "endOffset": 1540, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1734, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1793, "endOffset": 1863, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCliVersion", "ranges": [{"startOffset": 1819, "endOffset": 1860, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1929, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2051, "endOffset": 2197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2158, "endOffset": 2193, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2238, "endOffset": 2290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2348, "endOffset": 2918, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4014, "endOffset": 4044, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4119, "endOffset": 46964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4600, "endOffset": 6537, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5444, "endOffset": 5455, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 5480, "endOffset": 5501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5549, "endOffset": 5569, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5616, "endOffset": 5634, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5687, "endOffset": 5704, "count": 60}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5766, "endOffset": 5776, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5828, "endOffset": 5840, "count": 1}], "isBlockCoverage": true}, {"functionName": "getProcessorHook", "ranges": [{"startOffset": 6567, "endOffset": 7282, "count": 29}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6738, "endOffset": 7271, "count": 32}], "isBlockCoverage": true}, {"functionName": "getProcessor", "ranges": [{"startOffset": 7307, "endOffset": 7394, "count": 27}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7448, "endOffset": 9523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7542, "endOffset": 8771, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7715, "endOffset": 7809, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8873, "endOffset": 9517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9005, "endOffset": 9092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9579, "endOffset": 10504, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9672, "endOffset": 10498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10007, "endOffset": 10094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10563, "endOffset": 11043, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10656, "endOffset": 11037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10788, "endOffset": 10878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11113, "endOffset": 11872, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11225, "endOffset": 11866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11357, "endOffset": 11446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11930, "endOffset": 13205, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12014, "endOffset": 13199, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12713, "endOffset": 12772, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13263, "endOffset": 15923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13377, "endOffset": 15124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13868, "endOffset": 14424, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14466, "endOffset": 14546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15212, "endOffset": 15917, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15563, "endOffset": 15643, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15981, "endOffset": 17700, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16064, "endOffset": 16434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16196, "endOffset": 16276, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16531, "endOffset": 17236, "count": 1}], "isBlockCoverage": true}, {"functionName": "getArienClient", "ranges": [{"startOffset": 16672, "endOffset": 16726, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16854, "endOffset": 16935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17325, "endOffset": 17694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17457, "endOffset": 17539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17756, "endOffset": 21630, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17842, "endOffset": 17996, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18039, "endOffset": 18085, "count": 2}], "isBlockCoverage": true}, {"functionName": "getExpectedUrl", "ranges": [{"startOffset": 18115, "endOffset": 19167, "count": 1}, {"startOffset": 18423, "endOffset": 18553, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19272, "endOffset": 20128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19780, "endOffset": 19877, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20230, "endOffset": 21624, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20600, "endOffset": 20616, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21276, "endOffset": 21373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21698, "endOffset": 23101, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21747, "endOffset": 21808, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21851, "endOffset": 21912, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22051, "endOffset": 23090, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22298, "endOffset": 22360, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22913, "endOffset": 22997, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23160, "endOffset": 23889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23271, "endOffset": 23883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23403, "endOffset": 23493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23947, "endOffset": 28884, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24046, "endOffset": 24776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24312, "endOffset": 24393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24869, "endOffset": 25681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25217, "endOffset": 25298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25782, "endOffset": 26729, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26381, "endOffset": 26462, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26831, "endOffset": 27625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27332, "endOffset": 27413, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27722, "endOffset": 28878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28357, "endOffset": 28443, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28940, "endOffset": 42564, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28989, "endOffset": 29008, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29102, "endOffset": 29838, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29368, "endOffset": 29447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29961, "endOffset": 31024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30423, "endOffset": 30502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31156, "endOffset": 32241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31579, "endOffset": 31658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32357, "endOffset": 35283, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32617, "endOffset": 32879, "count": 6}, {"startOffset": 32673, "endOffset": 32722, "count": 2}, {"startOffset": 32722, "endOffset": 32761, "count": 4}, {"startOffset": 32761, "endOffset": 32878, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33351, "endOffset": 33584, "count": 3}, {"startOffset": 33407, "endOffset": 33431, "count": 1}, {"startOffset": 33431, "endOffset": 33470, "count": 2}, {"startOffset": 33470, "endOffset": 33557, "count": 1}, {"startOffset": 33557, "endOffset": 33583, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33979, "endOffset": 34058, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35389, "endOffset": 37788, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35646, "endOffset": 35820, "count": 2}, {"startOffset": 35751, "endOffset": 35819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36595, "endOffset": 36674, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37870, "endOffset": 40249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38092, "endOffset": 38357, "count": 4}, {"startOffset": 38148, "endOffset": 38288, "count": 2}, {"startOffset": 38288, "endOffset": 38356, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38693, "endOffset": 38863, "count": 2}, {"startOffset": 38749, "endOffset": 38836, "count": 1}, {"startOffset": 38836, "endOffset": 38862, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39258, "endOffset": 39337, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40346, "endOffset": 42558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40568, "endOffset": 40831, "count": 4}, {"startOffset": 40624, "endOffset": 40762, "count": 2}, {"startOffset": 40762, "endOffset": 40830, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 41194, "endOffset": 41364, "count": 2}, {"startOffset": 41250, "endOffset": 41337, "count": 1}, {"startOffset": 41337, "endOffset": 41363, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 41759, "endOffset": 41838, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42619, "endOffset": 45341, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42708, "endOffset": 45335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42965, "endOffset": 43139, "count": 2}, {"startOffset": 43070, "endOffset": 43138, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44158, "endOffset": 44244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45402, "endOffset": 46960, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45480, "endOffset": 46954, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45582, "endOffset": 46150, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45695, "endOffset": 45745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46185, "endOffset": 46267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46302, "endOffset": 46348, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/slashCommandProcessor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 116707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 116707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 33}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "useSlashCommandProcessor", "ranges": [{"startOffset": 2461, "endOffset": 37458, "count": 33}, {"startOffset": 3202, "endOffset": 3272, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2850, "endOffset": 2993, "count": 30}, {"startOffset": 2893, "endOffset": 2914, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3338, "endOffset": 4653, "count": 23}, {"startOffset": 3451, "endOffset": 3792, "count": 2}, {"startOffset": 3792, "endOffset": 4582, "count": 21}, {"startOffset": 3860, "endOffset": 4053, "count": 1}, {"startOffset": 4053, "endOffset": 4582, "count": 20}, {"startOffset": 4120, "endOffset": 4264, "count": 0}, {"startOffset": 4338, "endOffset": 4463, "count": 1}, {"startOffset": 4463, "endOffset": 4582, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4739, "endOffset": 4879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4983, "endOffset": 5618, "count": 2}, {"startOffset": 5038, "endOffset": 5059, "count": 1}, {"startOffset": 5061, "endOffset": 5617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5704, "endOffset": 6100, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6171, "endOffset": 35277, "count": 33}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 6316, "endOffset": 6437, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 6567, "endOffset": 7391, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 7514, "endOffset": 7769, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 7867, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8048, "endOffset": 8125, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8238, "endOffset": 8317, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 8427, "endOffset": 8507, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8634, "endOffset": 9183, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 9300, "endOffset": 16616, "count": 8}, {"startOffset": 9476, "endOffset": 9529, "count": 0}, {"startOffset": 9601, "endOffset": 9655, "count": 0}, {"startOffset": 9711, "endOffset": 9764, "count": 0}, {"startOffset": 9824, "endOffset": 9878, "count": 0}, {"startOffset": 9956, "endOffset": 9977, "count": 7}, {"startOffset": 9979, "endOffset": 10026, "count": 1}, {"startOffset": 10121, "endOffset": 10368, "count": 1}, {"startOffset": 10368, "endOffset": 10420, "count": 7}, {"startOffset": 10423, "endOffset": 10428, "count": 0}, {"startOffset": 10525, "endOffset": 11650, "count": 2}, {"startOffset": 11147, "endOffset": 11617, "count": 0}, {"startOffset": 11650, "endOffset": 12043, "count": 5}, {"startOffset": 12043, "endOffset": 12074, "count": 4}, {"startOffset": 12076, "endOffset": 12332, "count": 1}, {"startOffset": 12332, "endOffset": 12608, "count": 5}, {"startOffset": 12608, "endOffset": 16398, "count": 9}, {"startOffset": 12945, "endOffset": 13099, "count": 6}, {"startOffset": 13114, "endOffset": 13307, "count": 1}, {"startOffset": 13322, "endOffset": 13378, "count": 2}, {"startOffset": 13393, "endOffset": 13509, "count": 2}, {"startOffset": 13676, "endOffset": 13746, "count": 0}, {"startOffset": 13867, "endOffset": 13941, "count": 6}, {"startOffset": 13941, "endOffset": 14174, "count": 3}, {"startOffset": 14012, "endOffset": 14087, "count": 1}, {"startOffset": 14087, "endOffset": 14174, "count": 2}, {"startOffset": 14213, "endOffset": 14229, "count": 7}, {"startOffset": 14231, "endOffset": 14253, "count": 2}, {"startOffset": 14255, "endOffset": 14712, "count": 2}, {"startOffset": 14642, "endOffset": 14698, "count": 0}, {"startOffset": 14712, "endOffset": 14764, "count": 7}, {"startOffset": 14840, "endOffset": 16284, "count": 8}, {"startOffset": 16284, "endOffset": 16356, "count": 1}, {"startOffset": 16398, "endOffset": 16615, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11720, "endOffset": 11833, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14876, "endOffset": 16268, "count": 11}, {"startOffset": 14929, "endOffset": 14945, "count": 7}, {"startOffset": 14947, "endOffset": 14966, "count": 4}, {"startOffset": 14968, "endOffset": 15537, "count": 4}, {"startOffset": 15455, "endOffset": 15519, "count": 0}, {"startOffset": 15537, "endOffset": 15627, "count": 7}, {"startOffset": 15664, "endOffset": 16252, "count": 2}, {"startOffset": 16108, "endOffset": 16214, "count": 12}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 16762, "endOffset": 17753, "count": 5}, {"startOffset": 16843, "endOffset": 16911, "count": 1}, {"startOffset": 16924, "endOffset": 16999, "count": 1}, {"startOffset": 17012, "endOffset": 17092, "count": 2}, {"startOffset": 17138, "endOffset": 17428, "count": 0}, {"startOffset": 17441, "endOffset": 17731, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 17865, "endOffset": 20010, "count": 5}, {"startOffset": 18006, "endOffset": 18039, "count": 4}, {"startOffset": 18041, "endOffset": 18094, "count": 1}, {"startOffset": 18094, "endOffset": 18443, "count": 4}, {"startOffset": 18166, "endOffset": 18220, "count": 0}, {"startOffset": 18276, "endOffset": 18329, "count": 0}, {"startOffset": 18389, "endOffset": 18443, "count": 0}, {"startOffset": 18545, "endOffset": 18558, "count": 4}, {"startOffset": 18584, "endOffset": 18823, "count": 2}, {"startOffset": 18823, "endOffset": 19000, "count": 3}, {"startOffset": 19000, "endOffset": 19697, "count": 2}, {"startOffset": 19697, "endOffset": 19765, "count": 1}, {"startOffset": 19765, "endOffset": 20009, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18868, "endOffset": 18901, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19034, "endOffset": 19683, "count": 4}, {"startOffset": 19084, "endOffset": 19103, "count": 2}, {"startOffset": 19105, "endOffset": 19669, "count": 2}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 20067, "endOffset": 20145, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 20244, "endOffset": 21413, "count": 2}, {"startOffset": 20520, "endOffset": 20796, "count": 1}, {"startOffset": 20768, "endOffset": 20780, "count": 0}, {"startOffset": 20848, "endOffset": 20860, "count": 0}, {"startOffset": 21009, "endOffset": 21014, "count": 1}, {"startOffset": 21100, "endOffset": 21105, "count": 0}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 21512, "endOffset": 24069, "count": 2}, {"startOffset": 21600, "endOffset": 21605, "count": 0}, {"startOffset": 22010, "endOffset": 22320, "count": 1}, {"startOffset": 22292, "endOffset": 22304, "count": 0}, {"startOffset": 22372, "endOffset": 22384, "count": 0}, {"startOffset": 23082, "endOffset": 23095, "count": 1}, {"startOffset": 23097, "endOffset": 23161, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23571, "endOffset": 24055, "count": 2}, {"startOffset": 23686, "endOffset": 24043, "count": 0}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 24216, "endOffset": 28147, "count": 0}], "isBlockCoverage": false}, {"functionName": "completion", "ranges": [{"startOffset": 28169, "endOffset": 28234, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 28352, "endOffset": 29076, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28989, "endOffset": 29059, "count": 2}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 29245, "endOffset": 31003, "count": 1}, {"startOffset": 29349, "endOffset": 29621, "count": 0}, {"startOffset": 30410, "endOffset": 30654, "count": 0}, {"startOffset": 30667, "endOffset": 30949, "count": 0}], "isBlockCoverage": true}, {"functionName": "completion", "ranges": [{"startOffset": 31282, "endOffset": 31768, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 31786, "endOffset": 35235, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35839, "endOffset": 37340, "count": 30}, {"startOffset": 35901, "endOffset": 35932, "count": 0}, {"startOffset": 36008, "endOffset": 36035, "count": 0}, {"startOffset": 36037, "endOffset": 36068, "count": 0}, {"startOffset": 36147, "endOffset": 36169, "count": 29}, {"startOffset": 36171, "endOffset": 36314, "count": 28}, {"startOffset": 36811, "endOffset": 37136, "count": 316}, {"startOffset": 36850, "endOffset": 36880, "count": 288}, {"startOffset": 36882, "endOffset": 37128, "count": 29}, {"startOffset": 37011, "endOffset": 37046, "count": 1}, {"startOffset": 37048, "endOffset": 37094, "count": 1}, {"startOffset": 37094, "endOffset": 37128, "count": 28}, {"startOffset": 37136, "endOffset": 37339, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36384, "endOffset": 36725, "count": 30}, {"startOffset": 36429, "endOffset": 36465, "count": 0}, {"startOffset": 36561, "endOffset": 36605, "count": 9}, {"startOffset": 36637, "endOffset": 36691, "count": 3}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1328", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useStateAndRef.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3416, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3416, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 33}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStateAndRef", "ranges": [{"startOffset": 596, "endOffset": 1159, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 834, "endOffset": 1110, "count": 2}, {"startOffset": 932, "endOffset": 993, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1329", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 331, "count": 1}, {"startOffset": 321, "endOffset": 329, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 387, "endOffset": 468, "count": 1}, {"startOffset": 458, "endOffset": 466, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 528, "endOffset": 613, "count": 1}, {"startOffset": 603, "endOffset": 611, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1330", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 1}, {"startOffset": 302, "endOffset": 310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1331", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 516, "endOffset": 568, "count": 1}, {"startOffset": 558, "endOffset": 566, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5304, "endOffset": 5451, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5519, "endOffset": 5626, "count": 8}], "isBlockCoverage": true}, {"functionName": "MCPServerConfig", "ranges": [{"startOffset": 5632, "endOffset": 6190, "count": 8}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6213, "endOffset": 6887, "count": 0}], "isBlockCoverage": true}, {"functionName": "Config", "ranges": [{"startOffset": 6893, "endOffset": 9555, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshAuth", "ranges": [{"startOffset": 9560, "endOffset": 10741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 10746, "endOffset": 10799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGeneratorConfig", "ranges": [{"startOffset": 10804, "endOffset": 10883, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModel", "ranges": [{"startOffset": 10888, "endOffset": 10971, "count": 0}], "isBlockCoverage": false}, {"functionName": "setModel", "ranges": [{"startOffset": 10976, "endOffset": 11165, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModelSwitchedDuringSession", "ranges": [{"startOffset": 11170, "endOffset": 11256, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetModelToDefault", "ranges": [{"startOffset": 11261, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFlashFallbackHandler", "ranges": [{"startOffset": 11500, "endOffset": 11585, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEmbeddingModel", "ranges": [{"startOffset": 11590, "endOffset": 11653, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandbox", "ranges": [{"startOffset": 11658, "endOffset": 11707, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 11712, "endOffset": 11765, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 11770, "endOffset": 11825, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolRegistry", "ranges": [{"startOffset": 11830, "endOffset": 11906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 11911, "endOffset": 11964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestion", "ranges": [{"startOffset": 11969, "endOffset": 12020, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFullContext", "ranges": [{"startOffset": 12025, "endOffset": 12082, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreTools", "ranges": [{"startOffset": 12087, "endOffset": 12140, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExcludeTools", "ranges": [{"startOffset": 12145, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolDiscoveryCommand", "ranges": [{"startOffset": 12209, "endOffset": 12284, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolCallCommand", "ranges": [{"startOffset": 12289, "endOffset": 12354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServerCommand", "ranges": [{"startOffset": 12359, "endOffset": 12426, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServers", "ranges": [{"startOffset": 12431, "endOffset": 12486, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserMemory", "ranges": [{"startOffset": 12491, "endOffset": 12546, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUserMemory", "ranges": [{"startOffset": 12551, "endOffset": 12628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFileCount", "ranges": [{"startOffset": 12633, "endOffset": 12700, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFileCount", "ranges": [{"startOffset": 12705, "endOffset": 12778, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApprovalMode", "ranges": [{"startOffset": 12783, "endOffset": 12842, "count": 0}], "isBlockCoverage": false}, {"functionName": "setApprovalMode", "ranges": [{"startOffset": 12847, "endOffset": 12910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowMemoryUsage", "ranges": [{"startOffset": 12915, "endOffset": 12980, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessibility", "ranges": [{"startOffset": 12985, "endOffset": 13046, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryEnabled", "ranges": [{"startOffset": 13051, "endOffset": 13136, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryLogPromptsEnabled", "ranges": [{"startOffset": 13141, "endOffset": 13238, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryOtlpEndpoint", "ranges": [{"startOffset": 13243, "endOffset": 13377, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryTarget", "ranges": [{"startOffset": 13382, "endOffset": 13507, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 13512, "endOffset": 13569, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienDir", "ranges": [{"startOffset": 13574, "endOffset": 13699, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 13704, "endOffset": 13815, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnableRecursiveFileSearch", "ranges": [{"startOffset": 13820, "endOffset": 13919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 13924, "endOffset": 14018, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCheckpointingEnabled", "ranges": [{"startOffset": 14023, "endOffset": 14091, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProxy", "ranges": [{"startOffset": 14096, "endOffset": 14141, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWorkingDir", "ranges": [{"startOffset": 14146, "endOffset": 14194, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBugCommand", "ranges": [{"startOffset": 14199, "endOffset": 14254, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileService", "ranges": [{"startOffset": 14259, "endOffset": 14483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 14488, "endOffset": 14567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExtensionContextFilePaths", "ranges": [{"startOffset": 14572, "endOffset": 14657, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGitService", "ranges": [{"startOffset": 14662, "endOffset": 14899, "count": 0}], "isBlockCoverage": false}, {"functionName": "createToolRegistry", "ranges": [{"startOffset": 14903, "endOffset": 16829, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1332", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 1}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1472, "endOffset": 1648, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1680, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3230, "endOffset": 4131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1512", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1247, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1513", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 1}, {"startOffset": 386, "endOffset": 394, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3046, "endOffset": 3754, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 3756, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 6146, "endOffset": 6761, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 6763, "endOffset": 7401, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 7402, "endOffset": 7695, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 7696, "endOffset": 7846, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 7847, "endOffset": 7999, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1516", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 949, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1218, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2462, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1517", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1518", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 1}, {"startOffset": 412, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1248, "endOffset": 1284, "count": 0}], "isBlockCoverage": true}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1290, "endOffset": 1446, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1451, "endOffset": 1857, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1862, "endOffset": 2135, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 2140, "endOffset": 2230, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2235, "endOffset": 2331, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2336, "endOffset": 2455, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2460, "endOffset": 2588, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2593, "endOffset": 2807, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2812, "endOffset": 2867, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2872, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3344, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3770, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1523", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 959, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1138, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1238, "endOffset": 1406, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1408, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1784, "endOffset": 2250, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2251, "endOffset": 2484, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2485, "endOffset": 2605, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2606, "endOffset": 3139, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3140, "endOffset": 3197, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3198, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3340, "endOffset": 4349, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1524", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 2}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 1}, {"startOffset": 537, "endOffset": 545, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1525", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1049, "endOffset": 2840, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1526", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1433, "endOffset": 1487, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1493, "endOffset": 2732, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2737, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4882, "endOffset": 4933, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4939, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 5129, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 5575, "endOffset": 7177, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 7450, "endOffset": 7640, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 7734, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 7896, "endOffset": 8158, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 8225, "endOffset": 8283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1527", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 1}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 577, "endOffset": 679, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 1168, "endOffset": 1538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1647, "endOffset": 1813, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2269, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2753, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 3048, "endOffset": 3278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1528", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 1}, {"startOffset": 503, "endOffset": 511, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 1}, {"startOffset": 620, "endOffset": 628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 1}, {"startOffset": 743, "endOffset": 751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 1}, {"startOffset": 844, "endOffset": 852, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 1}, {"startOffset": 955, "endOffset": 963, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 1}, {"startOffset": 1060, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 1}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 1}, {"startOffset": 1258, "endOffset": 1266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3516, "endOffset": 3853, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4220, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4372, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4609, "endOffset": 4842, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4894, "endOffset": 5025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 5066, "endOffset": 5151, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5200, "endOffset": 5265, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5267, "endOffset": 7941, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 7943, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 20315, "endOffset": 20843, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1621", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 712, "endOffset": 827, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 833, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 867, "endOffset": 1405, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1410, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2753, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4273, "endOffset": 5686, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1622", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 1}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1373, "endOffset": 1398, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1404, "endOffset": 1434, "count": 1}], "isBlockCoverage": true}, {"functionName": "LSTool", "ranges": [{"startOffset": 1621, "endOffset": 2848, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3041, "endOffset": 3622, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3799, "endOffset": 4357, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldIgnore", "ranges": [{"startOffset": 4600, "endOffset": 5161, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5347, "endOffset": 5547, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorResult", "ranges": [{"startOffset": 5598, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5982, "endOffset": 10207, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1623", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 690, "endOffset": 2131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1624", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 2}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 1}, {"startOffset": 368, "endOffset": 376, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 1}, {"startOffset": 455, "endOffset": 463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 1}, {"startOffset": 544, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 1}, {"startOffset": 629, "endOffset": 637, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 1}, {"startOffset": 718, "endOffset": 726, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 1}, {"startOffset": 811, "endOffset": 819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 1}, {"startOffset": 910, "endOffset": 918, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1544, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1907, "endOffset": 4318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4756, "endOffset": 5197, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5241, "endOffset": 5604, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5650, "endOffset": 5727, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5927, "endOffset": 6056, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6260, "endOffset": 6468, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1625", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1757, "endOffset": 1782, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1788, "endOffset": 1813, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1819, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3252, "endOffset": 4595, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4600, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4993, "endOffset": 6235, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1626", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1752, "endOffset": 1939, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2182, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3140, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4692, "endOffset": 6028, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6428, "endOffset": 11566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1631", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1747, "endOffset": 1891, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2136, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2242, "endOffset": 2413, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2415, "endOffset": 4312, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4314, "endOffset": 4828, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4830, "endOffset": 5085, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5087, "endOffset": 5603, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5605, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6151, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1727", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1728", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2536, "endOffset": 2549, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2555, "endOffset": 2590, "count": 1}], "isBlockCoverage": true}, {"functionName": "GrepTool", "ranges": [{"startOffset": 2799, "endOffset": 4210, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 4563, "endOffset": 5695, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5872, "endOffset": 6580, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6783, "endOffset": 9645, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 9926, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 11020, "endOffset": 12605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 12774, "endOffset": 13495, "count": 0}], "isBlockCoverage": false}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 13739, "endOffset": 22277, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1748", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 1}, {"startOffset": 665, "endOffset": 673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1059, "endOffset": 1144, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1146, "endOffset": 1362, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1488, "endOffset": 2306, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2308, "endOffset": 2566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1754", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1755", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1765, "endOffset": 2458, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2627, "endOffset": 2647, "count": 1}], "isBlockCoverage": true}, {"functionName": "GlobTool", "ranges": [{"startOffset": 2783, "endOffset": 4263, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 4338, "endOffset": 4932, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4999, "endOffset": 6375, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6445, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6975, "endOffset": 10877, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1756", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2525, "endOffset": 2548, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2554, "endOffset": 2591, "count": 0}], "isBlockCoverage": true}, {"functionName": "EditTool", "ranges": [{"startOffset": 2727, "endOffset": 6048, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 6257, "endOffset": 6690, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 6859, "endOffset": 7439, "count": 0}], "isBlockCoverage": false}, {"functionName": "_applyReplacement", "ranges": [{"startOffset": 7444, "endOffset": 8050, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateEdit", "ranges": [{"startOffset": 8345, "endOffset": 11949, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 12095, "endOffset": 13827, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 13832, "endOffset": 14832, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 15015, "endOffset": 17873, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureParentDirectoriesExist", "ranges": [{"startOffset": 17948, "endOffset": 18202, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 18207, "endOffset": 19573, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1758", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 2846, "endOffset": 7832, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 7834, "endOffset": 8426, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 8916, "endOffset": 11293, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 11899, "endOffset": 14728, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 15211, "endOffset": 17532, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 17964, "endOffset": 19887, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 19889, "endOffset": 20549, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 20627, "endOffset": 22935, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 22994, "endOffset": 23303, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 23305, "endOffset": 23427, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1759", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 399, "endOffset": 417, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1760", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1761", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2020, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2032, "endOffset": 2065, "count": 1}], "isBlockCoverage": true}, {"functionName": "ShellTool", "ranges": [{"startOffset": 2098, "endOffset": 4093, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4098, "endOffset": 4628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommandRoot", "ranges": [{"startOffset": 4633, "endOffset": 5152, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5157, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6114, "endOffset": 6982, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6987, "endOffset": 16021, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1764", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2893, "endOffset": 2943, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2905, "endOffset": 2931, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteFileTool", "ranges": [{"startOffset": 2949, "endOffset": 3800, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3805, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4316, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5811, "endOffset": 6184, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6268, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7961, "endOffset": 11962, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getCorrected<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 11967, "endOffset": 14133, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 14138, "endOffset": 15015, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1765", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1985, "endOffset": 2099, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2244, "endOffset": 2250, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2256, "endOffset": 2281, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebFetchTool", "ranges": [{"startOffset": 2287, "endOffset": 3153, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>back", "ranges": [{"startOffset": 3158, "endOffset": 5558, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 5563, "endOffset": 6212, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6217, "endOffset": 6469, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6474, "endOffset": 7711, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7716, "endOffset": 12476, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1766", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1767", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1150, "endOffset": 1154, "count": 0}], "isBlockCoverage": true}, {"functionName": "FetchError", "ranges": [{"startOffset": 1160, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1282, "endOffset": 1492, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1494, "endOffset": 2120, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1800", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3449, "endOffset": 3537, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3476, "endOffset": 3507, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 3789, "endOffset": 7980, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 7985, "endOffset": 9539, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 9544, "endOffset": 11151, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 11156, "endOffset": 19498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1801", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 1}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 1}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 1}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 2}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 1}, {"startOffset": 838, "endOffset": 846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 1}, {"startOffset": 923, "endOffset": 931, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3737, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 4068, "endOffset": 4241, "count": 1}, {"startOffset": 4154, "endOffset": 4203, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 4243, "endOffset": 4412, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4414, "endOffset": 4549, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4621, "endOffset": 4942, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5032, "endOffset": 5071, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5077, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5213, "endOffset": 7631, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7636, "endOffset": 9167, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1802", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1399, "endOffset": 1405, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1411, "endOffset": 1444, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebSearchTool", "ranges": [{"startOffset": 1450, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 2014, "endOffset": 2427, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2432, "endOffset": 2521, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2526, "endOffset": 6272, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1803", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 3118, "endOffset": 3236, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3261, "endOffset": 3429, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienClient", "ranges": [{"startOffset": 3435, "endOffset": 3752, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3757, "endOffset": 3962, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGenerator", "ranges": [{"startOffset": 3967, "endOffset": 4149, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 4154, "endOffset": 4231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChat", "ranges": [{"startOffset": 4236, "endOffset": 4369, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 4374, "endOffset": 4444, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 4449, "endOffset": 4526, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetChat", "ranges": [{"startOffset": 4531, "endOffset": 4625, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnvironment", "ranges": [{"startOffset": 4630, "endOffset": 6989, "count": 0}], "isBlockCoverage": false}, {"functionName": "startChat", "ranges": [{"startOffset": 6994, "endOffset": 8646, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 8651, "endOffset": 9822, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJson", "ranges": [{"startOffset": 9827, "endOffset": 12489, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 12494, "endOffset": 14052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateEmbedding", "ranges": [{"startOffset": 14057, "endOffset": 15157, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryCompressChat", "ranges": [{"startOffset": 15162, "endOffset": 17765, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 17957, "endOffset": 19078, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1804", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 995, "endOffset": 6814, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7082, "endOffset": 9356, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9777, "endOffset": 12254, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1805", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 1}, {"startOffset": 362, "endOffset": 370, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1677, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1805, "endOffset": 1881, "count": 0}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1887, "endOffset": 2008, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 2083, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 5910, "endOffset": 6524, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 6529, "endOffset": 6592, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 6597, "endOffset": 6662, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1806", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1807", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2803, "endOffset": 31135, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1808", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3232, "endOffset": 6198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1809", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 398, "endOffset": 569, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1810", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3468, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3779, "endOffset": 4875, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5082, "endOffset": 5277, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienChat", "ranges": [{"startOffset": 5283, "endOffset": 5561, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getRequestTextFromContents", "ranges": [{"startOffset": 5566, "endOffset": 5780, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiRequest", "ranges": [{"startOffset": 5785, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiResponse", "ranges": [{"startOffset": 6032, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiError", "ranges": [{"startOffset": 6279, "endOffset": 6640, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 6832, "endOffset": 7915, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessage", "ranges": [{"startOffset": 8567, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 12194, "endOffset": 14714, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 15750, "endOffset": 16039, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHistory", "ranges": [{"startOffset": 16092, "endOffset": 16141, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 16273, "endOffset": 16336, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 16341, "endOffset": 16400, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFinalUsageMetadata", "ranges": [{"startOffset": 16405, "endOffset": 16635, "count": 0}], "isBlockCoverage": false}, {"functionName": "processStreamResponse", "ranges": [{"startOffset": 16640, "endOffset": 18155, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordHistory", "ranges": [{"startOffset": 18160, "endOffset": 21626, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTextContent", "ranges": [{"startOffset": 21631, "endOffset": 21899, "count": 0}], "isBlockCoverage": false}, {"functionName": "isT<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 21904, "endOffset": 22184, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1811", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1000, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1711, "endOffset": 1795, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2103, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 5455, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 6244, "endOffset": 7452, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 7671, "endOffset": 7930, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 8175, "endOffset": 9298, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1812", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 1}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 1}, {"startOffset": 568, "endOffset": 576, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 1}, {"startOffset": 655, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 1}, {"startOffset": 748, "endOffset": 756, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2375, "endOffset": 2425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2427, "endOffset": 2532, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2533, "endOffset": 3720, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3722, "endOffset": 4490, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4492, "endOffset": 5628, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5630, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6272, "endOffset": 7445, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7447, "endOffset": 9239, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1847", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 4345, "endOffset": 4418, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 4420, "endOffset": 5097, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 5098, "endOffset": 7537, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7539, "endOffset": 7968, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2341", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1870, "endOffset": 1885, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1891, "endOffset": 2083, "count": 0}], "isBlockCoverage": true}, {"functionName": "ClearcutLogger", "ranges": [{"startOffset": 2139, "endOffset": 2196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstance", "ranges": [{"startOffset": 2208, "endOffset": 2496, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueueLogEvent", "ranges": [{"startOffset": 2601, "endOffset": 2804, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLogEvent", "ranges": [{"startOffset": 2809, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushIfNeeded", "ranges": [{"startOffset": 3091, "endOffset": 3251, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushToClearcut", "ranges": [{"startOffset": 3256, "endOffset": 5307, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeLogResponse", "ranges": [{"startOffset": 5396, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "logStartSessionEvent", "ranges": [{"startOffset": 6718, "endOffset": 9465, "count": 0}], "isBlockCoverage": false}, {"functionName": "logNewPromptEvent", "ranges": [{"startOffset": 9470, "endOffset": 9837, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCallEvent", "ranges": [{"startOffset": 9842, "endOffset": 11128, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequestEvent", "ranges": [{"startOffset": 11133, "endOffset": 11493, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponseEvent", "ranges": [{"startOffset": 11498, "endOffset": 13468, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiErrorEvent", "ranges": [{"startOffset": 13473, "endOffset": 14389, "count": 0}], "isBlockCoverage": false}, {"functionName": "logEndSessionEvent", "ranges": [{"startOffset": 14394, "endOffset": 14804, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdown", "ranges": [{"startOffset": 14809, "endOffset": 14945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2342", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 1}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 1}, {"startOffset": 691, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 1}, {"startOffset": 877, "endOffset": 885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 1}, {"startOffset": 968, "endOffset": 976, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 1}, {"startOffset": 1065, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1762, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1810, "endOffset": 2480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2512, "endOffset": 2837, "count": 0}], "isBlockCoverage": true}, {"functionName": "StartSessionEvent", "ranges": [{"startOffset": 2843, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4225, "endOffset": 4288, "count": 0}], "isBlockCoverage": true}, {"functionName": "EndSessionEvent", "ranges": [{"startOffset": 4294, "endOffset": 4475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4507, "endOffset": 4585, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserPromptEvent", "ranges": [{"startOffset": 4591, "endOffset": 4811, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4841, "endOffset": 4997, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolCallEvent", "ranges": [{"startOffset": 5003, "endOffset": 5542, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5574, "endOffset": 5650, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiRequestEvent", "ranges": [{"startOffset": 5656, "endOffset": 5870, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5900, "endOffset": 6019, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiErrorEvent", "ranges": [{"startOffset": 6025, "endOffset": 6372, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6405, "endOffset": 6654, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiResponseEvent", "ranges": [{"startOffset": 6660, "endOffset": 7382, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2343", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8055, "endOffset": 8354, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2344", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6096, "count": 1}, {"startOffset": 1116, "endOffset": 1121, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureArienDirExists", "ranges": [{"startOffset": 1282, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "readUserIdFromFile", "ranges": [{"startOffset": 1451, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeUserIdToFile", "ranges": [{"startOffset": 1683, "endOffset": 1791, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPersistentUserId", "ranges": [{"startOffset": 1967, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2345", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 1}, {"startOffset": 384, "endOffset": 392, "count": 0}], "isBlockCoverage": true}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2483", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 992, "endOffset": 1061, "count": 0}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 1067, "endOffset": 1832, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1915, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 2463, "endOffset": 2630, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2706, "endOffset": 2879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2949, "endOffset": 3041, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2484", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1010, "endOffset": 1086, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1092, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 1200, "endOffset": 1618, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1623, "endOffset": 2177, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 2182, "endOffset": 2283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 2288, "endOffset": 2816, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2821, "endOffset": 2872, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2486", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1719, "endOffset": 1730, "count": 30}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1736, "endOffset": 1839, "count": 30}], "isBlockCoverage": true}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1844, "endOffset": 2078, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 2083, "endOffset": 2483, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 2488, "endOffset": 2795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2936, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 4541, "endOffset": 4951, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 4956, "endOffset": 5098, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 5103, "endOffset": 5313, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 5318, "endOffset": 5597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2493", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 1}, {"startOffset": 511, "endOffset": 519, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 1}, {"startOffset": 636, "endOffset": 644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 1}, {"startOffset": 757, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 1}, {"startOffset": 894, "endOffset": 902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 1}, {"startOffset": 1019, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 1}, {"startOffset": 1132, "endOffset": 1140, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 1}, {"startOffset": 1354, "endOffset": 1362, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 1}, {"startOffset": 1463, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 1}, {"startOffset": 1578, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 1}, {"startOffset": 1699, "endOffset": 1707, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 1}, {"startOffset": 1816, "endOffset": 1824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 1}, {"startOffset": 1933, "endOffset": 1941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 1}, {"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 1}, {"startOffset": 2276, "endOffset": 2284, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 1}, {"startOffset": 2395, "endOffset": 2403, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 1}, {"startOffset": 2510, "endOffset": 2518, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 1}, {"startOffset": 2615, "endOffset": 2623, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 1}, {"startOffset": 2738, "endOffset": 2746, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4170, "endOffset": 4276, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2494", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1097, "endOffset": 1169, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1238, "endOffset": 1413, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1489, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1560, "endOffset": 2994, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2999, "endOffset": 3521, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3526, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4825, "endOffset": 7173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 7178, "endOffset": 7603, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 7608, "endOffset": 8759, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 8764, "endOffset": 9090, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 9095, "endOffset": 9609, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 9614, "endOffset": 10632, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 10637, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2495", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2496", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1290, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1501, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3277, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3586, "endOffset": 3748, "count": 0}], "isBlockCoverage": true}, {"functionName": "CoreToolScheduler", "ranges": [{"startOffset": 3754, "endOffset": 4222, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatusInternal", "ranges": [{"startOffset": 4227, "endOffset": 8661, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArgsInternal", "ranges": [{"startOffset": 8666, "endOffset": 8979, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRunning", "ranges": [{"startOffset": 8984, "endOffset": 9117, "count": 0}], "isBlockCoverage": false}, {"functionName": "schedule", "ranges": [{"startOffset": 9122, "endOffset": 11771, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmationResponse", "ranges": [{"startOffset": 11776, "endOffset": 13799, "count": 0}], "isBlockCoverage": false}, {"functionName": "attemptExecutionOfScheduledCalls", "ranges": [{"startOffset": 13804, "endOffset": 16328, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAndNotifyCompletion", "ranges": [{"startOffset": 16333, "endOffset": 17051, "count": 0}], "isBlockCoverage": false}, {"functionName": "notifyToolCallsUpdate", "ranges": [{"startOffset": 17056, "endOffset": 17192, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2497", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1626, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1702, "endOffset": 2744, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2745, "endOffset": 3774, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3775, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4335, "endOffset": 5000, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2498", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 1}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 1}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1159, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1160, "endOffset": 1380, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1715, "endOffset": 1935, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1937, "endOffset": 2168, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2327, "endOffset": 2530, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2587, "endOffset": 4512, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4725, "endOffset": 6603, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2499", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 933, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2500", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 1}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1420, "endOffset": 1484, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1559, "endOffset": 1621, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1697, "endOffset": 1761, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1813, "endOffset": 2906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2907, "endOffset": 6602, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6603, "endOffset": 7464, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7465, "endOffset": 8104, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8105, "endOffset": 9189, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2501", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2502", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2503", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 153}, {"startOffset": 562, "endOffset": 570, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2504", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/generated/git-commit.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 4}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2505", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 2}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 3}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 2}, {"startOffset": 598, "endOffset": 649, "count": 0}, {"startOffset": 745, "endOffset": 779, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1565, "count": 3}, {"startOffset": 850, "endOffset": 872, "count": 0}, {"startOffset": 900, "endOffset": 937, "count": 0}, {"startOffset": 1007, "endOffset": 1054, "count": 0}, {"startOffset": 1423, "endOffset": 1536, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2506", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/built-in-mcp-servers.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 366, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 475, "endOffset": 529, "count": 5}, {"startOffset": 519, "endOffset": 527, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 584, "endOffset": 642, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 698, "endOffset": 757, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 813, "endOffset": 872, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBuiltInMcpServers", "ranges": [{"startOffset": 1290, "endOffset": 1923, "count": 5}, {"startOffset": 1544, "endOffset": 1752, "count": 0}, {"startOffset": 1757, "endOffset": 1921, "count": 0}], "isBlockCoverage": true}, {"functionName": "getBuiltInMcpServersSync", "ranges": [{"startOffset": 1925, "endOffset": 2002, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConfigurableMcpServers", "ranges": [{"startOffset": 2004, "endOffset": 2086, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExperimentalMcpServers", "ranges": [{"startOffset": 2088, "endOffset": 2203, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2507", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/mcp-server-registry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 59147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 59147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 7}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 755, "endOffset": 6220, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVerifiedServers", "ranges": [{"startOffset": 6313, "endOffset": 6498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6410, "endOffset": 6472, "count": 6}], "isBlockCoverage": true}, {"functionName": "getExperimentalServers", "ranges": [{"startOffset": 6577, "endOffset": 6770, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConfigurableServers", "ranges": [{"startOffset": 6854, "endOffset": 7047, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6959, "endOffset": 7021, "count": 1}], "isBlockCoverage": true}, {"functionName": "getServerEntry", "ranges": [{"startOffset": 7117, "endOffset": 7253, "count": 5}, {"startOffset": 7179, "endOffset": 7213, "count": 0}, {"startOffset": 7214, "endOffset": 7248, "count": 0}], "isBlockCoverage": true}, {"functionName": "getServersByCategory", "ranges": [{"startOffset": 7309, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "getServersByTags", "ranges": [{"startOffset": 7660, "endOffset": 7969, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkServerRequirements", "ranges": [{"startOffset": 8041, "endOffset": 9254, "count": 5}, {"startOffset": 8152, "endOffset": 8245, "count": 0}, {"startOffset": 8723, "endOffset": 8821, "count": 0}, {"startOffset": 8829, "endOffset": 8883, "count": 0}, {"startOffset": 8885, "endOffset": 9253, "count": 0}], "isBlockCoverage": true}, {"functionName": "getInstallationInstructions", "ranges": [{"startOffset": 9325, "endOffset": 10553, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFallbackConfiguration", "ranges": [{"startOffset": 10653, "endOffset": 14401, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkCommandAvailability", "ranges": [{"startOffset": 14475, "endOffset": 15016, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCompatibleServers", "ranges": [{"startOffset": 15102, "endOffset": 18457, "count": 5}, {"startOffset": 15277, "endOffset": 15398, "count": 0}, {"startOffset": 15581, "endOffset": 15750, "count": 0}, {"startOffset": 15789, "endOffset": 16056, "count": 0}, {"startOffset": 16128, "endOffset": 18269, "count": 0}, {"startOffset": 18278, "endOffset": 18416, "count": 0}, {"startOffset": 18422, "endOffset": 18456, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16942, "endOffset": 16974, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16980, "endOffset": 17016, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18179, "endOffset": 18231, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2508", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/dependency-installer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 840, "endOffset": 2461, "count": 1}], "isBlockCoverage": true}, {"functionName": "installDependency", "ranges": [{"startOffset": 2544, "endOffset": 5162, "count": 0}], "isBlockCoverage": false}, {"functionName": "canAutoInstall", "ranges": [{"startOffset": 5243, "endOffset": 5433, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationInstructions", "ranges": [{"startOffset": 5508, "endOffset": 5763, "count": 0}], "isBlockCoverage": false}, {"functionName": "installMultipleDependencies", "ranges": [{"startOffset": 5832, "endOffset": 6702, "count": 0}], "isBlockCoverage": false}, {"functionName": "summarizeInstallationResults", "ranges": [{"startOffset": 6772, "endOffset": 7498, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortOptionsByPlatform", "ranges": [{"startOffset": 7575, "endOffset": 8242, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWindowsScore", "ranges": [{"startOffset": 8252, "endOffset": 8555, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMacScore", "ranges": [{"startOffset": 8565, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLinuxScore", "ranges": [{"startOffset": 8872, "endOffset": 9171, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdated<PERSON>ath", "ranges": [{"startOffset": 9265, "endOffset": 9970, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}