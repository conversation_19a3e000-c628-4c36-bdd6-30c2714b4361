{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8786, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8786, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 697, "endOffset": 3616, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 763, "endOffset": 1348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 835, "endOffset": 956, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1023, "endOffset": 1147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1214, "endOffset": 1342, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 3612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1679, "endOffset": 1788, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1868, "endOffset": 1981, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2079, "endOffset": 2195, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2275, "endOffset": 2387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2474, "endOffset": 2590, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2668, "endOffset": 2780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2865, "endOffset": 2982, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3077, "endOffset": 3197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3266, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3494, "endOffset": 3606, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1028", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 3}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 11}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 3}, {"startOffset": 598, "endOffset": 649, "count": 1}, {"startOffset": 649, "endOffset": 685, "count": 2}, {"startOffset": 685, "endOffset": 779, "count": 1}], "isBlockCoverage": true}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1565, "count": 11}, {"startOffset": 850, "endOffset": 872, "count": 2}, {"startOffset": 872, "endOffset": 900, "count": 9}, {"startOffset": 900, "endOffset": 937, "count": 1}, {"startOffset": 937, "endOffset": 1007, "count": 8}, {"startOffset": 1007, "endOffset": 1054, "count": 2}, {"startOffset": 1054, "endOffset": 1247, "count": 6}, {"startOffset": 1247, "endOffset": 1281, "count": 4}, {"startOffset": 1281, "endOffset": 1302, "count": 6}, {"startOffset": 1302, "endOffset": 1338, "count": 4}, {"startOffset": 1338, "endOffset": 1359, "count": 6}, {"startOffset": 1359, "endOffset": 1395, "count": 4}, {"startOffset": 1395, "endOffset": 1423, "count": 6}, {"startOffset": 1423, "endOffset": 1536, "count": 0}, {"startOffset": 1536, "endOffset": 1564, "count": 6}], "isBlockCoverage": true}], "startOffset": 209}]}