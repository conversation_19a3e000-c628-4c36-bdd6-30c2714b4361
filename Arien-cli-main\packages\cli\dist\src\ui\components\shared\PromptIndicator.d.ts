/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
interface PromptIndicatorProps {
    type?: 'default' | 'shell' | 'completion' | 'waiting';
    animated?: boolean;
    color?: string;
}
export declare const PromptIndicator: React.FC<PromptIndicatorProps>;
interface TypewriterTextProps {
    text: string;
    speed?: number;
    color?: string;
    onComplete?: () => void;
}
export declare const TypewriterText: React.FC<TypewriterTextProps>;
export {};
