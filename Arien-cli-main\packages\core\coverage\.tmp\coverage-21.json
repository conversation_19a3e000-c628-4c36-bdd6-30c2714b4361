{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editCorrector.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 90880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 90880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 391, "endOffset": 716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 464, "endOffset": 712, "count": 19}], "isBlockCoverage": true}, {"functionName": "generateJson", "ranges": [{"startOffset": 508, "endOffset": 550, "count": 15}], "isBlockCoverage": true}, {"functionName": "startChat", "ranges": [{"startOffset": 573, "endOffset": 612, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 643, "endOffset": 690, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1298, "endOffset": 32310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1363, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1440, "endOffset": 1544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1619, "endOffset": 1725, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1806, "endOffset": 1913, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1995, "endOffset": 2102, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2194, "endOffset": 2401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2481, "endOffset": 2690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2789, "endOffset": 2901, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2965, "endOffset": 3172, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3247, "endOffset": 7463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3324, "endOffset": 3838, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3918, "endOffset": 4080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4162, "endOffset": 4415, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4506, "endOffset": 4656, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4756, "endOffset": 5031, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5159, "endOffset": 5485, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5567, "endOffset": 5676, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5774, "endOffset": 6041, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6140, "endOffset": 6294, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6395, "endOffset": 6584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6657, "endOffset": 7030, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7152, "endOffset": 7457, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7524, "endOffset": 25837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7714, "endOffset": 11011, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8413, "endOffset": 8438, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8487, "endOffset": 8511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8562, "endOffset": 8588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8641, "endOffset": 8669, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8725, "endOffset": 8747, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8800, "endOffset": 8828, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8880, "endOffset": 8907, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8962, "endOffset": 8992, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9045, "endOffset": 9073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9137, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9235, "endOffset": 9269, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9329, "endOffset": 9364, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9418, "endOffset": 9447, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9500, "endOffset": 9528, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9582, "endOffset": 9611, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9665, "endOffset": 9726, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9786, "endOffset": 9821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9881, "endOffset": 9952, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10041, "endOffset": 10094, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10181, "endOffset": 10268, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10406, "endOffset": 10728, "count": 11}, {"startOffset": 10478, "endOffset": 10544, "count": 0}, {"startOffset": 10651, "endOffset": 10678, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11132, "endOffset": 14983, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11267, "endOffset": 12143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12279, "endOffset": 13043, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13191, "endOffset": 14070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14208, "endOffset": 14975, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15156, "endOffset": 18051, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15314, "endOffset": 16173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16345, "endOffset": 17119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17267, "endOffset": 18043, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18134, "endOffset": 22614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18312, "endOffset": 19227, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19400, "endOffset": 20459, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20596, "endOffset": 21521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21709, "endOffset": 22606, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22711, "endOffset": 24477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22879, "endOffset": 23634, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23783, "endOffset": 24469, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24615, "endOffset": 25831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24751, "endOffset": 25823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25905, "endOffset": 32306, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26095, "endOffset": 29392, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26794, "endOffset": 26819, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26868, "endOffset": 26892, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26943, "endOffset": 26969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27022, "endOffset": 27050, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27106, "endOffset": 27128, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27181, "endOffset": 27209, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27261, "endOffset": 27288, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27343, "endOffset": 27373, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27426, "endOffset": 27454, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27518, "endOffset": 27557, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27616, "endOffset": 27650, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27710, "endOffset": 27745, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27799, "endOffset": 27828, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27881, "endOffset": 27909, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27963, "endOffset": 27992, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28046, "endOffset": 28107, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28167, "endOffset": 28202, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28262, "endOffset": 28333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28422, "endOffset": 28475, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28562, "endOffset": 28649, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28787, "endOffset": 29109, "count": 4}, {"startOffset": 28859, "endOffset": 28925, "count": 0}, {"startOffset": 29032, "endOffset": 29059, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29494, "endOffset": 29878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29983, "endOffset": 30512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30641, "endOffset": 31179, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31273, "endOffset": 31672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31771, "endOffset": 32300, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editCorrector.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 61494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 61494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 14}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 5}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 24}, {"startOffset": 953, "endOffset": 961, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 11}, {"startOffset": 1050, "endOffset": 1058, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 19}, {"startOffset": 1183, "endOffset": 1191, "count": 0}], "isBlockCoverage": true}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 1970, "endOffset": 6224, "count": 14}, {"startOffset": 2238, "endOffset": 2268, "count": 0}, {"startOffset": 2680, "endOffset": 2889, "count": 6}, {"startOffset": 2719, "endOffset": 2885, "count": 4}, {"startOffset": 2889, "endOffset": 5680, "count": 8}, {"startOffset": 2935, "endOffset": 3599, "count": 1}, {"startOffset": 3061, "endOffset": 3232, "count": 0}, {"startOffset": 3442, "endOffset": 3599, "count": 0}, {"startOffset": 3599, "endOffset": 5680, "count": 7}, {"startOffset": 3836, "endOffset": 4263, "count": 3}, {"startOffset": 3927, "endOffset": 4257, "count": 1}, {"startOffset": 4263, "endOffset": 5676, "count": 4}, {"startOffset": 4633, "endOffset": 5231, "count": 3}, {"startOffset": 4765, "endOffset": 5223, "count": 2}, {"startOffset": 5231, "endOffset": 5465, "count": 1}, {"startOffset": 5471, "endOffset": 5676, "count": 0}, {"startOffset": 5680, "endOffset": 6223, "count": 12}], "isBlockCoverage": true}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 6226, "endOffset": 6804, "count": 5}, {"startOffset": 6382, "endOffset": 6412, "count": 0}, {"startOffset": 6532, "endOffset": 6611, "count": 1}, {"startOffset": 6611, "endOffset": 6803, "count": 4}], "isBlockCoverage": true}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 7181, "endOffset": 9495, "count": 4}, {"startOffset": 9231, "endOffset": 9277, "count": 0}, {"startOffset": 9282, "endOffset": 9493, "count": 0}], "isBlockCoverage": true}, {"functionName": "correctNewString", "ranges": [{"startOffset": 9887, "endOffset": 12621, "count": 3}, {"startOffset": 10053, "endOffset": 10088, "count": 0}, {"startOffset": 12385, "endOffset": 12430, "count": 0}, {"startOffset": 12435, "endOffset": 12619, "count": 0}], "isBlockCoverage": true}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 13073, "endOffset": 15329, "count": 4}, {"startOffset": 15038, "endOffset": 15097, "count": 0}, {"startOffset": 15102, "endOffset": 15327, "count": 0}], "isBlockCoverage": true}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 15730, "endOffset": 17593, "count": 4}, {"startOffset": 17210, "endOffset": 17256, "count": 3}, {"startOffset": 17258, "endOffset": 17312, "count": 3}, {"startOffset": 17312, "endOffset": 17368, "count": 1}, {"startOffset": 17373, "endOffset": 17591, "count": 0}], "isBlockCoverage": true}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 17595, "endOffset": 18207, "count": 12}, {"startOffset": 17787, "endOffset": 18134, "count": 0}], "isBlockCoverage": true}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 18208, "endOffset": 19265, "count": 52}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18323, "endOffset": 19258, "count": 70}, {"startOffset": 18388, "endOffset": 18420, "count": 12}, {"startOffset": 18482, "endOffset": 18513, "count": 5}, {"startOffset": 18571, "endOffset": 18603, "count": 0}, {"startOffset": 18673, "endOffset": 18704, "count": 1}, {"startOffset": 18768, "endOffset": 18799, "count": 40}, {"startOffset": 18867, "endOffset": 18898, "count": 4}, {"startOffset": 18960, "endOffset": 18993, "count": 4}, {"startOffset": 19076, "endOffset": 19109, "count": 4}, {"startOffset": 19212, "endOffset": 19244, "count": 0}], "isBlockCoverage": true}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 19267, "endOffset": 19510, "count": 48}, {"startOffset": 19329, "endOffset": 19348, "count": 1}, {"startOffset": 19348, "endOffset": 19421, "count": 47}, {"startOffset": 19421, "endOffset": 19491, "count": 38}, {"startOffset": 19491, "endOffset": 19509, "count": 47}], "isBlockCoverage": true}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 19512, "endOffset": 19630, "count": 19}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "513", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/LruCache.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 397, "endOffset": 413, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 417, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 518, "endOffset": 677, "count": 19}, {"startOffset": 583, "endOffset": 654, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 680, "endOffset": 984, "count": 19}, {"startOffset": 727, "endOffset": 764, "count": 0}, {"startOffset": 807, "endOffset": 947, "count": 0}], "isBlockCoverage": true}, {"functionName": "clear", "ranges": [{"startOffset": 987, "endOffset": 1024, "count": 38}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "514", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/config/models.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "515", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tool-registry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1054, "endOffset": 2309, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2312, "endOffset": 3987, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4014, "endOffset": 4077, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4081, "endOffset": 4132, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 4255, "endOffset": 4465, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 4607, "endOffset": 5768, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 6027, "endOffset": 6193, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 6279, "endOffset": 6342, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 6427, "endOffset": 6651, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 6710, "endOffset": 6762, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "516", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "517", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2369, "endOffset": 2570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2637, "endOffset": 2848, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 3028, "endOffset": 3117, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 3119, "endOffset": 3298, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 3300, "endOffset": 3495, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 3496, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 3630, "endOffset": 3713, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 3715, "endOffset": 3778, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 3780, "endOffset": 5751, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 5753, "endOffset": 14983, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 14984, "endOffset": 15383, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "610", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 629, "endOffset": 1194, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1197, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1245, "endOffset": 2342, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2345, "endOffset": 2669, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 2673, "endOffset": 3597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}