import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
const statusConfig = {
    success: { icon: '✓', color: Colors.AccentGreen },
    error: { icon: '✗', color: Colors.AccentRed },
    warning: { icon: '⚠', color: Colors.AccentYellow },
    info: { icon: 'i', color: Colors.AccentCyan },
    pending: { icon: '◎', color: Colors.AccentPurple },
    custom: { icon: '●', color: Colors.Gray },
};
export const StatusBadge = ({ status, text, customColor, customIcon, compact = false, }) => {
    const config = statusConfig[status] || statusConfig.custom;
    const icon = customIcon || config.icon;
    const color = customColor || config.color;
    if (compact) {
        return (_jsxs(Text, { color: color, bold: true, children: [icon, " ", text] }));
    }
    return (_jsx(Box, { borderStyle: "round", borderColor: color, paddingX: 1, children: _jsxs(Text, { color: color, bold: true, children: [icon, " ", text] }) }));
};
export const StatusList = ({ items, title }) => {
    if (items.length === 0)
        return null;
    return (_jsxs(Box, { flexDirection: "column", marginTop: 1, children: [title && (_jsx(Text, { color: Colors.AccentCyan, bold: true, children: title })), items.map((item, index) => (_jsx(Box, { marginLeft: title ? 2 : 0, children: _jsx(StatusBadge, { ...item, compact: true }) }, index)))] }));
};
//# sourceMappingURL=StatusBadge.js.map