/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export interface DependencyInstallResult {
    success: boolean;
    command: string;
    message: string;
    error?: string;
}
export interface InstallationOption {
    command: string;
    description: string;
    installCommand: string;
    verifyCommand: string;
}
/**
 * Handles automatic installation of missing dependencies for MCP servers
 */
export declare class DependencyInstaller {
    private static readonly INSTALLATION_OPTIONS;
    /**
     * Attempts to automatically install a missing dependency
     */
    static installDependency(command: string, interactive?: boolean): Promise<DependencyInstallResult>;
    /**
     * Checks if a dependency can be automatically installed
     */
    static canAutoInstall(command: string): boolean;
    /**
     * Gets installation instructions for a dependency
     */
    static getInstallationInstructions(command: string): string[];
    /**
     * Attempts to install multiple dependencies
     */
    static installMultipleDependencies(commands: string[], interactive?: boolean): Promise<Record<string, DependencyInstallResult>>;
    /**
     * Provides a summary of installation results
     */
    static summarizeInstallationResults(results: Record<string, DependencyInstallResult>): void;
    /**
     * Sorts installation options by platform preference
     */
    private static sortOptionsByPlatform;
    private static getWindowsScore;
    private static getMacScore;
    private static getLinuxScore;
    /**
     * Gets an updated PATH that includes common installation directories
     */
    private static getUpdatedPath;
}
