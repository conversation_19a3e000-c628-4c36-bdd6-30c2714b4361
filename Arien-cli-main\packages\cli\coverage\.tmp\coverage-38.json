{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/nonInteractiveCli.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 497, "endOffset": 786, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1177, "endOffset": 10162, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1358, "endOffset": 2527, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1838, "endOffset": 1859, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1947, "endOffset": 1969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2343, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2468, "endOffset": 2485, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2568, "endOffset": 2627, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2707, "endOffset": 3640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2745, "endOffset": 2950, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3722, "endOffset": 5471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4418, "endOffset": 4491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4515, "endOffset": 4637, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5550, "endOffset": 7380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6277, "endOffset": 6350, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6374, "endOffset": 6531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6725, "endOffset": 6738, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7478, "endOffset": 7923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7705, "endOffset": 7718, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8036, "endOffset": 10158, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8870, "endOffset": 8943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8967, "endOffset": 9183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9377, "endOffset": 9390, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1028", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/nonInteractiveCli.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14419, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14419, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 5}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "getResponseText", "ranges": [{"startOffset": 772, "endOffset": 1257, "count": 8}, {"startOffset": 835, "endOffset": 868, "count": 5}, {"startOffset": 870, "endOffset": 1239, "count": 5}, {"startOffset": 1098, "endOffset": 1128, "count": 0}, {"startOffset": 1239, "endOffset": 1256, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1174, "endOffset": 1193, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1199, "endOffset": 1218, "count": 5}], "isBlockCoverage": true}, {"functionName": "runNonInteractive", "ranges": [{"startOffset": 1258, "endOffset": 4383, "count": 5}, {"startOffset": 1705, "endOffset": 4035, "count": 8}, {"startOffset": 1842, "endOffset": 1847, "count": 0}, {"startOffset": 2085, "endOffset": 2133, "count": 7}, {"startOffset": 2179, "endOffset": 2257, "count": 0}, {"startOffset": 2329, "endOffset": 2382, "count": 5}, {"startOffset": 2416, "endOffset": 2480, "count": 3}, {"startOffset": 2488, "endOffset": 2526, "count": 7}, {"startOffset": 2526, "endOffset": 3962, "count": 3}, {"startOffset": 2639, "endOffset": 2668, "count": 0}, {"startOffset": 2775, "endOffset": 2780, "count": 0}, {"startOffset": 3065, "endOffset": 3434, "count": 2}, {"startOffset": 3296, "endOffset": 3325, "count": 0}, {"startOffset": 3376, "endOffset": 3422, "count": 1}, {"startOffset": 3577, "endOffset": 3607, "count": 0}, {"startOffset": 3693, "endOffset": 3766, "count": 0}, {"startOffset": 3962, "endOffset": 4029, "count": 4}, {"startOffset": 4040, "endOffset": 4238, "count": 1}, {"startOffset": 4311, "endOffset": 4377, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1337, "endOffset": 1412, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1029", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 331, "count": 1}, {"startOffset": 321, "endOffset": 329, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 387, "endOffset": 468, "count": 1}, {"startOffset": 458, "endOffset": 466, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 528, "endOffset": 613, "count": 1}, {"startOffset": 603, "endOffset": 611, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1030", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 1}, {"startOffset": 302, "endOffset": 310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1031", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 516, "endOffset": 568, "count": 1}, {"startOffset": 558, "endOffset": 566, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5304, "endOffset": 5451, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5519, "endOffset": 5626, "count": 0}], "isBlockCoverage": true}, {"functionName": "MCPServerConfig", "ranges": [{"startOffset": 5632, "endOffset": 6190, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6213, "endOffset": 6887, "count": 0}], "isBlockCoverage": true}, {"functionName": "Config", "ranges": [{"startOffset": 6893, "endOffset": 9555, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshAuth", "ranges": [{"startOffset": 9560, "endOffset": 10741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 10746, "endOffset": 10799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGeneratorConfig", "ranges": [{"startOffset": 10804, "endOffset": 10883, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModel", "ranges": [{"startOffset": 10888, "endOffset": 10971, "count": 0}], "isBlockCoverage": false}, {"functionName": "setModel", "ranges": [{"startOffset": 10976, "endOffset": 11165, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModelSwitchedDuringSession", "ranges": [{"startOffset": 11170, "endOffset": 11256, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetModelToDefault", "ranges": [{"startOffset": 11261, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFlashFallbackHandler", "ranges": [{"startOffset": 11500, "endOffset": 11585, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEmbeddingModel", "ranges": [{"startOffset": 11590, "endOffset": 11653, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandbox", "ranges": [{"startOffset": 11658, "endOffset": 11707, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 11712, "endOffset": 11765, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 11770, "endOffset": 11825, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolRegistry", "ranges": [{"startOffset": 11830, "endOffset": 11906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 11911, "endOffset": 11964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestion", "ranges": [{"startOffset": 11969, "endOffset": 12020, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFullContext", "ranges": [{"startOffset": 12025, "endOffset": 12082, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreTools", "ranges": [{"startOffset": 12087, "endOffset": 12140, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExcludeTools", "ranges": [{"startOffset": 12145, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolDiscoveryCommand", "ranges": [{"startOffset": 12209, "endOffset": 12284, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolCallCommand", "ranges": [{"startOffset": 12289, "endOffset": 12354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServerCommand", "ranges": [{"startOffset": 12359, "endOffset": 12426, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServers", "ranges": [{"startOffset": 12431, "endOffset": 12486, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserMemory", "ranges": [{"startOffset": 12491, "endOffset": 12546, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUserMemory", "ranges": [{"startOffset": 12551, "endOffset": 12628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFileCount", "ranges": [{"startOffset": 12633, "endOffset": 12700, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFileCount", "ranges": [{"startOffset": 12705, "endOffset": 12778, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApprovalMode", "ranges": [{"startOffset": 12783, "endOffset": 12842, "count": 0}], "isBlockCoverage": false}, {"functionName": "setApprovalMode", "ranges": [{"startOffset": 12847, "endOffset": 12910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowMemoryUsage", "ranges": [{"startOffset": 12915, "endOffset": 12980, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessibility", "ranges": [{"startOffset": 12985, "endOffset": 13046, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryEnabled", "ranges": [{"startOffset": 13051, "endOffset": 13136, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryLogPromptsEnabled", "ranges": [{"startOffset": 13141, "endOffset": 13238, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryOtlpEndpoint", "ranges": [{"startOffset": 13243, "endOffset": 13377, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryTarget", "ranges": [{"startOffset": 13382, "endOffset": 13507, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 13512, "endOffset": 13569, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienDir", "ranges": [{"startOffset": 13574, "endOffset": 13699, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 13704, "endOffset": 13815, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnableRecursiveFileSearch", "ranges": [{"startOffset": 13820, "endOffset": 13919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 13924, "endOffset": 14018, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCheckpointingEnabled", "ranges": [{"startOffset": 14023, "endOffset": 14091, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProxy", "ranges": [{"startOffset": 14096, "endOffset": 14141, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWorkingDir", "ranges": [{"startOffset": 14146, "endOffset": 14194, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBugCommand", "ranges": [{"startOffset": 14199, "endOffset": 14254, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileService", "ranges": [{"startOffset": 14259, "endOffset": 14483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 14488, "endOffset": 14567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExtensionContextFilePaths", "ranges": [{"startOffset": 14572, "endOffset": 14657, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGitService", "ranges": [{"startOffset": 14662, "endOffset": 14899, "count": 0}], "isBlockCoverage": false}, {"functionName": "createToolRegistry", "ranges": [{"startOffset": 14903, "endOffset": 16829, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1035", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 1}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1472, "endOffset": 1648, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1680, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3230, "endOffset": 4131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1215", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1247, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1216", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 1}, {"startOffset": 386, "endOffset": 394, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3046, "endOffset": 3754, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 3756, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 6146, "endOffset": 6761, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 6763, "endOffset": 7401, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 7402, "endOffset": 7695, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 7696, "endOffset": 7846, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 7847, "endOffset": 7999, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1229", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 949, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1218, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2462, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1230", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1231", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 1}, {"startOffset": 412, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1248, "endOffset": 1284, "count": 0}], "isBlockCoverage": true}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1290, "endOffset": 1446, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1451, "endOffset": 1857, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1862, "endOffset": 2135, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 2140, "endOffset": 2230, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2235, "endOffset": 2331, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2336, "endOffset": 2455, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2460, "endOffset": 2588, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2593, "endOffset": 2807, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2812, "endOffset": 2867, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2872, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3344, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3770, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1236", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 959, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1138, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1238, "endOffset": 1406, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1408, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1784, "endOffset": 2250, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2251, "endOffset": 2484, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2485, "endOffset": 2605, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2606, "endOffset": 3139, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3140, "endOffset": 3197, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3198, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3340, "endOffset": 4349, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1237", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 2}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 1}, {"startOffset": 537, "endOffset": 545, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1238", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1049, "endOffset": 2840, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1239", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1433, "endOffset": 1487, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1493, "endOffset": 2732, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2737, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4882, "endOffset": 4933, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4939, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 5129, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 5575, "endOffset": 7177, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 7450, "endOffset": 7640, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 7734, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 7896, "endOffset": 8158, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 8225, "endOffset": 8283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1240", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 1}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 577, "endOffset": 679, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 1168, "endOffset": 1538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1647, "endOffset": 1813, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2269, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2753, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 3048, "endOffset": 3278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1241", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 1}, {"startOffset": 503, "endOffset": 511, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 1}, {"startOffset": 620, "endOffset": 628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 1}, {"startOffset": 743, "endOffset": 751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 1}, {"startOffset": 844, "endOffset": 852, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 1}, {"startOffset": 955, "endOffset": 963, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 1}, {"startOffset": 1060, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 1}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 1}, {"startOffset": 1258, "endOffset": 1266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3516, "endOffset": 3853, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4220, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4372, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4609, "endOffset": 4842, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4894, "endOffset": 5025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 5066, "endOffset": 5151, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5200, "endOffset": 5265, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5267, "endOffset": 7941, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 7943, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 20315, "endOffset": 20843, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1332", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 712, "endOffset": 827, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 833, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 867, "endOffset": 1405, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1410, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2753, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4273, "endOffset": 5686, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1333", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 1}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1373, "endOffset": 1398, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1404, "endOffset": 1434, "count": 1}], "isBlockCoverage": true}, {"functionName": "LSTool", "ranges": [{"startOffset": 1621, "endOffset": 2848, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3041, "endOffset": 3622, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3799, "endOffset": 4357, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldIgnore", "ranges": [{"startOffset": 4600, "endOffset": 5161, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5347, "endOffset": 5547, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorResult", "ranges": [{"startOffset": 5598, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5982, "endOffset": 10207, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1334", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 690, "endOffset": 2131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1335", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 2}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 1}, {"startOffset": 368, "endOffset": 376, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 1}, {"startOffset": 455, "endOffset": 463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 1}, {"startOffset": 544, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 1}, {"startOffset": 629, "endOffset": 637, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 1}, {"startOffset": 718, "endOffset": 726, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 1}, {"startOffset": 811, "endOffset": 819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 1}, {"startOffset": 910, "endOffset": 918, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1544, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1907, "endOffset": 4318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4756, "endOffset": 5197, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5241, "endOffset": 5604, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5650, "endOffset": 5727, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5927, "endOffset": 6056, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6260, "endOffset": 6468, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1336", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1757, "endOffset": 1782, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1788, "endOffset": 1813, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1819, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3252, "endOffset": 4595, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4600, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4993, "endOffset": 6235, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1337", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1752, "endOffset": 1939, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2182, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3140, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4692, "endOffset": 6028, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6428, "endOffset": 11566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1342", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1747, "endOffset": 1891, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2136, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2242, "endOffset": 2413, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2415, "endOffset": 4312, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4314, "endOffset": 4828, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4830, "endOffset": 5085, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5087, "endOffset": 5603, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5605, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6151, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1438", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1439", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2536, "endOffset": 2549, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2555, "endOffset": 2590, "count": 1}], "isBlockCoverage": true}, {"functionName": "GrepTool", "ranges": [{"startOffset": 2799, "endOffset": 4210, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 4563, "endOffset": 5695, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5872, "endOffset": 6580, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6783, "endOffset": 9645, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 9926, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 11020, "endOffset": 12605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 12774, "endOffset": 13495, "count": 0}], "isBlockCoverage": false}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 13739, "endOffset": 22277, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1459", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 1}, {"startOffset": 665, "endOffset": 673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1059, "endOffset": 1144, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1146, "endOffset": 1362, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1488, "endOffset": 2306, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2308, "endOffset": 2566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1465", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1466", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1765, "endOffset": 2458, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2627, "endOffset": 2647, "count": 1}], "isBlockCoverage": true}, {"functionName": "GlobTool", "ranges": [{"startOffset": 2783, "endOffset": 4263, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 4338, "endOffset": 4932, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4999, "endOffset": 6375, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6445, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6975, "endOffset": 10877, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1467", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2525, "endOffset": 2548, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2554, "endOffset": 2591, "count": 0}], "isBlockCoverage": true}, {"functionName": "EditTool", "ranges": [{"startOffset": 2727, "endOffset": 6048, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 6257, "endOffset": 6690, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 6859, "endOffset": 7439, "count": 0}], "isBlockCoverage": false}, {"functionName": "_applyReplacement", "ranges": [{"startOffset": 7444, "endOffset": 8050, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateEdit", "ranges": [{"startOffset": 8345, "endOffset": 11949, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 12095, "endOffset": 13827, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 13832, "endOffset": 14832, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 15015, "endOffset": 17873, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureParentDirectoriesExist", "ranges": [{"startOffset": 17948, "endOffset": 18202, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 18207, "endOffset": 19573, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1469", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 2846, "endOffset": 7832, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 7834, "endOffset": 8426, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 8916, "endOffset": 11293, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 11899, "endOffset": 14728, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 15211, "endOffset": 17532, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 17964, "endOffset": 19887, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 19889, "endOffset": 20549, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 20627, "endOffset": 22935, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 22994, "endOffset": 23303, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 23305, "endOffset": 23427, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1470", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 399, "endOffset": 417, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1471", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1472", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2020, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2032, "endOffset": 2065, "count": 1}], "isBlockCoverage": true}, {"functionName": "ShellTool", "ranges": [{"startOffset": 2098, "endOffset": 4093, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4098, "endOffset": 4628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommandRoot", "ranges": [{"startOffset": 4633, "endOffset": 5152, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5157, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6114, "endOffset": 6982, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6987, "endOffset": 16021, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1475", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2893, "endOffset": 2943, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2905, "endOffset": 2931, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteFileTool", "ranges": [{"startOffset": 2949, "endOffset": 3800, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3805, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4316, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5811, "endOffset": 6184, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6268, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7961, "endOffset": 11962, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getCorrected<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 11967, "endOffset": 14133, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 14138, "endOffset": 15015, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1476", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1985, "endOffset": 2099, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2244, "endOffset": 2250, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2256, "endOffset": 2281, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebFetchTool", "ranges": [{"startOffset": 2287, "endOffset": 3153, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>back", "ranges": [{"startOffset": 3158, "endOffset": 5558, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 5563, "endOffset": 6212, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6217, "endOffset": 6469, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6474, "endOffset": 7711, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7716, "endOffset": 12476, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1477", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1478", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1150, "endOffset": 1154, "count": 0}], "isBlockCoverage": true}, {"functionName": "FetchError", "ranges": [{"startOffset": 1160, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1282, "endOffset": 1492, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1494, "endOffset": 2120, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1511", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3449, "endOffset": 3537, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3476, "endOffset": 3507, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 3789, "endOffset": 7980, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 7985, "endOffset": 9539, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 9544, "endOffset": 11151, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 11156, "endOffset": 19498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1512", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 1}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 1}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 1}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 2}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 1}, {"startOffset": 838, "endOffset": 846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 1}, {"startOffset": 923, "endOffset": 931, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3737, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 4068, "endOffset": 4241, "count": 1}, {"startOffset": 4154, "endOffset": 4203, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 4243, "endOffset": 4412, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4414, "endOffset": 4549, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4621, "endOffset": 4942, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5032, "endOffset": 5071, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5077, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5213, "endOffset": 7631, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7636, "endOffset": 9167, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1513", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1399, "endOffset": 1405, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1411, "endOffset": 1444, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebSearchTool", "ranges": [{"startOffset": 1450, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 2014, "endOffset": 2427, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2432, "endOffset": 2521, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2526, "endOffset": 6272, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1514", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 3118, "endOffset": 3236, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3261, "endOffset": 3429, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienClient", "ranges": [{"startOffset": 3435, "endOffset": 3752, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3757, "endOffset": 3962, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGenerator", "ranges": [{"startOffset": 3967, "endOffset": 4149, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 4154, "endOffset": 4231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChat", "ranges": [{"startOffset": 4236, "endOffset": 4369, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 4374, "endOffset": 4444, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 4449, "endOffset": 4526, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetChat", "ranges": [{"startOffset": 4531, "endOffset": 4625, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnvironment", "ranges": [{"startOffset": 4630, "endOffset": 6989, "count": 0}], "isBlockCoverage": false}, {"functionName": "startChat", "ranges": [{"startOffset": 6994, "endOffset": 8646, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 8651, "endOffset": 9822, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJson", "ranges": [{"startOffset": 9827, "endOffset": 12489, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 12494, "endOffset": 14052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateEmbedding", "ranges": [{"startOffset": 14057, "endOffset": 15157, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryCompressChat", "ranges": [{"startOffset": 15162, "endOffset": 17765, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 17957, "endOffset": 19078, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1515", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 995, "endOffset": 6814, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7082, "endOffset": 9356, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9777, "endOffset": 12254, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1516", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 1}, {"startOffset": 362, "endOffset": 370, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1677, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1805, "endOffset": 1881, "count": 0}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1887, "endOffset": 2008, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 2083, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 5910, "endOffset": 6524, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 6529, "endOffset": 6592, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 6597, "endOffset": 6662, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1517", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1518", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2803, "endOffset": 31135, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1519", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3232, "endOffset": 6198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1520", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 398, "endOffset": 569, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1521", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3468, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3779, "endOffset": 4875, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5082, "endOffset": 5277, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienChat", "ranges": [{"startOffset": 5283, "endOffset": 5561, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getRequestTextFromContents", "ranges": [{"startOffset": 5566, "endOffset": 5780, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiRequest", "ranges": [{"startOffset": 5785, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiResponse", "ranges": [{"startOffset": 6032, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiError", "ranges": [{"startOffset": 6279, "endOffset": 6640, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 6832, "endOffset": 7915, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessage", "ranges": [{"startOffset": 8567, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 12194, "endOffset": 14714, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 15750, "endOffset": 16039, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHistory", "ranges": [{"startOffset": 16092, "endOffset": 16141, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 16273, "endOffset": 16336, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 16341, "endOffset": 16400, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFinalUsageMetadata", "ranges": [{"startOffset": 16405, "endOffset": 16635, "count": 0}], "isBlockCoverage": false}, {"functionName": "processStreamResponse", "ranges": [{"startOffset": 16640, "endOffset": 18155, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordHistory", "ranges": [{"startOffset": 18160, "endOffset": 21626, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTextContent", "ranges": [{"startOffset": 21631, "endOffset": 21899, "count": 0}], "isBlockCoverage": false}, {"functionName": "isT<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 21904, "endOffset": 22184, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1522", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1000, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1711, "endOffset": 1795, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2103, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 5455, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 6244, "endOffset": 7452, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 7671, "endOffset": 7930, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 8175, "endOffset": 9298, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1523", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 1}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 1}, {"startOffset": 568, "endOffset": 576, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 1}, {"startOffset": 655, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 1}, {"startOffset": 748, "endOffset": 756, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2375, "endOffset": 2425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2427, "endOffset": 2532, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2533, "endOffset": 3720, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3722, "endOffset": 4490, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4492, "endOffset": 5628, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5630, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6272, "endOffset": 7445, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7447, "endOffset": 9239, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 4345, "endOffset": 4418, "count": 5}], "isBlockCoverage": true}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 4420, "endOffset": 5097, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 5098, "endOffset": 7537, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7539, "endOffset": 7968, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2052", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1870, "endOffset": 1885, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1891, "endOffset": 2083, "count": 0}], "isBlockCoverage": true}, {"functionName": "ClearcutLogger", "ranges": [{"startOffset": 2139, "endOffset": 2196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstance", "ranges": [{"startOffset": 2208, "endOffset": 2496, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueueLogEvent", "ranges": [{"startOffset": 2601, "endOffset": 2804, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLogEvent", "ranges": [{"startOffset": 2809, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushIfNeeded", "ranges": [{"startOffset": 3091, "endOffset": 3251, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushToClearcut", "ranges": [{"startOffset": 3256, "endOffset": 5307, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeLogResponse", "ranges": [{"startOffset": 5396, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "logStartSessionEvent", "ranges": [{"startOffset": 6718, "endOffset": 9465, "count": 0}], "isBlockCoverage": false}, {"functionName": "logNewPromptEvent", "ranges": [{"startOffset": 9470, "endOffset": 9837, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCallEvent", "ranges": [{"startOffset": 9842, "endOffset": 11128, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequestEvent", "ranges": [{"startOffset": 11133, "endOffset": 11493, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponseEvent", "ranges": [{"startOffset": 11498, "endOffset": 13468, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiErrorEvent", "ranges": [{"startOffset": 13473, "endOffset": 14389, "count": 0}], "isBlockCoverage": false}, {"functionName": "logEndSessionEvent", "ranges": [{"startOffset": 14394, "endOffset": 14804, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdown", "ranges": [{"startOffset": 14809, "endOffset": 14945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2053", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 1}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 1}, {"startOffset": 691, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 1}, {"startOffset": 877, "endOffset": 885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 1}, {"startOffset": 968, "endOffset": 976, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 1}, {"startOffset": 1065, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1762, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1810, "endOffset": 2480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2512, "endOffset": 2837, "count": 0}], "isBlockCoverage": true}, {"functionName": "StartSessionEvent", "ranges": [{"startOffset": 2843, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4225, "endOffset": 4288, "count": 0}], "isBlockCoverage": true}, {"functionName": "EndSessionEvent", "ranges": [{"startOffset": 4294, "endOffset": 4475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4507, "endOffset": 4585, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserPromptEvent", "ranges": [{"startOffset": 4591, "endOffset": 4811, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4841, "endOffset": 4997, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolCallEvent", "ranges": [{"startOffset": 5003, "endOffset": 5542, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5574, "endOffset": 5650, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiRequestEvent", "ranges": [{"startOffset": 5656, "endOffset": 5870, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5900, "endOffset": 6019, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiErrorEvent", "ranges": [{"startOffset": 6025, "endOffset": 6372, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6405, "endOffset": 6654, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiResponseEvent", "ranges": [{"startOffset": 6660, "endOffset": 7382, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2054", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8055, "endOffset": 8354, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2055", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6096, "count": 1}, {"startOffset": 1116, "endOffset": 1121, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureArienDirExists", "ranges": [{"startOffset": 1282, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "readUserIdFromFile", "ranges": [{"startOffset": 1451, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeUserIdToFile", "ranges": [{"startOffset": 1683, "endOffset": 1791, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPersistentUserId", "ranges": [{"startOffset": 1967, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2056", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 1}, {"startOffset": 384, "endOffset": 392, "count": 0}], "isBlockCoverage": true}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2194", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 992, "endOffset": 1061, "count": 0}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 1067, "endOffset": 1832, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1915, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 2463, "endOffset": 2630, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2706, "endOffset": 2879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2949, "endOffset": 3041, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2195", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1010, "endOffset": 1086, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1092, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 1200, "endOffset": 1618, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1623, "endOffset": 2177, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 2182, "endOffset": 2283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 2288, "endOffset": 2816, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2821, "endOffset": 2872, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2197", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1719, "endOffset": 1730, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1736, "endOffset": 1839, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1844, "endOffset": 2078, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 2083, "endOffset": 2483, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 2488, "endOffset": 2795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2936, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 4541, "endOffset": 4951, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 4956, "endOffset": 5098, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 5103, "endOffset": 5313, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 5318, "endOffset": 5597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2204", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 1}, {"startOffset": 511, "endOffset": 519, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 1}, {"startOffset": 636, "endOffset": 644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 1}, {"startOffset": 757, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 1}, {"startOffset": 894, "endOffset": 902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 1}, {"startOffset": 1019, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 1}, {"startOffset": 1132, "endOffset": 1140, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 1}, {"startOffset": 1354, "endOffset": 1362, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 1}, {"startOffset": 1463, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 1}, {"startOffset": 1578, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 1}, {"startOffset": 1699, "endOffset": 1707, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 1}, {"startOffset": 1816, "endOffset": 1824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 1}, {"startOffset": 1933, "endOffset": 1941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 1}, {"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 1}, {"startOffset": 2276, "endOffset": 2284, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 1}, {"startOffset": 2395, "endOffset": 2403, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 1}, {"startOffset": 2510, "endOffset": 2518, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 1}, {"startOffset": 2615, "endOffset": 2623, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 1}, {"startOffset": 2738, "endOffset": 2746, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4170, "endOffset": 4276, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2205", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1097, "endOffset": 1169, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1238, "endOffset": 1413, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1489, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1560, "endOffset": 2994, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2999, "endOffset": 3521, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3526, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4825, "endOffset": 7173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 7178, "endOffset": 7603, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 7608, "endOffset": 8759, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 8764, "endOffset": 9090, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 9095, "endOffset": 9609, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 9614, "endOffset": 10632, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 10637, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2206", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2207", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1290, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1501, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3277, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3586, "endOffset": 3748, "count": 0}], "isBlockCoverage": true}, {"functionName": "CoreToolScheduler", "ranges": [{"startOffset": 3754, "endOffset": 4222, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatusInternal", "ranges": [{"startOffset": 4227, "endOffset": 8661, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArgsInternal", "ranges": [{"startOffset": 8666, "endOffset": 8979, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRunning", "ranges": [{"startOffset": 8984, "endOffset": 9117, "count": 0}], "isBlockCoverage": false}, {"functionName": "schedule", "ranges": [{"startOffset": 9122, "endOffset": 11771, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmationResponse", "ranges": [{"startOffset": 11776, "endOffset": 13799, "count": 0}], "isBlockCoverage": false}, {"functionName": "attemptExecutionOfScheduledCalls", "ranges": [{"startOffset": 13804, "endOffset": 16328, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAndNotifyCompletion", "ranges": [{"startOffset": 16333, "endOffset": 17051, "count": 0}], "isBlockCoverage": false}, {"functionName": "notifyToolCallsUpdate", "ranges": [{"startOffset": 17056, "endOffset": 17192, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2208", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1626, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1702, "endOffset": 2744, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2745, "endOffset": 3774, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3775, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4335, "endOffset": 5000, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2209", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 1}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 1}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1159, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1160, "endOffset": 1380, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1715, "endOffset": 1935, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1937, "endOffset": 2168, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2327, "endOffset": 2530, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2587, "endOffset": 4512, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4725, "endOffset": 6603, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2210", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 933, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2211", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 1}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1420, "endOffset": 1484, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1559, "endOffset": 1621, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1697, "endOffset": 1761, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1813, "endOffset": 2906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2907, "endOffset": 6602, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6603, "endOffset": 7464, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7465, "endOffset": 8104, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8105, "endOffset": 9189, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2212", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2213", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2214", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/errorParsing.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 1}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "isApiError", "ranges": [{"startOffset": 1306, "endOffset": 1473, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStructuredError", "ranges": [{"startOffset": 1474, "endOffset": 1624, "count": 1}], "isBlockCoverage": true}, {"functionName": "getRateLimitMessage", "ranges": [{"startOffset": 1625, "endOffset": 2068, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseAndFormatApiError", "ranges": [{"startOffset": 2069, "endOffset": 3215, "count": 1}, {"startOffset": 2232, "endOffset": 2284, "count": 0}, {"startOffset": 2306, "endOffset": 3214, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}