
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/ui/components/shared/text-buffer.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/ui/components/shared</a> text-buffer.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/1135</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/1135</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a>
<a name='L1284'></a><a href='#L1284'>1284</a>
<a name='L1285'></a><a href='#L1285'>1285</a>
<a name='L1286'></a><a href='#L1286'>1286</a>
<a name='L1287'></a><a href='#L1287'>1287</a>
<a name='L1288'></a><a href='#L1288'>1288</a>
<a name='L1289'></a><a href='#L1289'>1289</a>
<a name='L1290'></a><a href='#L1290'>1290</a>
<a name='L1291'></a><a href='#L1291'>1291</a>
<a name='L1292'></a><a href='#L1292'>1292</a>
<a name='L1293'></a><a href='#L1293'>1293</a>
<a name='L1294'></a><a href='#L1294'>1294</a>
<a name='L1295'></a><a href='#L1295'>1295</a>
<a name='L1296'></a><a href='#L1296'>1296</a>
<a name='L1297'></a><a href='#L1297'>1297</a>
<a name='L1298'></a><a href='#L1298'>1298</a>
<a name='L1299'></a><a href='#L1299'>1299</a>
<a name='L1300'></a><a href='#L1300'>1300</a>
<a name='L1301'></a><a href='#L1301'>1301</a>
<a name='L1302'></a><a href='#L1302'>1302</a>
<a name='L1303'></a><a href='#L1303'>1303</a>
<a name='L1304'></a><a href='#L1304'>1304</a>
<a name='L1305'></a><a href='#L1305'>1305</a>
<a name='L1306'></a><a href='#L1306'>1306</a>
<a name='L1307'></a><a href='#L1307'>1307</a>
<a name='L1308'></a><a href='#L1308'>1308</a>
<a name='L1309'></a><a href='#L1309'>1309</a>
<a name='L1310'></a><a href='#L1310'>1310</a>
<a name='L1311'></a><a href='#L1311'>1311</a>
<a name='L1312'></a><a href='#L1312'>1312</a>
<a name='L1313'></a><a href='#L1313'>1313</a>
<a name='L1314'></a><a href='#L1314'>1314</a>
<a name='L1315'></a><a href='#L1315'>1315</a>
<a name='L1316'></a><a href='#L1316'>1316</a>
<a name='L1317'></a><a href='#L1317'>1317</a>
<a name='L1318'></a><a href='#L1318'>1318</a>
<a name='L1319'></a><a href='#L1319'>1319</a>
<a name='L1320'></a><a href='#L1320'>1320</a>
<a name='L1321'></a><a href='#L1321'>1321</a>
<a name='L1322'></a><a href='#L1322'>1322</a>
<a name='L1323'></a><a href='#L1323'>1323</a>
<a name='L1324'></a><a href='#L1324'>1324</a>
<a name='L1325'></a><a href='#L1325'>1325</a>
<a name='L1326'></a><a href='#L1326'>1326</a>
<a name='L1327'></a><a href='#L1327'>1327</a>
<a name='L1328'></a><a href='#L1328'>1328</a>
<a name='L1329'></a><a href='#L1329'>1329</a>
<a name='L1330'></a><a href='#L1330'>1330</a>
<a name='L1331'></a><a href='#L1331'>1331</a>
<a name='L1332'></a><a href='#L1332'>1332</a>
<a name='L1333'></a><a href='#L1333'>1333</a>
<a name='L1334'></a><a href='#L1334'>1334</a>
<a name='L1335'></a><a href='#L1335'>1335</a>
<a name='L1336'></a><a href='#L1336'>1336</a>
<a name='L1337'></a><a href='#L1337'>1337</a>
<a name='L1338'></a><a href='#L1338'>1338</a>
<a name='L1339'></a><a href='#L1339'>1339</a>
<a name='L1340'></a><a href='#L1340'>1340</a>
<a name='L1341'></a><a href='#L1341'>1341</a>
<a name='L1342'></a><a href='#L1342'>1342</a>
<a name='L1343'></a><a href='#L1343'>1343</a>
<a name='L1344'></a><a href='#L1344'>1344</a>
<a name='L1345'></a><a href='#L1345'>1345</a>
<a name='L1346'></a><a href='#L1346'>1346</a>
<a name='L1347'></a><a href='#L1347'>1347</a>
<a name='L1348'></a><a href='#L1348'>1348</a>
<a name='L1349'></a><a href='#L1349'>1349</a>
<a name='L1350'></a><a href='#L1350'>1350</a>
<a name='L1351'></a><a href='#L1351'>1351</a>
<a name='L1352'></a><a href='#L1352'>1352</a>
<a name='L1353'></a><a href='#L1353'>1353</a>
<a name='L1354'></a><a href='#L1354'>1354</a>
<a name='L1355'></a><a href='#L1355'>1355</a>
<a name='L1356'></a><a href='#L1356'>1356</a>
<a name='L1357'></a><a href='#L1357'>1357</a>
<a name='L1358'></a><a href='#L1358'>1358</a>
<a name='L1359'></a><a href='#L1359'>1359</a>
<a name='L1360'></a><a href='#L1360'>1360</a>
<a name='L1361'></a><a href='#L1361'>1361</a>
<a name='L1362'></a><a href='#L1362'>1362</a>
<a name='L1363'></a><a href='#L1363'>1363</a>
<a name='L1364'></a><a href='#L1364'>1364</a>
<a name='L1365'></a><a href='#L1365'>1365</a>
<a name='L1366'></a><a href='#L1366'>1366</a>
<a name='L1367'></a><a href='#L1367'>1367</a>
<a name='L1368'></a><a href='#L1368'>1368</a>
<a name='L1369'></a><a href='#L1369'>1369</a>
<a name='L1370'></a><a href='#L1370'>1370</a>
<a name='L1371'></a><a href='#L1371'>1371</a>
<a name='L1372'></a><a href='#L1372'>1372</a>
<a name='L1373'></a><a href='#L1373'>1373</a>
<a name='L1374'></a><a href='#L1374'>1374</a>
<a name='L1375'></a><a href='#L1375'>1375</a>
<a name='L1376'></a><a href='#L1376'>1376</a>
<a name='L1377'></a><a href='#L1377'>1377</a>
<a name='L1378'></a><a href='#L1378'>1378</a>
<a name='L1379'></a><a href='#L1379'>1379</a>
<a name='L1380'></a><a href='#L1380'>1380</a>
<a name='L1381'></a><a href='#L1381'>1381</a>
<a name='L1382'></a><a href='#L1382'>1382</a>
<a name='L1383'></a><a href='#L1383'>1383</a>
<a name='L1384'></a><a href='#L1384'>1384</a>
<a name='L1385'></a><a href='#L1385'>1385</a>
<a name='L1386'></a><a href='#L1386'>1386</a>
<a name='L1387'></a><a href='#L1387'>1387</a>
<a name='L1388'></a><a href='#L1388'>1388</a>
<a name='L1389'></a><a href='#L1389'>1389</a>
<a name='L1390'></a><a href='#L1390'>1390</a>
<a name='L1391'></a><a href='#L1391'>1391</a>
<a name='L1392'></a><a href='#L1392'>1392</a>
<a name='L1393'></a><a href='#L1393'>1393</a>
<a name='L1394'></a><a href='#L1394'>1394</a>
<a name='L1395'></a><a href='#L1395'>1395</a>
<a name='L1396'></a><a href='#L1396'>1396</a>
<a name='L1397'></a><a href='#L1397'>1397</a>
<a name='L1398'></a><a href='#L1398'>1398</a>
<a name='L1399'></a><a href='#L1399'>1399</a>
<a name='L1400'></a><a href='#L1400'>1400</a>
<a name='L1401'></a><a href='#L1401'>1401</a>
<a name='L1402'></a><a href='#L1402'>1402</a>
<a name='L1403'></a><a href='#L1403'>1403</a>
<a name='L1404'></a><a href='#L1404'>1404</a>
<a name='L1405'></a><a href='#L1405'>1405</a>
<a name='L1406'></a><a href='#L1406'>1406</a>
<a name='L1407'></a><a href='#L1407'>1407</a>
<a name='L1408'></a><a href='#L1408'>1408</a>
<a name='L1409'></a><a href='#L1409'>1409</a>
<a name='L1410'></a><a href='#L1410'>1410</a>
<a name='L1411'></a><a href='#L1411'>1411</a>
<a name='L1412'></a><a href='#L1412'>1412</a>
<a name='L1413'></a><a href='#L1413'>1413</a>
<a name='L1414'></a><a href='#L1414'>1414</a>
<a name='L1415'></a><a href='#L1415'>1415</a>
<a name='L1416'></a><a href='#L1416'>1416</a>
<a name='L1417'></a><a href='#L1417'>1417</a>
<a name='L1418'></a><a href='#L1418'>1418</a>
<a name='L1419'></a><a href='#L1419'>1419</a>
<a name='L1420'></a><a href='#L1420'>1420</a>
<a name='L1421'></a><a href='#L1421'>1421</a>
<a name='L1422'></a><a href='#L1422'>1422</a>
<a name='L1423'></a><a href='#L1423'>1423</a>
<a name='L1424'></a><a href='#L1424'>1424</a>
<a name='L1425'></a><a href='#L1425'>1425</a>
<a name='L1426'></a><a href='#L1426'>1426</a>
<a name='L1427'></a><a href='#L1427'>1427</a>
<a name='L1428'></a><a href='#L1428'>1428</a>
<a name='L1429'></a><a href='#L1429'>1429</a>
<a name='L1430'></a><a href='#L1430'>1430</a>
<a name='L1431'></a><a href='#L1431'>1431</a>
<a name='L1432'></a><a href='#L1432'>1432</a>
<a name='L1433'></a><a href='#L1433'>1433</a>
<a name='L1434'></a><a href='#L1434'>1434</a>
<a name='L1435'></a><a href='#L1435'>1435</a>
<a name='L1436'></a><a href='#L1436'>1436</a>
<a name='L1437'></a><a href='#L1437'>1437</a>
<a name='L1438'></a><a href='#L1438'>1438</a>
<a name='L1439'></a><a href='#L1439'>1439</a>
<a name='L1440'></a><a href='#L1440'>1440</a>
<a name='L1441'></a><a href='#L1441'>1441</a>
<a name='L1442'></a><a href='#L1442'>1442</a>
<a name='L1443'></a><a href='#L1443'>1443</a>
<a name='L1444'></a><a href='#L1444'>1444</a>
<a name='L1445'></a><a href='#L1445'>1445</a>
<a name='L1446'></a><a href='#L1446'>1446</a>
<a name='L1447'></a><a href='#L1447'>1447</a>
<a name='L1448'></a><a href='#L1448'>1448</a>
<a name='L1449'></a><a href='#L1449'>1449</a>
<a name='L1450'></a><a href='#L1450'>1450</a>
<a name='L1451'></a><a href='#L1451'>1451</a>
<a name='L1452'></a><a href='#L1452'>1452</a>
<a name='L1453'></a><a href='#L1453'>1453</a>
<a name='L1454'></a><a href='#L1454'>1454</a>
<a name='L1455'></a><a href='#L1455'>1455</a>
<a name='L1456'></a><a href='#L1456'>1456</a>
<a name='L1457'></a><a href='#L1457'>1457</a>
<a name='L1458'></a><a href='#L1458'>1458</a>
<a name='L1459'></a><a href='#L1459'>1459</a>
<a name='L1460'></a><a href='#L1460'>1460</a>
<a name='L1461'></a><a href='#L1461'>1461</a>
<a name='L1462'></a><a href='#L1462'>1462</a>
<a name='L1463'></a><a href='#L1463'>1463</a>
<a name='L1464'></a><a href='#L1464'>1464</a>
<a name='L1465'></a><a href='#L1465'>1465</a>
<a name='L1466'></a><a href='#L1466'>1466</a>
<a name='L1467'></a><a href='#L1467'>1467</a>
<a name='L1468'></a><a href='#L1468'>1468</a>
<a name='L1469'></a><a href='#L1469'>1469</a>
<a name='L1470'></a><a href='#L1470'>1470</a>
<a name='L1471'></a><a href='#L1471'>1471</a>
<a name='L1472'></a><a href='#L1472'>1472</a>
<a name='L1473'></a><a href='#L1473'>1473</a>
<a name='L1474'></a><a href='#L1474'>1474</a>
<a name='L1475'></a><a href='#L1475'>1475</a>
<a name='L1476'></a><a href='#L1476'>1476</a>
<a name='L1477'></a><a href='#L1477'>1477</a>
<a name='L1478'></a><a href='#L1478'>1478</a>
<a name='L1479'></a><a href='#L1479'>1479</a>
<a name='L1480'></a><a href='#L1480'>1480</a>
<a name='L1481'></a><a href='#L1481'>1481</a>
<a name='L1482'></a><a href='#L1482'>1482</a>
<a name='L1483'></a><a href='#L1483'>1483</a>
<a name='L1484'></a><a href='#L1484'>1484</a>
<a name='L1485'></a><a href='#L1485'>1485</a>
<a name='L1486'></a><a href='#L1486'>1486</a>
<a name='L1487'></a><a href='#L1487'>1487</a>
<a name='L1488'></a><a href='#L1488'>1488</a>
<a name='L1489'></a><a href='#L1489'>1489</a>
<a name='L1490'></a><a href='#L1490'>1490</a>
<a name='L1491'></a><a href='#L1491'>1491</a>
<a name='L1492'></a><a href='#L1492'>1492</a>
<a name='L1493'></a><a href='#L1493'>1493</a>
<a name='L1494'></a><a href='#L1494'>1494</a>
<a name='L1495'></a><a href='#L1495'>1495</a>
<a name='L1496'></a><a href='#L1496'>1496</a>
<a name='L1497'></a><a href='#L1497'>1497</a>
<a name='L1498'></a><a href='#L1498'>1498</a>
<a name='L1499'></a><a href='#L1499'>1499</a>
<a name='L1500'></a><a href='#L1500'>1500</a>
<a name='L1501'></a><a href='#L1501'>1501</a>
<a name='L1502'></a><a href='#L1502'>1502</a>
<a name='L1503'></a><a href='#L1503'>1503</a>
<a name='L1504'></a><a href='#L1504'>1504</a>
<a name='L1505'></a><a href='#L1505'>1505</a>
<a name='L1506'></a><a href='#L1506'>1506</a>
<a name='L1507'></a><a href='#L1507'>1507</a>
<a name='L1508'></a><a href='#L1508'>1508</a>
<a name='L1509'></a><a href='#L1509'>1509</a>
<a name='L1510'></a><a href='#L1510'>1510</a>
<a name='L1511'></a><a href='#L1511'>1511</a>
<a name='L1512'></a><a href='#L1512'>1512</a>
<a name='L1513'></a><a href='#L1513'>1513</a>
<a name='L1514'></a><a href='#L1514'>1514</a>
<a name='L1515'></a><a href='#L1515'>1515</a>
<a name='L1516'></a><a href='#L1516'>1516</a>
<a name='L1517'></a><a href='#L1517'>1517</a>
<a name='L1518'></a><a href='#L1518'>1518</a>
<a name='L1519'></a><a href='#L1519'>1519</a>
<a name='L1520'></a><a href='#L1520'>1520</a>
<a name='L1521'></a><a href='#L1521'>1521</a>
<a name='L1522'></a><a href='#L1522'>1522</a>
<a name='L1523'></a><a href='#L1523'>1523</a>
<a name='L1524'></a><a href='#L1524'>1524</a>
<a name='L1525'></a><a href='#L1525'>1525</a>
<a name='L1526'></a><a href='#L1526'>1526</a>
<a name='L1527'></a><a href='#L1527'>1527</a>
<a name='L1528'></a><a href='#L1528'>1528</a>
<a name='L1529'></a><a href='#L1529'>1529</a>
<a name='L1530'></a><a href='#L1530'>1530</a>
<a name='L1531'></a><a href='#L1531'>1531</a>
<a name='L1532'></a><a href='#L1532'>1532</a>
<a name='L1533'></a><a href='#L1533'>1533</a>
<a name='L1534'></a><a href='#L1534'>1534</a>
<a name='L1535'></a><a href='#L1535'>1535</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >/**<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
&nbsp;
<span class="cstat-no" title="statement not covered" >import stripAnsi from 'strip-ansi';</span>
<span class="cstat-no" title="statement not covered" >import { spawnSync } from 'child_process';</span>
<span class="cstat-no" title="statement not covered" >import fs from 'fs';</span>
<span class="cstat-no" title="statement not covered" >import os from 'os';</span>
<span class="cstat-no" title="statement not covered" >import pathMod from 'path';</span>
<span class="cstat-no" title="statement not covered" >import { useState, useCallback, useEffect, useMemo } from 'react';</span>
<span class="cstat-no" title="statement not covered" >import stringWidth from 'string-width';</span>
<span class="cstat-no" title="statement not covered" >import { unescapePath } from '@arien/arien-cli-core';</span>
<span class="cstat-no" title="statement not covered" >import { toCodePoints, cpLen, cpSlice } from '../../utils/textUtils.js';</span>
&nbsp;
export type Direction =
  | 'left'
  | 'right'
  | 'up'
  | 'down'
  | 'wordLeft'
  | 'wordRight'
  | 'home'
  | 'end';
&nbsp;
// TODO(jacob314): refactor so all edit operations to be part of this list.
// This makes it robust for clients to apply multiple edit operations without
// having to carefully reason about how React manages state.
type UpdateOperation =
  | { type: 'insert'; payload: string }
  | { type: 'backspace' };
&nbsp;
// Simple helper for word‑wise ops.
<span class="cstat-no" title="statement not covered" >function isWordChar(ch: string | undefined): boolean {</span>
<span class="cstat-no" title="statement not covered" >  if (ch === undefined) {</span>
<span class="cstat-no" title="statement not covered" >    return false;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  return !/[\s,.;!?]/.test(ch);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Strip characters that can break terminal rendering.
 *
 * Strip ANSI escape codes and control characters except for line breaks.
 * Control characters such as delete break terminal UI rendering.
 */
<span class="cstat-no" title="statement not covered" >function stripUnsafeCharacters(str: string): string {</span>
<span class="cstat-no" title="statement not covered" >  const stripped = stripAnsi(str);</span>
<span class="cstat-no" title="statement not covered" >  return toCodePoints(stripAnsi(stripped))</span>
<span class="cstat-no" title="statement not covered" >    .filter((char) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (char.length &gt; 1) return false;</span>
<span class="cstat-no" title="statement not covered" >      const code = char.codePointAt(0);</span>
<span class="cstat-no" title="statement not covered" >      if (code === undefined) {</span>
<span class="cstat-no" title="statement not covered" >        return false;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      const isUnsafe =</span>
<span class="cstat-no" title="statement not covered" >        code === 127 || (code &lt;= 31 &amp;&amp; code !== 13 &amp;&amp; code !== 10);</span>
<span class="cstat-no" title="statement not covered" >      return !isUnsafe;</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >    .join('');</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
export interface Viewport {
  height: number;
  width: number;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >function clamp(v: number, min: number, max: number): number {</span>
<span class="cstat-no" title="statement not covered" >  return v &lt; min ? min : v &gt; max ? max : v;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/* -------------------------------------------------------------------------
 *  Debug helper – enable verbose logging by setting env var TEXTBUFFER_DEBUG=1
 * ---------------------------------------------------------------------- */
&nbsp;
// Enable verbose logging only when requested via env var.
<span class="cstat-no" title="statement not covered" >const DEBUG =</span>
<span class="cstat-no" title="statement not covered" >  process.env['TEXTBUFFER_DEBUG'] === '1' ||</span>
<span class="cstat-no" title="statement not covered" >  process.env['TEXTBUFFER_DEBUG'] === 'true';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >function dbg(...args: unknown[]): void {</span>
<span class="cstat-no" title="statement not covered" >  if (DEBUG) {</span>
<span class="cstat-no" title="statement not covered" >    console.log('[TextBuffer]', ...args);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/* ────────────────────────────────────────────────────────────────────────── */
&nbsp;
interface UseTextBufferProps {
  initialText?: string;
  initialCursorOffset?: number;
  viewport: Viewport; // Viewport dimensions needed for scrolling
  stdin?: NodeJS.ReadStream | null; // For external editor
  setRawMode?: (mode: boolean) =&gt; void; // For external editor
  onChange?: (text: string) =&gt; void; // Callback for when text changes
  isValidPath: (path: string) =&gt; boolean;
}
&nbsp;
interface UndoHistoryEntry {
  lines: string[];
  cursorRow: number;
  cursorCol: number;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >function calculateInitialCursorPosition(</span>
<span class="cstat-no" title="statement not covered" >  initialLines: string[],</span>
<span class="cstat-no" title="statement not covered" >  offset: number,</span>
<span class="cstat-no" title="statement not covered" >): [number, number] {</span>
<span class="cstat-no" title="statement not covered" >  let remainingChars = offset;</span>
<span class="cstat-no" title="statement not covered" >  let row = 0;</span>
<span class="cstat-no" title="statement not covered" >  while (row &lt; initialLines.length) {</span>
<span class="cstat-no" title="statement not covered" >    const lineLength = cpLen(initialLines[row]);</span>
    // Add 1 for the newline character (except for the last line)
<span class="cstat-no" title="statement not covered" >    const totalCharsInLineAndNewline =</span>
<span class="cstat-no" title="statement not covered" >      lineLength + (row &lt; initialLines.length - 1 ? 1 : 0);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (remainingChars &lt;= lineLength) {</span>
      // Cursor is on this line
<span class="cstat-no" title="statement not covered" >      return [row, remainingChars];</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    remainingChars -= totalCharsInLineAndNewline;</span>
<span class="cstat-no" title="statement not covered" >    row++;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  // Offset is beyond the text, place cursor at the end of the last line
<span class="cstat-no" title="statement not covered" >  if (initialLines.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >    const lastRow = initialLines.length - 1;</span>
<span class="cstat-no" title="statement not covered" >    return [lastRow, cpLen(initialLines[lastRow])];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  return [0, 0]; // Default for empty text</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export function offsetToLogicalPos(</span>
<span class="cstat-no" title="statement not covered" >  text: string,</span>
<span class="cstat-no" title="statement not covered" >  offset: number,</span>
<span class="cstat-no" title="statement not covered" >): [number, number] {</span>
<span class="cstat-no" title="statement not covered" >  let row = 0;</span>
<span class="cstat-no" title="statement not covered" >  let col = 0;</span>
<span class="cstat-no" title="statement not covered" >  let currentOffset = 0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (offset === 0) return [0, 0];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const lines = text.split('\n');</span>
<span class="cstat-no" title="statement not covered" >  for (let i = 0; i &lt; lines.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >    const line = lines[i];</span>
<span class="cstat-no" title="statement not covered" >    const lineLength = cpLen(line);</span>
<span class="cstat-no" title="statement not covered" >    const lineLengthWithNewline = lineLength + (i &lt; lines.length - 1 ? 1 : 0);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (offset &lt;= currentOffset + lineLength) {</span>
      // Check against lineLength first
<span class="cstat-no" title="statement not covered" >      row = i;</span>
<span class="cstat-no" title="statement not covered" >      col = offset - currentOffset;</span>
<span class="cstat-no" title="statement not covered" >      return [row, col];</span>
<span class="cstat-no" title="statement not covered" >    } else if (offset &lt;= currentOffset + lineLengthWithNewline) {</span>
      // Check if offset is the newline itself
<span class="cstat-no" title="statement not covered" >      row = i;</span>
<span class="cstat-no" title="statement not covered" >      col = lineLength; // Position cursor at the end of the current line content</span>
      // If the offset IS the newline, and it's not the last line, advance to next line, col 0
<span class="cstat-no" title="statement not covered" >      if (</span>
<span class="cstat-no" title="statement not covered" >        offset === currentOffset + lineLengthWithNewline &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >        i &lt; lines.length - 1</span>
<span class="cstat-no" title="statement not covered" >      ) {</span>
<span class="cstat-no" title="statement not covered" >        return [i + 1, 0];</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      return [row, col]; // Otherwise, it's at the end of the current line content</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    currentOffset += lineLengthWithNewline;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // If offset is beyond the text length, place cursor at the end of the last line
  // or [0,0] if text is empty
<span class="cstat-no" title="statement not covered" >  if (lines.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >    row = lines.length - 1;</span>
<span class="cstat-no" title="statement not covered" >    col = cpLen(lines[row]);</span>
<span class="cstat-no" title="statement not covered" >  } else {</span>
<span class="cstat-no" title="statement not covered" >    row = 0;</span>
<span class="cstat-no" title="statement not covered" >    col = 0;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  return [row, col];</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper to calculate visual lines and map cursor positions
<span class="cstat-no" title="statement not covered" >function calculateVisualLayout(</span>
<span class="cstat-no" title="statement not covered" >  logicalLines: string[],</span>
<span class="cstat-no" title="statement not covered" >  logicalCursor: [number, number],</span>
<span class="cstat-no" title="statement not covered" >  viewportWidth: number,</span>
): {
  visualLines: string[];
  visualCursor: [number, number];
  logicalToVisualMap: Array&lt;Array&lt;[number, number]&gt;&gt;; // For each logical line, an array of [visualLineIndex, startColInLogical]
  visualToLogicalMap: Array&lt;[number, number]&gt;; // For each visual line, its [logicalLineIndex, startColInLogical]
<span class="cstat-no" title="statement not covered" >} {</span>
<span class="cstat-no" title="statement not covered" >  const visualLines: string[] = [];</span>
<span class="cstat-no" title="statement not covered" >  const logicalToVisualMap: Array&lt;Array&lt;[number, number]&gt;&gt; = [];</span>
<span class="cstat-no" title="statement not covered" >  const visualToLogicalMap: Array&lt;[number, number]&gt; = [];</span>
<span class="cstat-no" title="statement not covered" >  let currentVisualCursor: [number, number] = [0, 0];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  logicalLines.forEach((logLine, logIndex) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    logicalToVisualMap[logIndex] = [];</span>
<span class="cstat-no" title="statement not covered" >    if (logLine.length === 0) {</span>
      // Handle empty logical line
<span class="cstat-no" title="statement not covered" >      logicalToVisualMap[logIndex].push([visualLines.length, 0]);</span>
<span class="cstat-no" title="statement not covered" >      visualToLogicalMap.push([logIndex, 0]);</span>
<span class="cstat-no" title="statement not covered" >      visualLines.push('');</span>
<span class="cstat-no" title="statement not covered" >      if (logIndex === logicalCursor[0] &amp;&amp; logicalCursor[1] === 0) {</span>
<span class="cstat-no" title="statement not covered" >        currentVisualCursor = [visualLines.length - 1, 0];</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
      // Non-empty logical line
<span class="cstat-no" title="statement not covered" >      let currentPosInLogLine = 0; // Tracks position within the current logical line (code point index)</span>
<span class="cstat-no" title="statement not covered" >      const codePointsInLogLine = toCodePoints(logLine);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      while (currentPosInLogLine &lt; codePointsInLogLine.length) {</span>
<span class="cstat-no" title="statement not covered" >        let currentChunk = '';</span>
<span class="cstat-no" title="statement not covered" >        let currentChunkVisualWidth = 0;</span>
<span class="cstat-no" title="statement not covered" >        let numCodePointsInChunk = 0;</span>
<span class="cstat-no" title="statement not covered" >        let lastWordBreakPoint = -1; // Index in codePointsInLogLine for word break</span>
<span class="cstat-no" title="statement not covered" >        let numCodePointsAtLastWordBreak = 0;</span>
&nbsp;
        // Iterate through code points to build the current visual line (chunk)
<span class="cstat-no" title="statement not covered" >        for (let i = currentPosInLogLine; i &lt; codePointsInLogLine.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >          const char = codePointsInLogLine[i];</span>
<span class="cstat-no" title="statement not covered" >          const charVisualWidth = stringWidth(char);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (currentChunkVisualWidth + charVisualWidth &gt; viewportWidth) {</span>
            // Character would exceed viewport width
<span class="cstat-no" title="statement not covered" >            if (</span>
<span class="cstat-no" title="statement not covered" >              lastWordBreakPoint !== -1 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              numCodePointsAtLastWordBreak &gt; 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              currentPosInLogLine + numCodePointsAtLastWordBreak &lt; i</span>
<span class="cstat-no" title="statement not covered" >            ) {</span>
              // We have a valid word break point to use, and it's not the start of the current segment
<span class="cstat-no" title="statement not covered" >              currentChunk = codePointsInLogLine</span>
<span class="cstat-no" title="statement not covered" >                .slice(</span>
<span class="cstat-no" title="statement not covered" >                  currentPosInLogLine,</span>
<span class="cstat-no" title="statement not covered" >                  currentPosInLogLine + numCodePointsAtLastWordBreak,</span>
<span class="cstat-no" title="statement not covered" >                )</span>
<span class="cstat-no" title="statement not covered" >                .join('');</span>
<span class="cstat-no" title="statement not covered" >              numCodePointsInChunk = numCodePointsAtLastWordBreak;</span>
<span class="cstat-no" title="statement not covered" >            } else {</span>
              // No word break, or word break is at the start of this potential chunk, or word break leads to empty chunk.
              // Hard break: take characters up to viewportWidth, or just the current char if it alone is too wide.
<span class="cstat-no" title="statement not covered" >              if (</span>
<span class="cstat-no" title="statement not covered" >                numCodePointsInChunk === 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >                charVisualWidth &gt; viewportWidth</span>
<span class="cstat-no" title="statement not covered" >              ) {</span>
                // Single character is wider than viewport, take it anyway
<span class="cstat-no" title="statement not covered" >                currentChunk = char;</span>
<span class="cstat-no" title="statement not covered" >                numCodePointsInChunk = 1;</span>
<span class="cstat-no" title="statement not covered" >              } else if (</span>
<span class="cstat-no" title="statement not covered" >                numCodePointsInChunk === 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >                charVisualWidth &lt;= viewportWidth</span>
<span class="cstat-no" title="statement not covered" >              ) {</span>
                // This case should ideally be caught by the next iteration if the char fits.
                // If it doesn't fit (because currentChunkVisualWidth was already &gt; 0 from a previous char that filled the line),
                // then numCodePointsInChunk would not be 0.
                // This branch means the current char *itself* doesn't fit an empty line, which is handled by the above.
                // If we are here, it means the loop should break and the current chunk (which is empty) is finalized.
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            break; // Break from inner loop to finalize this chunk</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          currentChunk += char;</span>
<span class="cstat-no" title="statement not covered" >          currentChunkVisualWidth += charVisualWidth;</span>
<span class="cstat-no" title="statement not covered" >          numCodePointsInChunk++;</span>
&nbsp;
          // Check for word break opportunity (space)
<span class="cstat-no" title="statement not covered" >          if (char === ' ') {</span>
<span class="cstat-no" title="statement not covered" >            lastWordBreakPoint = i; // Store code point index of the space</span>
            // Store the state *before* adding the space, if we decide to break here.
<span class="cstat-no" title="statement not covered" >            numCodePointsAtLastWordBreak = numCodePointsInChunk - 1; // Chars *before* the space</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // If the inner loop completed without breaking (i.e., remaining text fits)
        // or if the loop broke but numCodePointsInChunk is still 0 (e.g. first char too wide for empty line)
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          numCodePointsInChunk === 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          currentPosInLogLine &lt; codePointsInLogLine.length</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
          // This can happen if the very first character considered for a new visual line is wider than the viewport.
          // In this case, we take that single character.
<span class="cstat-no" title="statement not covered" >          const firstChar = codePointsInLogLine[currentPosInLogLine];</span>
<span class="cstat-no" title="statement not covered" >          currentChunk = firstChar;</span>
<span class="cstat-no" title="statement not covered" >          numCodePointsInChunk = 1; // Ensure we advance</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // If after everything, numCodePointsInChunk is still 0 but we haven't processed the whole logical line,
        // it implies an issue, like viewportWidth being 0 or less. Avoid infinite loop.
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          numCodePointsInChunk === 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          currentPosInLogLine &lt; codePointsInLogLine.length</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
          // Force advance by one character to prevent infinite loop if something went wrong
<span class="cstat-no" title="statement not covered" >          currentChunk = codePointsInLogLine[currentPosInLogLine];</span>
<span class="cstat-no" title="statement not covered" >          numCodePointsInChunk = 1;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        logicalToVisualMap[logIndex].push([</span>
<span class="cstat-no" title="statement not covered" >          visualLines.length,</span>
<span class="cstat-no" title="statement not covered" >          currentPosInLogLine,</span>
<span class="cstat-no" title="statement not covered" >        ]);</span>
<span class="cstat-no" title="statement not covered" >        visualToLogicalMap.push([logIndex, currentPosInLogLine]);</span>
<span class="cstat-no" title="statement not covered" >        visualLines.push(currentChunk);</span>
&nbsp;
        // Cursor mapping logic
        // Note: currentPosInLogLine here is the start of the currentChunk within the logical line.
<span class="cstat-no" title="statement not covered" >        if (logIndex === logicalCursor[0]) {</span>
<span class="cstat-no" title="statement not covered" >          const cursorLogCol = logicalCursor[1]; // This is a code point index</span>
<span class="cstat-no" title="statement not covered" >          if (</span>
<span class="cstat-no" title="statement not covered" >            cursorLogCol &gt;= currentPosInLogLine &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >            cursorLogCol &lt; currentPosInLogLine + numCodePointsInChunk // Cursor is within this chunk</span>
<span class="cstat-no" title="statement not covered" >          ) {</span>
<span class="cstat-no" title="statement not covered" >            currentVisualCursor = [</span>
<span class="cstat-no" title="statement not covered" >              visualLines.length - 1,</span>
<span class="cstat-no" title="statement not covered" >              cursorLogCol - currentPosInLogLine, // Visual col is also code point index within visual line</span>
<span class="cstat-no" title="statement not covered" >            ];</span>
<span class="cstat-no" title="statement not covered" >          } else if (</span>
<span class="cstat-no" title="statement not covered" >            cursorLogCol === currentPosInLogLine + numCodePointsInChunk &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >            numCodePointsInChunk &gt; 0</span>
<span class="cstat-no" title="statement not covered" >          ) {</span>
            // Cursor is exactly at the end of this non-empty chunk
<span class="cstat-no" title="statement not covered" >            currentVisualCursor = [</span>
<span class="cstat-no" title="statement not covered" >              visualLines.length - 1,</span>
<span class="cstat-no" title="statement not covered" >              numCodePointsInChunk,</span>
<span class="cstat-no" title="statement not covered" >            ];</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const logicalStartOfThisChunk = currentPosInLogLine;</span>
<span class="cstat-no" title="statement not covered" >        currentPosInLogLine += numCodePointsInChunk;</span>
&nbsp;
        // If the chunk processed did not consume the entire logical line,
        // and the character immediately following the chunk is a space,
        // advance past this space as it acted as a delimiter for word wrapping.
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          logicalStartOfThisChunk + numCodePointsInChunk &lt;</span>
<span class="cstat-no" title="statement not covered" >            codePointsInLogLine.length &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          currentPosInLogLine &lt; codePointsInLogLine.length &amp;&amp; // Redundant if previous is true, but safe</span>
<span class="cstat-no" title="statement not covered" >          codePointsInLogLine[currentPosInLogLine] === ' '</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
<span class="cstat-no" title="statement not covered" >          currentPosInLogLine++;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // After all chunks of a non-empty logical line are processed,
      // if the cursor is at the very end of this logical line, update visual cursor.
<span class="cstat-no" title="statement not covered" >      if (</span>
<span class="cstat-no" title="statement not covered" >        logIndex === logicalCursor[0] &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >        logicalCursor[1] === codePointsInLogLine.length // Cursor at end of logical line</span>
<span class="cstat-no" title="statement not covered" >      ) {</span>
<span class="cstat-no" title="statement not covered" >        const lastVisualLineIdx = visualLines.length - 1;</span>
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          lastVisualLineIdx &gt;= 0 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          visualLines[lastVisualLineIdx] !== undefined</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
<span class="cstat-no" title="statement not covered" >          currentVisualCursor = [</span>
<span class="cstat-no" title="statement not covered" >            lastVisualLineIdx,</span>
<span class="cstat-no" title="statement not covered" >            cpLen(visualLines[lastVisualLineIdx]), // Cursor at end of last visual line for this logical line</span>
<span class="cstat-no" title="statement not covered" >          ];</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  });</span>
&nbsp;
  // If the entire logical text was empty, ensure there's one empty visual line.
<span class="cstat-no" title="statement not covered" >  if (</span>
<span class="cstat-no" title="statement not covered" >    logicalLines.length === 0 ||</span>
<span class="cstat-no" title="statement not covered" >    (logicalLines.length === 1 &amp;&amp; logicalLines[0] === '')</span>
<span class="cstat-no" title="statement not covered" >  ) {</span>
<span class="cstat-no" title="statement not covered" >    if (visualLines.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >      visualLines.push('');</span>
<span class="cstat-no" title="statement not covered" >      if (!logicalToVisualMap[0]) logicalToVisualMap[0] = [];</span>
<span class="cstat-no" title="statement not covered" >      logicalToVisualMap[0].push([0, 0]);</span>
<span class="cstat-no" title="statement not covered" >      visualToLogicalMap.push([0, 0]);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    currentVisualCursor = [0, 0];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  // Handle cursor at the very end of the text (after all processing)
  // This case might be covered by the loop end condition now, but kept for safety.
  else if (
<span class="cstat-no" title="statement not covered" >    logicalCursor[0] === logicalLines.length - 1 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >    logicalCursor[1] === cpLen(logicalLines[logicalLines.length - 1]) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >    visualLines.length &gt; 0</span>
<span class="cstat-no" title="statement not covered" >  ) {</span>
<span class="cstat-no" title="statement not covered" >    const lastVisLineIdx = visualLines.length - 1;</span>
<span class="cstat-no" title="statement not covered" >    currentVisualCursor = [lastVisLineIdx, cpLen(visualLines[lastVisLineIdx])];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return {</span>
<span class="cstat-no" title="statement not covered" >    visualLines,</span>
<span class="cstat-no" title="statement not covered" >    visualCursor: currentVisualCursor,</span>
<span class="cstat-no" title="statement not covered" >    logicalToVisualMap,</span>
<span class="cstat-no" title="statement not covered" >    visualToLogicalMap,</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export function useTextBuffer({</span>
<span class="cstat-no" title="statement not covered" >  initialText = '',</span>
<span class="cstat-no" title="statement not covered" >  initialCursorOffset = 0,</span>
<span class="cstat-no" title="statement not covered" >  viewport,</span>
<span class="cstat-no" title="statement not covered" >  stdin,</span>
<span class="cstat-no" title="statement not covered" >  setRawMode,</span>
<span class="cstat-no" title="statement not covered" >  onChange,</span>
<span class="cstat-no" title="statement not covered" >  isValidPath,</span>
<span class="cstat-no" title="statement not covered" >}: UseTextBufferProps): TextBuffer {</span>
<span class="cstat-no" title="statement not covered" >  const [lines, setLines] = useState&lt;string[]&gt;(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const l = initialText.split('\n');</span>
<span class="cstat-no" title="statement not covered" >    return l.length === 0 ? [''] : l;</span>
<span class="cstat-no" title="statement not covered" >  });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const [[initialCursorRow, initialCursorCol]] = useState(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    calculateInitialCursorPosition(lines, initialCursorOffset),</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const [cursorRow, setCursorRow] = useState&lt;number&gt;(initialCursorRow);</span>
<span class="cstat-no" title="statement not covered" >  const [cursorCol, setCursorCol] = useState&lt;number&gt;(initialCursorCol);</span>
<span class="cstat-no" title="statement not covered" >  const [preferredCol, setPreferredCol] = useState&lt;number | null&gt;(null); // Visual preferred col</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const [undoStack, setUndoStack] = useState&lt;UndoHistoryEntry[]&gt;([]);</span>
<span class="cstat-no" title="statement not covered" >  const [redoStack, setRedoStack] = useState&lt;UndoHistoryEntry[]&gt;([]);</span>
<span class="cstat-no" title="statement not covered" >  const historyLimit = 100;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const [clipboard, setClipboard] = useState&lt;string | null&gt;(null);</span>
<span class="cstat-no" title="statement not covered" >  const [selectionAnchor, setSelectionAnchor] = useState&lt;</span>
    [number, number] | null
<span class="cstat-no" title="statement not covered" >  &gt;(null); // Logical selection</span>
&nbsp;
  // Visual state
<span class="cstat-no" title="statement not covered" >  const [visualLines, setVisualLines] = useState&lt;string[]&gt;(['']);</span>
<span class="cstat-no" title="statement not covered" >  const [visualCursor, setVisualCursor] = useState&lt;[number, number]&gt;([0, 0]);</span>
<span class="cstat-no" title="statement not covered" >  const [visualScrollRow, setVisualScrollRow] = useState&lt;number&gt;(0);</span>
<span class="cstat-no" title="statement not covered" >  const [logicalToVisualMap, setLogicalToVisualMap] = useState&lt;</span>
    Array&lt;Array&lt;[number, number]&gt;&gt;
<span class="cstat-no" title="statement not covered" >  &gt;([]);</span>
<span class="cstat-no" title="statement not covered" >  const [visualToLogicalMap, setVisualToLogicalMap] = useState&lt;</span>
    Array&lt;[number, number]&gt;
<span class="cstat-no" title="statement not covered" >  &gt;([]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const currentLine = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (r: number): string =&gt; lines[r] ?? '',</span>
<span class="cstat-no" title="statement not covered" >    [lines],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" >  const currentLineLen = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (r: number): number =&gt; cpLen(currentLine(r)),</span>
<span class="cstat-no" title="statement not covered" >    [currentLine],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
  // Recalculate visual layout whenever logical lines or viewport width changes
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const layout = calculateVisualLayout(</span>
<span class="cstat-no" title="statement not covered" >      lines,</span>
<span class="cstat-no" title="statement not covered" >      [cursorRow, cursorCol],</span>
<span class="cstat-no" title="statement not covered" >      viewport.width,</span>
<span class="cstat-no" title="statement not covered" >    );</span>
<span class="cstat-no" title="statement not covered" >    setVisualLines(layout.visualLines);</span>
<span class="cstat-no" title="statement not covered" >    setVisualCursor(layout.visualCursor);</span>
<span class="cstat-no" title="statement not covered" >    setLogicalToVisualMap(layout.logicalToVisualMap);</span>
<span class="cstat-no" title="statement not covered" >    setVisualToLogicalMap(layout.visualToLogicalMap);</span>
<span class="cstat-no" title="statement not covered" >  }, [lines, cursorRow, cursorCol, viewport.width]);</span>
&nbsp;
  // Update visual scroll (vertical)
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const { height } = viewport;</span>
<span class="cstat-no" title="statement not covered" >    let newVisualScrollRow = visualScrollRow;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (visualCursor[0] &lt; visualScrollRow) {</span>
<span class="cstat-no" title="statement not covered" >      newVisualScrollRow = visualCursor[0];</span>
<span class="cstat-no" title="statement not covered" >    } else if (visualCursor[0] &gt;= visualScrollRow + height) {</span>
<span class="cstat-no" title="statement not covered" >      newVisualScrollRow = visualCursor[0] - height + 1;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (newVisualScrollRow !== visualScrollRow) {</span>
<span class="cstat-no" title="statement not covered" >      setVisualScrollRow(newVisualScrollRow);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [visualCursor, visualScrollRow, viewport]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const pushUndo = useCallback(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('pushUndo', { cursor: [cursorRow, cursorCol], text: lines.join('\n') });</span>
<span class="cstat-no" title="statement not covered" >    const snapshot = { lines: [...lines], cursorRow, cursorCol };</span>
<span class="cstat-no" title="statement not covered" >    setUndoStack((prev) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const newStack = [...prev, snapshot];</span>
<span class="cstat-no" title="statement not covered" >      if (newStack.length &gt; historyLimit) {</span>
<span class="cstat-no" title="statement not covered" >        newStack.shift();</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      return newStack;</span>
<span class="cstat-no" title="statement not covered" >    });</span>
<span class="cstat-no" title="statement not covered" >    setRedoStack([]);</span>
<span class="cstat-no" title="statement not covered" >  }, [lines, cursorRow, cursorCol, historyLimit]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const _restoreState = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (state: UndoHistoryEntry | undefined): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (!state) return false;</span>
<span class="cstat-no" title="statement not covered" >      setLines(state.lines);</span>
<span class="cstat-no" title="statement not covered" >      setCursorRow(state.cursorRow);</span>
<span class="cstat-no" title="statement not covered" >      setCursorCol(state.cursorCol);</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const text = lines.join('\n');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (onChange) {</span>
<span class="cstat-no" title="statement not covered" >      onChange(text);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [text, onChange]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const undo = useCallback((): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const state = undoStack[undoStack.length - 1];</span>
<span class="cstat-no" title="statement not covered" >    if (!state) return false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    setUndoStack((prev) =&gt; prev.slice(0, -1));</span>
<span class="cstat-no" title="statement not covered" >    const currentSnapshot = { lines: [...lines], cursorRow, cursorCol };</span>
<span class="cstat-no" title="statement not covered" >    setRedoStack((prev) =&gt; [...prev, currentSnapshot]);</span>
<span class="cstat-no" title="statement not covered" >    return _restoreState(state);</span>
<span class="cstat-no" title="statement not covered" >  }, [undoStack, lines, cursorRow, cursorCol, _restoreState]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const redo = useCallback((): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const state = redoStack[redoStack.length - 1];</span>
<span class="cstat-no" title="statement not covered" >    if (!state) return false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    setRedoStack((prev) =&gt; prev.slice(0, -1));</span>
<span class="cstat-no" title="statement not covered" >    const currentSnapshot = { lines: [...lines], cursorRow, cursorCol };</span>
<span class="cstat-no" title="statement not covered" >    setUndoStack((prev) =&gt; [...prev, currentSnapshot]);</span>
<span class="cstat-no" title="statement not covered" >    return _restoreState(state);</span>
<span class="cstat-no" title="statement not covered" >  }, [redoStack, lines, cursorRow, cursorCol, _restoreState]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const insertStr = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (str: string): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      dbg('insertStr', { str, beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >      if (str === '') return false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      let normalised = str.replace(/\r\n/g, '\n').replace(/\r/g, '\n');</span>
<span class="cstat-no" title="statement not covered" >      normalised = stripUnsafeCharacters(normalised);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const parts = normalised.split('\n');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const newLines = [...lines];</span>
<span class="cstat-no" title="statement not covered" >      const lineContent = currentLine(cursorRow);</span>
<span class="cstat-no" title="statement not covered" >      const before = cpSlice(lineContent, 0, cursorCol);</span>
<span class="cstat-no" title="statement not covered" >      const after = cpSlice(lineContent, cursorCol);</span>
<span class="cstat-no" title="statement not covered" >      newLines[cursorRow] = before + parts[0];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (parts.length &gt; 1) {</span>
        // Adjusted condition for inserting multiple lines
<span class="cstat-no" title="statement not covered" >        const remainingParts = parts.slice(1);</span>
<span class="cstat-no" title="statement not covered" >        const lastPartOriginal = remainingParts.pop() ?? '';</span>
<span class="cstat-no" title="statement not covered" >        newLines.splice(cursorRow + 1, 0, ...remainingParts);</span>
<span class="cstat-no" title="statement not covered" >        newLines.splice(</span>
<span class="cstat-no" title="statement not covered" >          cursorRow + parts.length - 1,</span>
<span class="cstat-no" title="statement not covered" >          0,</span>
<span class="cstat-no" title="statement not covered" >          lastPartOriginal + after,</span>
<span class="cstat-no" title="statement not covered" >        );</span>
<span class="cstat-no" title="statement not covered" >        setCursorRow(cursorRow + parts.length - 1);</span>
<span class="cstat-no" title="statement not covered" >        setCursorCol(cpLen(lastPartOriginal));</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        setCursorCol(cpLen(before) + cpLen(parts[0]));</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      setLines(newLines);</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [pushUndo, cursorRow, cursorCol, lines, currentLine, setPreferredCol],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const applyOperations = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (ops: UpdateOperation[]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (ops.length === 0) return;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const expandedOps: UpdateOperation[] = [];</span>
<span class="cstat-no" title="statement not covered" >      for (const op of ops) {</span>
<span class="cstat-no" title="statement not covered" >        if (op.type === 'insert') {</span>
<span class="cstat-no" title="statement not covered" >          let currentText = '';</span>
<span class="cstat-no" title="statement not covered" >          for (const char of toCodePoints(op.payload)) {</span>
<span class="cstat-no" title="statement not covered" >            if (char.codePointAt(0) === 127) {</span>
              // \x7f
<span class="cstat-no" title="statement not covered" >              if (currentText.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                expandedOps.push({ type: 'insert', payload: currentText });</span>
<span class="cstat-no" title="statement not covered" >                currentText = '';</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              expandedOps.push({ type: 'backspace' });</span>
<span class="cstat-no" title="statement not covered" >            } else {</span>
<span class="cstat-no" title="statement not covered" >              currentText += char;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (currentText.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            expandedOps.push({ type: 'insert', payload: currentText });</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >          expandedOps.push(op);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (expandedOps.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      pushUndo(); // Snapshot before applying batch of updates</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const newLines = [...lines];</span>
<span class="cstat-no" title="statement not covered" >      let newCursorRow = cursorRow;</span>
<span class="cstat-no" title="statement not covered" >      let newCursorCol = cursorCol;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const currentLine = (r: number) =&gt; newLines[r] ?? '';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      for (const op of expandedOps) {</span>
<span class="cstat-no" title="statement not covered" >        if (op.type === 'insert') {</span>
<span class="cstat-no" title="statement not covered" >          const str = stripUnsafeCharacters(</span>
<span class="cstat-no" title="statement not covered" >            op.payload.replace(/\r\n/g, '\n').replace(/\r/g, '\n'),</span>
<span class="cstat-no" title="statement not covered" >          );</span>
<span class="cstat-no" title="statement not covered" >          const parts = str.split('\n');</span>
<span class="cstat-no" title="statement not covered" >          const lineContent = currentLine(newCursorRow);</span>
<span class="cstat-no" title="statement not covered" >          const before = cpSlice(lineContent, 0, newCursorCol);</span>
<span class="cstat-no" title="statement not covered" >          const after = cpSlice(lineContent, newCursorCol);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (parts.length &gt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            newLines[newCursorRow] = before + parts[0];</span>
<span class="cstat-no" title="statement not covered" >            const remainingParts = parts.slice(1);</span>
<span class="cstat-no" title="statement not covered" >            const lastPartOriginal = remainingParts.pop() ?? '';</span>
<span class="cstat-no" title="statement not covered" >            newLines.splice(newCursorRow + 1, 0, ...remainingParts);</span>
<span class="cstat-no" title="statement not covered" >            newLines.splice(</span>
<span class="cstat-no" title="statement not covered" >              newCursorRow + parts.length - 1,</span>
<span class="cstat-no" title="statement not covered" >              0,</span>
<span class="cstat-no" title="statement not covered" >              lastPartOriginal + after,</span>
<span class="cstat-no" title="statement not covered" >            );</span>
<span class="cstat-no" title="statement not covered" >            newCursorRow = newCursorRow + parts.length - 1;</span>
<span class="cstat-no" title="statement not covered" >            newCursorCol = cpLen(lastPartOriginal);</span>
<span class="cstat-no" title="statement not covered" >          } else {</span>
<span class="cstat-no" title="statement not covered" >            newLines[newCursorRow] = before + parts[0] + after;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            newCursorCol = cpLen(before) + cpLen(parts[0]);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        } else if (op.type === 'backspace') {</span>
<span class="cstat-no" title="statement not covered" >          if (newCursorCol === 0 &amp;&amp; newCursorRow === 0) continue;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (newCursorCol &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            const lineContent = currentLine(newCursorRow);</span>
<span class="cstat-no" title="statement not covered" >            newLines[newCursorRow] =</span>
<span class="cstat-no" title="statement not covered" >              cpSlice(lineContent, 0, newCursorCol - 1) +</span>
<span class="cstat-no" title="statement not covered" >              cpSlice(lineContent, newCursorCol);</span>
<span class="cstat-no" title="statement not covered" >            newCursorCol--;</span>
<span class="cstat-no" title="statement not covered" >          } else if (newCursorRow &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            const prevLineContent = currentLine(newCursorRow - 1);</span>
<span class="cstat-no" title="statement not covered" >            const currentLineContentVal = currentLine(newCursorRow);</span>
<span class="cstat-no" title="statement not covered" >            const newCol = cpLen(prevLineContent);</span>
<span class="cstat-no" title="statement not covered" >            newLines[newCursorRow - 1] =</span>
<span class="cstat-no" title="statement not covered" >              prevLineContent + currentLineContentVal;</span>
<span class="cstat-no" title="statement not covered" >            newLines.splice(newCursorRow, 1);</span>
<span class="cstat-no" title="statement not covered" >            newCursorRow--;</span>
<span class="cstat-no" title="statement not covered" >            newCursorCol = newCol;</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      setLines(newLines);</span>
<span class="cstat-no" title="statement not covered" >      setCursorRow(newCursorRow);</span>
<span class="cstat-no" title="statement not covered" >      setCursorCol(newCursorCol);</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [lines, cursorRow, cursorCol, pushUndo, setPreferredCol],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const insert = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (ch: string): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (/[\n\r]/.test(ch)) {</span>
<span class="cstat-no" title="statement not covered" >        insertStr(ch);</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      dbg('insert', { ch, beforeCursor: [cursorRow, cursorCol] });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      ch = stripUnsafeCharacters(ch);</span>
&nbsp;
      // Arbitrary threshold to avoid false positives on normal key presses
      // while still detecting virtually all reasonable length file paths.
<span class="cstat-no" title="statement not covered" >      const minLengthToInferAsDragDrop = 3;</span>
<span class="cstat-no" title="statement not covered" >      if (ch.length &gt;= minLengthToInferAsDragDrop) {</span>
        // Possible drag and drop of a file path.
<span class="cstat-no" title="statement not covered" >        let potentialPath = ch;</span>
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          potentialPath.length &gt; 2 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          potentialPath.startsWith("'") &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          potentialPath.endsWith("'")</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
<span class="cstat-no" title="statement not covered" >          potentialPath = ch.slice(1, -1);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        potentialPath = potentialPath.trim();</span>
        // Be conservative and only add an @ if the path is valid.
<span class="cstat-no" title="statement not covered" >        if (isValidPath(unescapePath(potentialPath))) {</span>
<span class="cstat-no" title="statement not covered" >          ch = `@${potentialPath}`;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      applyOperations([{ type: 'insert', payload: ch }]);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [applyOperations, cursorRow, cursorCol, isValidPath, insertStr],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const newline = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('newline', { beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >    applyOperations([{ type: 'insert', payload: '\n' }]);</span>
<span class="cstat-no" title="statement not covered" >  }, [applyOperations, cursorRow, cursorCol]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const backspace = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('backspace', { beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol === 0 &amp;&amp; cursorRow === 0) return;</span>
<span class="cstat-no" title="statement not covered" >    applyOperations([{ type: 'backspace' }]);</span>
<span class="cstat-no" title="statement not covered" >  }, [applyOperations, cursorRow, cursorCol]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const del = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('delete', { beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >    const lineContent = currentLine(cursorRow);</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol &lt; currentLineLen(cursorRow)) {</span>
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >        newLines[cursorRow] =</span>
<span class="cstat-no" title="statement not covered" >          cpSlice(lineContent, 0, cursorCol) +</span>
<span class="cstat-no" title="statement not covered" >          cpSlice(lineContent, cursorCol + 1);</span>
<span class="cstat-no" title="statement not covered" >        return newLines;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >    } else if (cursorRow &lt; lines.length - 1) {</span>
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      const nextLineContent = currentLine(cursorRow + 1);</span>
<span class="cstat-no" title="statement not covered" >      setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >        newLines[cursorRow] = lineContent + nextLineContent;</span>
<span class="cstat-no" title="statement not covered" >        newLines.splice(cursorRow + 1, 1);</span>
<span class="cstat-no" title="statement not covered" >        return newLines;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    // cursor position does not change for del
<span class="cstat-no" title="statement not covered" >    setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >  }, [</span>
<span class="cstat-no" title="statement not covered" >    pushUndo,</span>
<span class="cstat-no" title="statement not covered" >    cursorRow,</span>
<span class="cstat-no" title="statement not covered" >    cursorCol,</span>
<span class="cstat-no" title="statement not covered" >    currentLine,</span>
<span class="cstat-no" title="statement not covered" >    currentLineLen,</span>
<span class="cstat-no" title="statement not covered" >    lines.length,</span>
<span class="cstat-no" title="statement not covered" >    setPreferredCol,</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const setText = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (newText: string): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      dbg('setText', { text: newText });</span>
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      const newContentLines = newText.replace(/\r\n?/g, '\n').split('\n');</span>
<span class="cstat-no" title="statement not covered" >      setLines(newContentLines.length === 0 ? [''] : newContentLines);</span>
      // Set logical cursor to the end of the new text
<span class="cstat-no" title="statement not covered" >      const lastNewLineIndex = newContentLines.length - 1;</span>
<span class="cstat-no" title="statement not covered" >      setCursorRow(lastNewLineIndex);</span>
<span class="cstat-no" title="statement not covered" >      setCursorCol(cpLen(newContentLines[lastNewLineIndex] ?? ''));</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [pushUndo, setPreferredCol],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const replaceRange = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (</span>
<span class="cstat-no" title="statement not covered" >      startRow: number,</span>
<span class="cstat-no" title="statement not covered" >      startCol: number,</span>
<span class="cstat-no" title="statement not covered" >      endRow: number,</span>
<span class="cstat-no" title="statement not covered" >      endCol: number,</span>
<span class="cstat-no" title="statement not covered" >      replacementText: string,</span>
<span class="cstat-no" title="statement not covered" >    ): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (</span>
<span class="cstat-no" title="statement not covered" >        startRow &gt; endRow ||</span>
<span class="cstat-no" title="statement not covered" >        (startRow === endRow &amp;&amp; startCol &gt; endCol) ||</span>
<span class="cstat-no" title="statement not covered" >        startRow &lt; 0 ||</span>
<span class="cstat-no" title="statement not covered" >        startCol &lt; 0 ||</span>
<span class="cstat-no" title="statement not covered" >        endRow &gt;= lines.length ||</span>
<span class="cstat-no" title="statement not covered" >        (endRow &lt; lines.length &amp;&amp; endCol &gt; currentLineLen(endRow))</span>
<span class="cstat-no" title="statement not covered" >      ) {</span>
<span class="cstat-no" title="statement not covered" >        console.error('Invalid range provided to replaceRange', {</span>
<span class="cstat-no" title="statement not covered" >          startRow,</span>
<span class="cstat-no" title="statement not covered" >          startCol,</span>
<span class="cstat-no" title="statement not covered" >          endRow,</span>
<span class="cstat-no" title="statement not covered" >          endCol,</span>
<span class="cstat-no" title="statement not covered" >          linesLength: lines.length,</span>
<span class="cstat-no" title="statement not covered" >          endRowLineLength: currentLineLen(endRow),</span>
<span class="cstat-no" title="statement not covered" >        });</span>
<span class="cstat-no" title="statement not covered" >        return false;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      dbg('replaceRange', {</span>
<span class="cstat-no" title="statement not covered" >        start: [startRow, startCol],</span>
<span class="cstat-no" title="statement not covered" >        end: [endRow, endCol],</span>
<span class="cstat-no" title="statement not covered" >        text: replacementText,</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const sCol = clamp(startCol, 0, currentLineLen(startRow));</span>
<span class="cstat-no" title="statement not covered" >      const eCol = clamp(endCol, 0, currentLineLen(endRow));</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const prefix = cpSlice(currentLine(startRow), 0, sCol);</span>
<span class="cstat-no" title="statement not covered" >      const suffix = cpSlice(currentLine(endRow), eCol);</span>
<span class="cstat-no" title="statement not covered" >      const normalisedReplacement = replacementText</span>
<span class="cstat-no" title="statement not covered" >        .replace(/\r\n/g, '\n')</span>
<span class="cstat-no" title="statement not covered" >        .replace(/\r/g, '\n');</span>
<span class="cstat-no" title="statement not covered" >      const replacementParts = normalisedReplacement.split('\n');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const newLines = [...prevLines];</span>
        // Remove lines between startRow and endRow (exclusive of startRow, inclusive of endRow if different)
<span class="cstat-no" title="statement not covered" >        if (startRow &lt; endRow) {</span>
<span class="cstat-no" title="statement not covered" >          newLines.splice(startRow + 1, endRow - startRow);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // Construct the new content for the startRow
<span class="cstat-no" title="statement not covered" >        newLines[startRow] = prefix + replacementParts[0];</span>
&nbsp;
        // If replacementText has multiple lines, insert them
<span class="cstat-no" title="statement not covered" >        if (replacementParts.length &gt; 1) {</span>
<span class="cstat-no" title="statement not covered" >          const lastReplacementPart = replacementParts.pop() ?? ''; // parts are already split by \n</span>
          // Insert middle parts (if any)
<span class="cstat-no" title="statement not covered" >          if (replacementParts.length &gt; 1) {</span>
            // parts[0] is already used
<span class="cstat-no" title="statement not covered" >            newLines.splice(startRow + 1, 0, ...replacementParts.slice(1));</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // The line where the last part of the replacement will go
<span class="cstat-no" title="statement not covered" >          const targetRowForLastPart = startRow + (replacementParts.length - 1); // -1 because parts[0] is on startRow</span>
          // If the last part is not the first part (multi-line replacement)
<span class="cstat-no" title="statement not covered" >          if (</span>
<span class="cstat-no" title="statement not covered" >            targetRowForLastPart &gt; startRow ||</span>
<span class="cstat-no" title="statement not covered" >            (replacementParts.length === 1 &amp;&amp; lastReplacementPart !== '')</span>
<span class="cstat-no" title="statement not covered" >          ) {</span>
            // If the target row for the last part doesn't exist (because it's a new line created by replacement)
            // ensure it's created before trying to append suffix.
            // This case should be handled by splice if replacementParts.length &gt; 1
            // For single line replacement that becomes multi-line due to parts.length &gt; 1 logic, this is tricky.
            // Let's assume newLines[targetRowForLastPart] exists due to previous splice or it's newLines[startRow]
<span class="cstat-no" title="statement not covered" >            if (</span>
<span class="cstat-no" title="statement not covered" >              newLines[targetRowForLastPart] === undefined &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              targetRowForLastPart === startRow + 1 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              replacementParts.length === 1</span>
<span class="cstat-no" title="statement not covered" >            ) {</span>
              // This implies a single line replacement that became two lines.
              // e.g. "abc" replace "b" with "B\nC" -&gt; "aB", "C", "c"
              // Here, lastReplacementPart is "C", targetRowForLastPart is startRow + 1
<span class="cstat-no" title="statement not covered" >              newLines.splice(</span>
<span class="cstat-no" title="statement not covered" >                targetRowForLastPart,</span>
<span class="cstat-no" title="statement not covered" >                0,</span>
<span class="cstat-no" title="statement not covered" >                lastReplacementPart + suffix,</span>
<span class="cstat-no" title="statement not covered" >              );</span>
<span class="cstat-no" title="statement not covered" >            } else {</span>
<span class="cstat-no" title="statement not covered" >              newLines[targetRowForLastPart] =</span>
<span class="cstat-no" title="statement not covered" >                (newLines[targetRowForLastPart] || '') +</span>
<span class="cstat-no" title="statement not covered" >                lastReplacementPart +</span>
<span class="cstat-no" title="statement not covered" >                suffix;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          } else {</span>
            // Single line in replacementParts, but it was the only part
<span class="cstat-no" title="statement not covered" >            newLines[startRow] += suffix;</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          setCursorRow(targetRowForLastPart);</span>
<span class="cstat-no" title="statement not covered" >          setCursorCol(cpLen(newLines[targetRowForLastPart]) - cpLen(suffix));</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
          // Single line replacement (replacementParts has only one item)
<span class="cstat-no" title="statement not covered" >          newLines[startRow] += suffix;</span>
<span class="cstat-no" title="statement not covered" >          setCursorRow(startRow);</span>
<span class="cstat-no" title="statement not covered" >          setCursorCol(cpLen(prefix) + cpLen(replacementParts[0]));</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        return newLines;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [pushUndo, lines, currentLine, currentLineLen, setPreferredCol],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const deleteWordLeft = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('deleteWordLeft', { beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol === 0 &amp;&amp; cursorRow === 0) return;</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol === 0) {</span>
<span class="cstat-no" title="statement not covered" >      backspace();</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    pushUndo();</span>
<span class="cstat-no" title="statement not covered" >    const lineContent = currentLine(cursorRow);</span>
<span class="cstat-no" title="statement not covered" >    const arr = toCodePoints(lineContent);</span>
<span class="cstat-no" title="statement not covered" >    let start = cursorCol;</span>
<span class="cstat-no" title="statement not covered" >    let onlySpaces = true;</span>
<span class="cstat-no" title="statement not covered" >    for (let i = 0; i &lt; start; i++) {</span>
<span class="cstat-no" title="statement not covered" >      if (isWordChar(arr[i])) {</span>
<span class="cstat-no" title="statement not covered" >        onlySpaces = false;</span>
<span class="cstat-no" title="statement not covered" >        break;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (onlySpaces &amp;&amp; start &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      start--;</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      while (start &gt; 0 &amp;&amp; !isWordChar(arr[start - 1])) start--;</span>
<span class="cstat-no" title="statement not covered" >      while (start &gt; 0 &amp;&amp; isWordChar(arr[start - 1])) start--;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >      newLines[cursorRow] =</span>
<span class="cstat-no" title="statement not covered" >        cpSlice(lineContent, 0, start) + cpSlice(lineContent, cursorCol);</span>
<span class="cstat-no" title="statement not covered" >      return newLines;</span>
<span class="cstat-no" title="statement not covered" >    });</span>
<span class="cstat-no" title="statement not covered" >    setCursorCol(start);</span>
<span class="cstat-no" title="statement not covered" >    setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >  }, [pushUndo, cursorRow, cursorCol, currentLine, backspace, setPreferredCol]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const deleteWordRight = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    dbg('deleteWordRight', { beforeCursor: [cursorRow, cursorCol] });</span>
<span class="cstat-no" title="statement not covered" >    const lineContent = currentLine(cursorRow);</span>
<span class="cstat-no" title="statement not covered" >    const arr = toCodePoints(lineContent);</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol &gt;= arr.length &amp;&amp; cursorRow === lines.length - 1) return;</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol &gt;= arr.length) {</span>
<span class="cstat-no" title="statement not covered" >      del();</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    pushUndo();</span>
<span class="cstat-no" title="statement not covered" >    let end = cursorCol;</span>
<span class="cstat-no" title="statement not covered" >    while (end &lt; arr.length &amp;&amp; !isWordChar(arr[end])) end++;</span>
<span class="cstat-no" title="statement not covered" >    while (end &lt; arr.length &amp;&amp; isWordChar(arr[end])) end++;</span>
<span class="cstat-no" title="statement not covered" >    setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >      newLines[cursorRow] =</span>
<span class="cstat-no" title="statement not covered" >        cpSlice(lineContent, 0, cursorCol) + cpSlice(lineContent, end);</span>
<span class="cstat-no" title="statement not covered" >      return newLines;</span>
<span class="cstat-no" title="statement not covered" >    });</span>
<span class="cstat-no" title="statement not covered" >    setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >  }, [</span>
<span class="cstat-no" title="statement not covered" >    pushUndo,</span>
<span class="cstat-no" title="statement not covered" >    cursorRow,</span>
<span class="cstat-no" title="statement not covered" >    cursorCol,</span>
<span class="cstat-no" title="statement not covered" >    currentLine,</span>
<span class="cstat-no" title="statement not covered" >    del,</span>
<span class="cstat-no" title="statement not covered" >    lines.length,</span>
<span class="cstat-no" title="statement not covered" >    setPreferredCol,</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const killLineRight = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const lineContent = currentLine(cursorRow);</span>
<span class="cstat-no" title="statement not covered" >    if (cursorCol &lt; currentLineLen(cursorRow)) {</span>
      // Cursor is before the end of the line's content, delete text to the right
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >        newLines[cursorRow] = cpSlice(lineContent, 0, cursorCol);</span>
<span class="cstat-no" title="statement not covered" >        return newLines;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
      // Cursor position and preferredCol do not change in this case
<span class="cstat-no" title="statement not covered" >    } else if (</span>
<span class="cstat-no" title="statement not covered" >      cursorCol === currentLineLen(cursorRow) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >      cursorRow &lt; lines.length - 1</span>
<span class="cstat-no" title="statement not covered" >    ) {</span>
      // Cursor is at the end of the line's content (or line is empty),
      // and it's not the last line. Delete the newline.
      // `del()` handles pushUndo and setPreferredCol.
<span class="cstat-no" title="statement not covered" >      del();</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    // If cursor is at the end of the line and it's the last line, do nothing.
<span class="cstat-no" title="statement not covered" >  }, [</span>
<span class="cstat-no" title="statement not covered" >    pushUndo,</span>
<span class="cstat-no" title="statement not covered" >    cursorRow,</span>
<span class="cstat-no" title="statement not covered" >    cursorCol,</span>
<span class="cstat-no" title="statement not covered" >    currentLine,</span>
<span class="cstat-no" title="statement not covered" >    currentLineLen,</span>
<span class="cstat-no" title="statement not covered" >    lines.length,</span>
<span class="cstat-no" title="statement not covered" >    del,</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const killLineLeft = useCallback((): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const lineContent = currentLine(cursorRow);</span>
    // Only act if the cursor is not at the beginning of the line
<span class="cstat-no" title="statement not covered" >    if (cursorCol &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      pushUndo();</span>
<span class="cstat-no" title="statement not covered" >      setLines((prevLines) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const newLines = [...prevLines];</span>
<span class="cstat-no" title="statement not covered" >        newLines[cursorRow] = cpSlice(lineContent, cursorCol);</span>
<span class="cstat-no" title="statement not covered" >        return newLines;</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >      setCursorCol(0);</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [pushUndo, cursorRow, cursorCol, currentLine, setPreferredCol]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const move = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (dir: Direction): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      let newVisualRow = visualCursor[0];</span>
<span class="cstat-no" title="statement not covered" >      let newVisualCol = visualCursor[1];</span>
<span class="cstat-no" title="statement not covered" >      let newPreferredCol = preferredCol;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const currentVisLineLen = cpLen(visualLines[newVisualRow] ?? '');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      switch (dir) {</span>
<span class="cstat-no" title="statement not covered" >        case 'left':</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          if (newVisualCol &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol--;</span>
<span class="cstat-no" title="statement not covered" >          } else if (newVisualRow &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            newVisualRow--;</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol = cpLen(visualLines[newVisualRow] ?? '');</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        case 'right':</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          if (newVisualCol &lt; currentVisLineLen) {</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol++;</span>
<span class="cstat-no" title="statement not covered" >          } else if (newVisualRow &lt; visualLines.length - 1) {</span>
<span class="cstat-no" title="statement not covered" >            newVisualRow++;</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol = 0;</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        case 'up':</span>
<span class="cstat-no" title="statement not covered" >          if (newVisualRow &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            if (newPreferredCol === null) newPreferredCol = newVisualCol;</span>
<span class="cstat-no" title="statement not covered" >            newVisualRow--;</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol = clamp(</span>
<span class="cstat-no" title="statement not covered" >              newPreferredCol,</span>
<span class="cstat-no" title="statement not covered" >              0,</span>
<span class="cstat-no" title="statement not covered" >              cpLen(visualLines[newVisualRow] ?? ''),</span>
<span class="cstat-no" title="statement not covered" >            );</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        case 'down':</span>
<span class="cstat-no" title="statement not covered" >          if (newVisualRow &lt; visualLines.length - 1) {</span>
<span class="cstat-no" title="statement not covered" >            if (newPreferredCol === null) newPreferredCol = newVisualCol;</span>
<span class="cstat-no" title="statement not covered" >            newVisualRow++;</span>
<span class="cstat-no" title="statement not covered" >            newVisualCol = clamp(</span>
<span class="cstat-no" title="statement not covered" >              newPreferredCol,</span>
<span class="cstat-no" title="statement not covered" >              0,</span>
<span class="cstat-no" title="statement not covered" >              cpLen(visualLines[newVisualRow] ?? ''),</span>
<span class="cstat-no" title="statement not covered" >            );</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        case 'home':</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          newVisualCol = 0;</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        case 'end':</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          newVisualCol = currentVisLineLen;</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
        // wordLeft and wordRight might need more sophisticated visual handling
        // For now, they operate on the logical line derived from the visual cursor
<span class="cstat-no" title="statement not covered" >        case 'wordLeft': {</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          if (</span>
<span class="cstat-no" title="statement not covered" >            visualToLogicalMap.length === 0 ||</span>
<span class="cstat-no" title="statement not covered" >            logicalToVisualMap.length === 0</span>
          )
<span class="cstat-no" title="statement not covered" >            break;</span>
<span class="cstat-no" title="statement not covered" >          const [logRow, logColInitial] = visualToLogicalMap[newVisualRow] ?? [</span>
<span class="cstat-no" title="statement not covered" >            0, 0,</span>
<span class="cstat-no" title="statement not covered" >          ];</span>
<span class="cstat-no" title="statement not covered" >          const currentLogCol = logColInitial + newVisualCol;</span>
<span class="cstat-no" title="statement not covered" >          const lineText = lines[logRow];</span>
<span class="cstat-no" title="statement not covered" >          const sliceToCursor = cpSlice(lineText, 0, currentLogCol).replace(</span>
<span class="cstat-no" title="statement not covered" >            /[\s,.;!?]+$/,</span>
<span class="cstat-no" title="statement not covered" >            '',</span>
<span class="cstat-no" title="statement not covered" >          );</span>
<span class="cstat-no" title="statement not covered" >          let lastIdx = 0;</span>
<span class="cstat-no" title="statement not covered" >          const regex = /[\s,.;!?]+/g;</span>
<span class="cstat-no" title="statement not covered" >          let m;</span>
<span class="cstat-no" title="statement not covered" >          while ((m = regex.exec(sliceToCursor)) != null) lastIdx = m.index;</span>
<span class="cstat-no" title="statement not covered" >          const newLogicalCol =</span>
<span class="cstat-no" title="statement not covered" >            lastIdx === 0 ? 0 : cpLen(sliceToCursor.slice(0, lastIdx)) + 1;</span>
&nbsp;
          // Map newLogicalCol back to visual
<span class="cstat-no" title="statement not covered" >          const targetLogicalMapEntries = logicalToVisualMap[logRow];</span>
<span class="cstat-no" title="statement not covered" >          if (!targetLogicalMapEntries) break;</span>
<span class="cstat-no" title="statement not covered" >          for (let i = targetLogicalMapEntries.length - 1; i &gt;= 0; i--) {</span>
<span class="cstat-no" title="statement not covered" >            const [visRow, logStartCol] = targetLogicalMapEntries[i];</span>
<span class="cstat-no" title="statement not covered" >            if (newLogicalCol &gt;= logStartCol) {</span>
<span class="cstat-no" title="statement not covered" >              newVisualRow = visRow;</span>
<span class="cstat-no" title="statement not covered" >              newVisualCol = newLogicalCol - logStartCol;</span>
<span class="cstat-no" title="statement not covered" >              break;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case 'wordRight': {</span>
<span class="cstat-no" title="statement not covered" >          newPreferredCol = null;</span>
<span class="cstat-no" title="statement not covered" >          if (</span>
<span class="cstat-no" title="statement not covered" >            visualToLogicalMap.length === 0 ||</span>
<span class="cstat-no" title="statement not covered" >            logicalToVisualMap.length === 0</span>
          )
<span class="cstat-no" title="statement not covered" >            break;</span>
<span class="cstat-no" title="statement not covered" >          const [logRow, logColInitial] = visualToLogicalMap[newVisualRow] ?? [</span>
<span class="cstat-no" title="statement not covered" >            0, 0,</span>
<span class="cstat-no" title="statement not covered" >          ];</span>
<span class="cstat-no" title="statement not covered" >          const currentLogCol = logColInitial + newVisualCol;</span>
<span class="cstat-no" title="statement not covered" >          const lineText = lines[logRow];</span>
<span class="cstat-no" title="statement not covered" >          const regex = /[\s,.;!?]+/g;</span>
<span class="cstat-no" title="statement not covered" >          let moved = false;</span>
<span class="cstat-no" title="statement not covered" >          let m;</span>
<span class="cstat-no" title="statement not covered" >          let newLogicalCol = currentLineLen(logRow); // Default to end of logical line</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          while ((m = regex.exec(lineText)) != null) {</span>
<span class="cstat-no" title="statement not covered" >            const cpIdx = cpLen(lineText.slice(0, m.index));</span>
<span class="cstat-no" title="statement not covered" >            if (cpIdx &gt; currentLogCol) {</span>
<span class="cstat-no" title="statement not covered" >              newLogicalCol = cpIdx;</span>
<span class="cstat-no" title="statement not covered" >              moved = true;</span>
<span class="cstat-no" title="statement not covered" >              break;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (!moved &amp;&amp; currentLogCol &lt; currentLineLen(logRow)) {</span>
            // If no word break found after cursor, move to end
<span class="cstat-no" title="statement not covered" >            newLogicalCol = currentLineLen(logRow);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // Map newLogicalCol back to visual
<span class="cstat-no" title="statement not covered" >          const targetLogicalMapEntries = logicalToVisualMap[logRow];</span>
<span class="cstat-no" title="statement not covered" >          if (!targetLogicalMapEntries) break;</span>
<span class="cstat-no" title="statement not covered" >          for (let i = 0; i &lt; targetLogicalMapEntries.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            const [visRow, logStartCol] = targetLogicalMapEntries[i];</span>
<span class="cstat-no" title="statement not covered" >            const nextLogStartCol =</span>
<span class="cstat-no" title="statement not covered" >              i + 1 &lt; targetLogicalMapEntries.length</span>
<span class="cstat-no" title="statement not covered" >                ? targetLogicalMapEntries[i + 1][1]</span>
<span class="cstat-no" title="statement not covered" >                : Infinity;</span>
<span class="cstat-no" title="statement not covered" >            if (</span>
<span class="cstat-no" title="statement not covered" >              newLogicalCol &gt;= logStartCol &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              newLogicalCol &lt; nextLogStartCol</span>
<span class="cstat-no" title="statement not covered" >            ) {</span>
<span class="cstat-no" title="statement not covered" >              newVisualRow = visRow;</span>
<span class="cstat-no" title="statement not covered" >              newVisualCol = newLogicalCol - logStartCol;</span>
<span class="cstat-no" title="statement not covered" >              break;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            if (</span>
<span class="cstat-no" title="statement not covered" >              newLogicalCol === logStartCol &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              i === targetLogicalMapEntries.length - 1 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >              cpLen(visualLines[visRow] ?? '') === 0</span>
<span class="cstat-no" title="statement not covered" >            ) {</span>
              // Special case: moving to an empty visual line at the end of a logical line
<span class="cstat-no" title="statement not covered" >              newVisualRow = visRow;</span>
<span class="cstat-no" title="statement not covered" >              newVisualCol = 0;</span>
<span class="cstat-no" title="statement not covered" >              break;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        default:</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      setVisualCursor([newVisualRow, newVisualCol]);</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(newPreferredCol);</span>
&nbsp;
      // Update logical cursor based on new visual cursor
<span class="cstat-no" title="statement not covered" >      if (visualToLogicalMap[newVisualRow]) {</span>
<span class="cstat-no" title="statement not covered" >        const [logRow, logStartCol] = visualToLogicalMap[newVisualRow];</span>
<span class="cstat-no" title="statement not covered" >        setCursorRow(logRow);</span>
<span class="cstat-no" title="statement not covered" >        setCursorCol(</span>
<span class="cstat-no" title="statement not covered" >          clamp(logStartCol + newVisualCol, 0, currentLineLen(logRow)),</span>
<span class="cstat-no" title="statement not covered" >        );</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      dbg('move', {</span>
<span class="cstat-no" title="statement not covered" >        dir,</span>
<span class="cstat-no" title="statement not covered" >        visualBefore: visualCursor,</span>
<span class="cstat-no" title="statement not covered" >        visualAfter: [newVisualRow, newVisualCol],</span>
<span class="cstat-no" title="statement not covered" >        logicalAfter: [cursorRow, cursorCol],</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [</span>
<span class="cstat-no" title="statement not covered" >      visualCursor,</span>
<span class="cstat-no" title="statement not covered" >      visualLines,</span>
<span class="cstat-no" title="statement not covered" >      preferredCol,</span>
<span class="cstat-no" title="statement not covered" >      lines,</span>
<span class="cstat-no" title="statement not covered" >      currentLineLen,</span>
<span class="cstat-no" title="statement not covered" >      visualToLogicalMap,</span>
<span class="cstat-no" title="statement not covered" >      logicalToVisualMap,</span>
<span class="cstat-no" title="statement not covered" >      cursorCol,</span>
<span class="cstat-no" title="statement not covered" >      cursorRow,</span>
<span class="cstat-no" title="statement not covered" >    ],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const openInExternalEditor = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    async (opts: { editor?: string } = {}): Promise&lt;void&gt; =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const editor =</span>
<span class="cstat-no" title="statement not covered" >        opts.editor ??</span>
<span class="cstat-no" title="statement not covered" >        process.env['VISUAL'] ??</span>
<span class="cstat-no" title="statement not covered" >        process.env['EDITOR'] ??</span>
<span class="cstat-no" title="statement not covered" >        (process.platform === 'win32' ? 'notepad' : 'vi');</span>
<span class="cstat-no" title="statement not covered" >      const tmpDir = fs.mkdtempSync(pathMod.join(os.tmpdir(), 'gemini-edit-'));</span>
<span class="cstat-no" title="statement not covered" >      const filePath = pathMod.join(tmpDir, 'buffer.txt');</span>
<span class="cstat-no" title="statement not covered" >      fs.writeFileSync(filePath, text, 'utf8');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      pushUndo(); // Snapshot before external edit</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const wasRaw = stdin?.isRaw ?? false;</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        setRawMode?.(false);</span>
<span class="cstat-no" title="statement not covered" >        const { status, error } = spawnSync(editor, [filePath], {</span>
<span class="cstat-no" title="statement not covered" >          stdio: 'inherit',</span>
<span class="cstat-no" title="statement not covered" >        });</span>
<span class="cstat-no" title="statement not covered" >        if (error) throw error;</span>
<span class="cstat-no" title="statement not covered" >        if (typeof status === 'number' &amp;&amp; status !== 0)</span>
<span class="cstat-no" title="statement not covered" >          throw new Error(`External editor exited with status ${status}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        let newText = fs.readFileSync(filePath, 'utf8');</span>
<span class="cstat-no" title="statement not covered" >        newText = newText.replace(/\r\n?/g, '\n');</span>
<span class="cstat-no" title="statement not covered" >        setText(newText);</span>
<span class="cstat-no" title="statement not covered" >      } catch (err) {</span>
<span class="cstat-no" title="statement not covered" >        console.error('[useTextBuffer] external editor error', err);</span>
        // TODO(jacobr): potentially revert or handle error state.
<span class="cstat-no" title="statement not covered" >      } finally {</span>
<span class="cstat-no" title="statement not covered" >        if (wasRaw) setRawMode?.(true);</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >          fs.unlinkSync(filePath);</span>
<span class="cstat-no" title="statement not covered" >        } catch {</span>
          /* ignore */
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >          fs.rmdirSync(tmpDir);</span>
<span class="cstat-no" title="statement not covered" >        } catch {</span>
          /* ignore */
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [text, pushUndo, stdin, setRawMode, setText],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleInput = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (key: {</span>
      name: string;
      ctrl: boolean;
      meta: boolean;
      shift: boolean;
      paste: boolean;
      sequence: string;
<span class="cstat-no" title="statement not covered" >    }): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const { sequence: input } = key;</span>
<span class="cstat-no" title="statement not covered" >      dbg('handleInput', {</span>
<span class="cstat-no" title="statement not covered" >        key,</span>
<span class="cstat-no" title="statement not covered" >        cursor: [cursorRow, cursorCol],</span>
<span class="cstat-no" title="statement not covered" >        visualCursor,</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >      const beforeText = text;</span>
<span class="cstat-no" title="statement not covered" >      const beforeLogicalCursor = [cursorRow, cursorCol];</span>
<span class="cstat-no" title="statement not covered" >      const beforeVisualCursor = [...visualCursor];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (key.name === 'escape') return false;</span>
&nbsp;
      // Handle paste events first - this is the key fix
<span class="cstat-no" title="statement not covered" >      if (key.paste &amp;&amp; input) {</span>
<span class="cstat-no" title="statement not covered" >        const pastedText = stripUnsafeCharacters(input);</span>
<span class="cstat-no" title="statement not covered" >        if (pastedText) {</span>
<span class="cstat-no" title="statement not covered" >          insert(pastedText);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      else if (
<span class="cstat-no" title="statement not covered" >        key.name === 'return' ||</span>
<span class="cstat-no" title="statement not covered" >        input === '\r' ||</span>
<span class="cstat-no" title="statement not covered" >        input === '\n' ||</span>
<span class="cstat-no" title="statement not covered" >        input === '\\\r' // VSCode terminal represents shift + enter this way</span>
      )
<span class="cstat-no" title="statement not covered" >        newline();</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'left' &amp;&amp; !key.meta &amp;&amp; !key.ctrl) move('left');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.ctrl &amp;&amp; key.name === 'b') move('left');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'right' &amp;&amp; !key.meta &amp;&amp; !key.ctrl) move('right');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.ctrl &amp;&amp; key.name === 'f') move('right');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'up') move('up');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'down') move('down');</span>
<span class="cstat-no" title="statement not covered" >      else if ((key.ctrl || key.meta) &amp;&amp; key.name === 'left') move('wordLeft');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.meta &amp;&amp; key.name === 'b') move('wordLeft');</span>
<span class="cstat-no" title="statement not covered" >      else if ((key.ctrl || key.meta) &amp;&amp; key.name === 'right')</span>
<span class="cstat-no" title="statement not covered" >        move('wordRight');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.meta &amp;&amp; key.name === 'f') move('wordRight');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'home') move('home');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.ctrl &amp;&amp; key.name === 'a') move('home');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'end') move('end');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.ctrl &amp;&amp; key.name === 'e') move('end');</span>
<span class="cstat-no" title="statement not covered" >      else if (key.ctrl &amp;&amp; key.name === 'w') deleteWordLeft();</span>
      else if (
<span class="cstat-no" title="statement not covered" >        (key.meta || key.ctrl) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >        (key.name === 'backspace' || input === '\x7f')</span>
      )
<span class="cstat-no" title="statement not covered" >        deleteWordLeft();</span>
<span class="cstat-no" title="statement not covered" >      else if ((key.meta || key.ctrl) &amp;&amp; key.name === 'delete')</span>
<span class="cstat-no" title="statement not covered" >        deleteWordRight();</span>
      else if (
<span class="cstat-no" title="statement not covered" >        key.name === 'backspace' ||</span>
<span class="cstat-no" title="statement not covered" >        input === '\x7f' ||</span>
<span class="cstat-no" title="statement not covered" >        (key.ctrl &amp;&amp; key.name === 'h')</span>
      )
<span class="cstat-no" title="statement not covered" >        backspace();</span>
<span class="cstat-no" title="statement not covered" >      else if (key.name === 'delete' || (key.ctrl &amp;&amp; key.name === 'd')) del();</span>
<span class="cstat-no" title="statement not covered" >      else if (input &amp;&amp; !key.ctrl &amp;&amp; !key.meta) {</span>
<span class="cstat-no" title="statement not covered" >        insert(input);</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const textChanged = text !== beforeText;</span>
      // After operations, visualCursor might not be immediately updated if the change
      // was to `lines`, `cursorRow`, or `cursorCol` which then triggers the useEffect.
      // So, for return value, we check logical cursor change.
<span class="cstat-no" title="statement not covered" >      const cursorChanged =</span>
<span class="cstat-no" title="statement not covered" >        cursorRow !== beforeLogicalCursor[0] ||</span>
<span class="cstat-no" title="statement not covered" >        cursorCol !== beforeLogicalCursor[1] ||</span>
<span class="cstat-no" title="statement not covered" >        visualCursor[0] !== beforeVisualCursor[0] ||</span>
<span class="cstat-no" title="statement not covered" >        visualCursor[1] !== beforeVisualCursor[1];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      dbg('handleInput:after', {</span>
<span class="cstat-no" title="statement not covered" >        cursor: [cursorRow, cursorCol],</span>
<span class="cstat-no" title="statement not covered" >        visualCursor,</span>
<span class="cstat-no" title="statement not covered" >        text,</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >      return textChanged || cursorChanged;</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [</span>
<span class="cstat-no" title="statement not covered" >      text,</span>
<span class="cstat-no" title="statement not covered" >      cursorRow,</span>
<span class="cstat-no" title="statement not covered" >      cursorCol,</span>
<span class="cstat-no" title="statement not covered" >      visualCursor,</span>
<span class="cstat-no" title="statement not covered" >      newline,</span>
<span class="cstat-no" title="statement not covered" >      move,</span>
<span class="cstat-no" title="statement not covered" >      deleteWordLeft,</span>
<span class="cstat-no" title="statement not covered" >      deleteWordRight,</span>
<span class="cstat-no" title="statement not covered" >      backspace,</span>
<span class="cstat-no" title="statement not covered" >      del,</span>
<span class="cstat-no" title="statement not covered" >      insert,</span>
<span class="cstat-no" title="statement not covered" >    ],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const renderedVisualLines = useMemo(</span>
<span class="cstat-no" title="statement not covered" >    () =&gt; visualLines.slice(visualScrollRow, visualScrollRow + viewport.height),</span>
<span class="cstat-no" title="statement not covered" >    [visualLines, visualScrollRow, viewport.height],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const replaceRangeByOffset = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (</span>
<span class="cstat-no" title="statement not covered" >      startOffset: number,</span>
<span class="cstat-no" title="statement not covered" >      endOffset: number,</span>
<span class="cstat-no" title="statement not covered" >      replacementText: string,</span>
<span class="cstat-no" title="statement not covered" >    ): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      dbg('replaceRangeByOffset', { startOffset, endOffset, replacementText });</span>
<span class="cstat-no" title="statement not covered" >      const [startRow, startCol] = offsetToLogicalPos(text, startOffset);</span>
<span class="cstat-no" title="statement not covered" >      const [endRow, endCol] = offsetToLogicalPos(text, endOffset);</span>
<span class="cstat-no" title="statement not covered" >      return replaceRange(startRow, startCol, endRow, endCol, replacementText);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [text, replaceRange],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const moveToOffset = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (offset: number): void =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const [newRow, newCol] = offsetToLogicalPos(text, offset);</span>
<span class="cstat-no" title="statement not covered" >      setCursorRow(newRow);</span>
<span class="cstat-no" title="statement not covered" >      setCursorCol(newCol);</span>
<span class="cstat-no" title="statement not covered" >      setPreferredCol(null);</span>
<span class="cstat-no" title="statement not covered" >      dbg('moveToOffset', { offset, newCursor: [newRow, newCol] });</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [text, setPreferredCol],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const returnValue: TextBuffer = {</span>
<span class="cstat-no" title="statement not covered" >    lines,</span>
<span class="cstat-no" title="statement not covered" >    text,</span>
<span class="cstat-no" title="statement not covered" >    cursor: [cursorRow, cursorCol],</span>
<span class="cstat-no" title="statement not covered" >    preferredCol,</span>
<span class="cstat-no" title="statement not covered" >    selectionAnchor,</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    allVisualLines: visualLines,</span>
<span class="cstat-no" title="statement not covered" >    viewportVisualLines: renderedVisualLines,</span>
<span class="cstat-no" title="statement not covered" >    visualCursor,</span>
<span class="cstat-no" title="statement not covered" >    visualScrollRow,</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    setText,</span>
<span class="cstat-no" title="statement not covered" >    insert,</span>
<span class="cstat-no" title="statement not covered" >    newline,</span>
<span class="cstat-no" title="statement not covered" >    backspace,</span>
<span class="cstat-no" title="statement not covered" >    del,</span>
<span class="cstat-no" title="statement not covered" >    move,</span>
<span class="cstat-no" title="statement not covered" >    undo,</span>
<span class="cstat-no" title="statement not covered" >    redo,</span>
<span class="cstat-no" title="statement not covered" >    replaceRange,</span>
<span class="cstat-no" title="statement not covered" >    replaceRangeByOffset,</span>
<span class="cstat-no" title="statement not covered" >    moveToOffset, // Added here</span>
<span class="cstat-no" title="statement not covered" >    deleteWordLeft,</span>
<span class="cstat-no" title="statement not covered" >    deleteWordRight,</span>
<span class="cstat-no" title="statement not covered" >    killLineRight,</span>
<span class="cstat-no" title="statement not covered" >    killLineLeft,</span>
<span class="cstat-no" title="statement not covered" >    handleInput,</span>
<span class="cstat-no" title="statement not covered" >    openInExternalEditor,</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    applyOperations,</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    copy: useCallback(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (!selectionAnchor) return null;</span>
<span class="cstat-no" title="statement not covered" >      const [ar, ac] = selectionAnchor;</span>
<span class="cstat-no" title="statement not covered" >      const [br, bc] = [cursorRow, cursorCol];</span>
<span class="cstat-no" title="statement not covered" >      if (ar === br &amp;&amp; ac === bc) return null;</span>
<span class="cstat-no" title="statement not covered" >      const topBefore = ar &lt; br || (ar === br &amp;&amp; ac &lt; bc);</span>
<span class="cstat-no" title="statement not covered" >      const [sr, sc, er, ec] = topBefore ? [ar, ac, br, bc] : [br, bc, ar, ac];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      let selectedTextVal;</span>
<span class="cstat-no" title="statement not covered" >      if (sr === er) {</span>
<span class="cstat-no" title="statement not covered" >        selectedTextVal = cpSlice(currentLine(sr), sc, ec);</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        const parts: string[] = [cpSlice(currentLine(sr), sc)];</span>
<span class="cstat-no" title="statement not covered" >        for (let r = sr + 1; r &lt; er; r++) parts.push(currentLine(r));</span>
<span class="cstat-no" title="statement not covered" >        parts.push(cpSlice(currentLine(er), 0, ec));</span>
<span class="cstat-no" title="statement not covered" >        selectedTextVal = parts.join('\n');</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      setClipboard(selectedTextVal);</span>
<span class="cstat-no" title="statement not covered" >      return selectedTextVal;</span>
<span class="cstat-no" title="statement not covered" >    }, [selectionAnchor, cursorRow, cursorCol, currentLine, setClipboard]),</span>
<span class="cstat-no" title="statement not covered" >    paste: useCallback(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (clipboard === null) return false;</span>
<span class="cstat-no" title="statement not covered" >      return insertStr(clipboard);</span>
<span class="cstat-no" title="statement not covered" >    }, [clipboard, insertStr]),</span>
<span class="cstat-no" title="statement not covered" >    startSelection: useCallback(</span>
<span class="cstat-no" title="statement not covered" >      () =&gt; setSelectionAnchor([cursorRow, cursorCol]),</span>
<span class="cstat-no" title="statement not covered" >      [cursorRow, cursorCol, setSelectionAnchor],</span>
<span class="cstat-no" title="statement not covered" >    ),</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  return returnValue;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
export interface TextBuffer {
  // State
  lines: string[]; // Logical lines
  text: string;
  cursor: [number, number]; // Logical cursor [row, col]
  /**
   * When the user moves the caret vertically we try to keep their original
   * horizontal column even when passing through shorter lines.  We remember
   * that *preferred* column in this field while the user is still travelling
   * vertically.  Any explicit horizontal movement resets the preference.
   */
  preferredCol: number | null; // Preferred visual column
  selectionAnchor: [number, number] | null; // Logical selection anchor
&nbsp;
  // Visual state (handles wrapping)
  allVisualLines: string[]; // All visual lines for the current text and viewport width.
  viewportVisualLines: string[]; // The subset of visual lines to be rendered based on visualScrollRow and viewport.height
  visualCursor: [number, number]; // Visual cursor [row, col] relative to the start of all visualLines
  visualScrollRow: number; // Scroll position for visual lines (index of the first visible visual line)
&nbsp;
  // Actions
&nbsp;
  /**
   * Replaces the entire buffer content with the provided text.
   * The operation is undoable.
   */
  setText: (text: string) =&gt; void;
  /**
   * Insert a single character or string without newlines.
   */
  insert: (ch: string) =&gt; void;
  newline: () =&gt; void;
  backspace: () =&gt; void;
  del: () =&gt; void;
  move: (dir: Direction) =&gt; void;
  undo: () =&gt; boolean;
  redo: () =&gt; boolean;
  /**
   * Replaces the text within the specified range with new text.
   * Handles both single-line and multi-line ranges.
   *
   * @param startRow The starting row index (inclusive).
   * @param startCol The starting column index (inclusive, code-point based).
   * @param endRow The ending row index (inclusive).
   * @param endCol The ending column index (exclusive, code-point based).
   * @param text The new text to insert.
   * @returns True if the buffer was modified, false otherwise.
   */
  replaceRange: (
    startRow: number,
    startCol: number,
    endRow: number,
    endCol: number,
    text: string,
  ) =&gt; boolean;
  /**
   * Delete the word to the *left* of the caret, mirroring common
   * Ctrl/Alt+Backspace behaviour in editors &amp; terminals. Both the adjacent
   * whitespace *and* the word characters immediately preceding the caret are
   * removed.  If the caret is already at column‑0 this becomes a no-op.
   */
  deleteWordLeft: () =&gt; void;
  /**
   * Delete the word to the *right* of the caret, akin to many editors'
   * Ctrl/Alt+Delete shortcut.  Removes any whitespace/punctuation that
   * follows the caret and the next contiguous run of word characters.
   */
  deleteWordRight: () =&gt; void;
  /**
   * Deletes text from the cursor to the end of the current line.
   */
  killLineRight: () =&gt; void;
  /**
   * Deletes text from the start of the current line to the cursor.
   */
  killLineLeft: () =&gt; void;
  /**
   * High level "handleInput" – receives what Ink gives us.
   */
  handleInput: (key: {
    name: string;
    ctrl: boolean;
    meta: boolean;
    shift: boolean;
    paste: boolean;
    sequence: string;
  }) =&gt; boolean;
  /**
   * Opens the current buffer contents in the user's preferred terminal text
   * editor ($VISUAL or $EDITOR, falling back to "vi").  The method blocks
   * until the editor exits, then reloads the file and replaces the in‑memory
   * buffer with whatever the user saved.
   *
   * The operation is treated as a single undoable edit – we snapshot the
   * previous state *once* before launching the editor so one `undo()` will
   * revert the entire change set.
   *
   * Note: We purposefully rely on the *synchronous* spawn API so that the
   * calling process genuinely waits for the editor to close before
   * continuing.  This mirrors Git's behaviour and simplifies downstream
   * control‑flow (callers can simply `await` the Promise).
   */
  openInExternalEditor: (opts?: { editor?: string }) =&gt; Promise&lt;void&gt;;
&nbsp;
  // Selection &amp; Clipboard
  copy: () =&gt; string | null;
  paste: () =&gt; boolean;
  startSelection: () =&gt; void;
  replaceRangeByOffset: (
    startOffset: number,
    endOffset: number,
    replacementText: string,
  ) =&gt; boolean;
  moveToOffset(offset: number): void;
&nbsp;
  // Batch updates
  applyOperations: (ops: UpdateOperation[]) =&gt; void;
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-02T11:18:46.083Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    