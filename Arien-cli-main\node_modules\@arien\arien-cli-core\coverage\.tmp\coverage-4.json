{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 897, "endOffset": 5539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1029, "endOffset": 1633, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1365, "endOffset": 1390, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1674, "endOffset": 1733, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1791, "endOffset": 2860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1882, "endOffset": 2372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2471, "endOffset": 2854, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2915, "endOffset": 4180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2964, "endOffset": 3190, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3036, "endOffset": 3100, "count": 8}, {"startOffset": 3076, "endOffset": 3100, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3276, "endOffset": 3612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3706, "endOffset": 3982, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4051, "endOffset": 4174, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4243, "endOffset": 4912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4292, "endOffset": 4493, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4364, "endOffset": 4403, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4573, "endOffset": 4715, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4796, "endOffset": 4906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4966, "endOffset": 5535, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5052, "endOffset": 5266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5354, "endOffset": 5529, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 9}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 8}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 8}, {"startOffset": 1031, "endOffset": 1241, "count": 7}, {"startOffset": 1173, "endOffset": 1197, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1659, "endOffset": 1917, "count": 9}, {"startOffset": 1710, "endOffset": 1747, "count": 6}, {"startOffset": 1749, "endOffset": 1780, "count": 2}, {"startOffset": 1780, "endOffset": 1819, "count": 7}, {"startOffset": 1819, "endOffset": 1858, "count": 4}, {"startOffset": 1860, "endOffset": 1891, "count": 0}, {"startOffset": 1891, "endOffset": 1916, "count": 7}], "isBlockCoverage": true}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 8}, {"startOffset": 2113, "endOffset": 2135, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 4}, {"startOffset": 2334, "endOffset": 2356, "count": 0}], "isBlockCoverage": true}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "322", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}