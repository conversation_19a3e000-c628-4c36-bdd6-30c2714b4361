/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
export type StatusType = 'success' | 'error' | 'warning' | 'info' | 'pending' | 'custom';
interface StatusBadgeProps {
    status: StatusType;
    text: string;
    customColor?: string;
    customIcon?: string;
    compact?: boolean;
}
export declare const StatusBadge: React.FC<StatusBadgeProps>;
interface StatusListProps {
    items: Array<{
        status: StatusType;
        text: string;
        customColor?: string;
        customIcon?: string;
    }>;
    title?: string;
}
export declare const StatusList: React.FC<StatusListProps>;
export {};
