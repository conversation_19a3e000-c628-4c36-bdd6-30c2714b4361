{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/gitService.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33841, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33841, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 409, "endOffset": 444, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 499, "endOffset": 534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 630, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 729, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 877, "endOffset": 1078, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1137, "endOffset": 1172, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1237, "endOffset": 1272, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1339, "endOffset": 1374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1434, "endOffset": 1469, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1528, "endOffset": 1563, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1622, "endOffset": 1657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1719, "endOffset": 1754, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1801, "endOffset": 2107, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1863, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2178, "endOffset": 2213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2270, "endOffset": 2327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2394, "endOffset": 2429, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2484, "endOffset": 2533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2596, "endOffset": 2631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2670, "endOffset": 2711, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2777, "endOffset": 3067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2961, "endOffset": 3027, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3110, "endOffset": 3168, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3468, "endOffset": 11947, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3626, "endOffset": 5103, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3773, "endOffset": 3981, "count": 2}, {"startOffset": 3891, "endOffset": 3957, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4177, "endOffset": 4202, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4415, "endOffset": 4588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4635, "endOffset": 4835, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5144, "endOffset": 5203, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5258, "endOffset": 5504, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5376, "endOffset": 5498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5423, "endOffset": 5476, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5569, "endOffset": 6284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5667, "endOffset": 5856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5947, "endOffset": 6278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6002, "endOffset": 6099, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6338, "endOffset": 7602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6443, "endOffset": 6730, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6815, "endOffset": 7189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6870, "endOffset": 6967, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7285, "endOffset": 7596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7670, "endOffset": 11943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8091, "endOffset": 8504, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8592, "endOffset": 8853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8960, "endOffset": 9321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9414, "endOffset": 9686, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9778, "endOffset": 10443, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9890, "endOffset": 10033, "count": 1}, {"startOffset": 9959, "endOffset": 10005, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10562, "endOffset": 11128, "count": 1}, {"startOffset": 11122, "endOffset": 11127, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10682, "endOffset": 10817, "count": 1}, {"startOffset": 10751, "endOffset": 10789, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10877, "endOffset": 10902, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11233, "endOffset": 11565, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11663, "endOffset": 11937, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/gitService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 14}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1209, "endOffset": 1220, "count": 14}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1224, "endOffset": 1321, "count": 14}], "isBlockCoverage": true}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1324, "endOffset": 1548, "count": 8}], "isBlockCoverage": true}, {"functionName": "initialize", "ranges": [{"startOffset": 1551, "endOffset": 1913, "count": 3}, {"startOffset": 1642, "endOffset": 1712, "count": 1}, {"startOffset": 1712, "endOffset": 1798, "count": 2}, {"startOffset": 1798, "endOffset": 1912, "count": 1}], "isBlockCoverage": true}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 1916, "endOffset": 2167, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1965, "endOffset": 2161, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2034, "endOffset": 2153, "count": 4}, {"startOffset": 2066, "endOffset": 2145, "count": 2}], "isBlockCoverage": true}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2298, "endOffset": 3610, "count": 8}, {"startOffset": 2910, "endOffset": 3063, "count": 6}, {"startOffset": 3384, "endOffset": 3519, "count": 0}], "isBlockCoverage": true}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 3613, "endOffset": 3978, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 3981, "endOffset": 4113, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 4116, "endOffset": 4308, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 4311, "endOffset": 4501, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 8}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 8}, {"startOffset": 811, "endOffset": 819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 8}], "isBlockCoverage": true}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}