{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/grep.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 399, "endOffset": 691, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 445, "endOffset": 687, "count": 7}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 462, "endOffset": 580, "count": 14}, {"startOffset": 507, "endOffset": 527, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 550, "endOffset": 561, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1121, "endOffset": 11751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1255, "endOffset": 2210, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2251, "endOffset": 2353, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2415, "endOffset": 4560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2510, "endOffset": 2654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2748, "endOffset": 2903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3007, "endOffset": 3210, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3291, "endOffset": 3482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3567, "endOffset": 3761, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3843, "endOffset": 4160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4254, "endOffset": 4554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4611, "endOffset": 10007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4709, "endOffset": 5527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5605, "endOffset": 6155, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6235, "endOffset": 6829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6918, "endOffset": 7698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7798, "endOffset": 8176, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8264, "endOffset": 8714, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8805, "endOffset": 9459, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9543, "endOffset": 10001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10065, "endOffset": 11747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10163, "endOffset": 10320, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10420, "endOffset": 10624, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10721, "endOffset": 11023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11130, "endOffset": 11481, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11563, "endOffset": 11741, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/grep.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 20}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "GrepTool", "ranges": [{"startOffset": 1884, "endOffset": 3145, "count": 20}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3148, "endOffset": 3183, "count": 1}], "isBlockCoverage": true}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 3521, "endOffset": 4437, "count": 20}, {"startOffset": 3654, "endOffset": 3660, "count": 11}, {"startOffset": 3714, "endOffset": 3750, "count": 0}, {"startOffset": 3752, "endOffset": 3934, "count": 0}, {"startOffset": 4050, "endOffset": 4126, "count": 1}, {"startOffset": 4133, "endOffset": 4409, "count": 3}, {"startOffset": 4204, "endOffset": 4230, "count": 2}, {"startOffset": 4232, "endOffset": 4304, "count": 0}, {"startOffset": 4409, "endOffset": 4436, "count": 17}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4602, "endOffset": 5208, "count": 16}, {"startOffset": 4764, "endOffset": 4824, "count": 2}, {"startOffset": 4824, "endOffset": 4876, "count": 14}, {"startOffset": 4876, "endOffset": 5036, "count": 1}, {"startOffset": 5036, "endOffset": 5102, "count": 13}, {"startOffset": 5102, "endOffset": 5186, "count": 3}, {"startOffset": 5186, "endOffset": 5207, "count": 10}], "isBlockCoverage": true}, {"functionName": "execute", "ranges": [{"startOffset": 5397, "endOffset": 7957, "count": 8}, {"startOffset": 5515, "endOffset": 5718, "count": 1}, {"startOffset": 5718, "endOffset": 5858, "count": 7}, {"startOffset": 5858, "endOffset": 5864, "count": 5}, {"startOffset": 6037, "endOffset": 6070, "count": 7}, {"startOffset": 6070, "endOffset": 6324, "count": 1}, {"startOffset": 6195, "endOffset": 6229, "count": 0}, {"startOffset": 6324, "endOffset": 6998, "count": 6}, {"startOffset": 6998, "endOffset": 7007, "count": 4}, {"startOffset": 7008, "endOffset": 7019, "count": 2}, {"startOffset": 7156, "endOffset": 7190, "count": 2}, {"startOffset": 7191, "endOffset": 7195, "count": 4}, {"startOffset": 7512, "endOffset": 7641, "count": 6}, {"startOffset": 7641, "endOffset": 7953, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6378, "endOffset": 6894, "count": 9}, {"startOffset": 6589, "endOffset": 6646, "count": 0}, {"startOffset": 6686, "endOffset": 6739, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6823, "endOffset": 6860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7335, "endOffset": 7471, "count": 9}], "isBlockCoverage": true}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 8224, "endOffset": 8799, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8277, "endOffset": 8793, "count": 7}, {"startOffset": 8358, "endOffset": 8369, "count": 0}, {"startOffset": 8436, "endOffset": 8453, "count": 0}, {"startOffset": 8748, "endOffset": 8787, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8659, "endOffset": 8688, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8717, "endOffset": 8737, "count": 7}], "isBlockCoverage": true}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 9213, "endOffset": 10380, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 10537, "endOffset": 11166, "count": 7}, {"startOffset": 10631, "endOffset": 10684, "count": 3}, {"startOffset": 10707, "endOffset": 11137, "count": 5}, {"startOffset": 10854, "endOffset": 10876, "count": 4}, {"startOffset": 10878, "endOffset": 10924, "count": 1}, {"startOffset": 10924, "endOffset": 11131, "count": 4}], "isBlockCoverage": true}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 11398, "endOffset": 17940, "count": 7}, {"startOffset": 11647, "endOffset": 11686, "count": 0}, {"startOffset": 11687, "endOffset": 13329, "count": 0}, {"startOffset": 13423, "endOffset": 16081, "count": 0}, {"startOffset": 16260, "endOffset": 16269, "count": 2}, {"startOffset": 16270, "endOffset": 16278, "count": 5}, {"startOffset": 16798, "endOffset": 17704, "count": 20}, {"startOffset": 17388, "endOffset": 17696, "count": 0}, {"startOffset": 17737, "endOffset": 17936, "count": 0}], "isBlockCoverage": true}, {"functionName": "output", "ranges": [{"startOffset": 12036, "endOffset": 13067, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13622, "endOffset": 13668, "count": 0}], "isBlockCoverage": false}, {"functionName": "output", "ranges": [{"startOffset": 13871, "endOffset": 15814, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17027, "endOffset": 17375, "count": 34}, {"startOffset": 17080, "endOffset": 17363, "count": 9}, {"startOffset": 17211, "endOffset": 17270, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "340", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 20}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 32}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "341", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/schemaValidator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 16}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 609, "endOffset": 1607, "count": 16}, {"startOffset": 840, "endOffset": 936, "count": 2}, {"startOffset": 944, "endOffset": 1018, "count": 14}, {"startOffset": 1020, "endOffset": 1585, "count": 14}, {"startOffset": 1154, "endOffset": 1579, "count": 42}, {"startOffset": 1192, "endOffset": 1204, "count": 24}, {"startOffset": 1206, "endOffset": 1571, "count": 24}, {"startOffset": 1307, "endOffset": 1316, "count": 0}, {"startOffset": 1383, "endOffset": 1561, "count": 0}, {"startOffset": 1585, "endOffset": 1606, "count": 14}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "342", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 4}, {"startOffset": 455, "endOffset": 463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 4}, {"startOffset": 544, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 4}, {"startOffset": 1626, "endOffset": 3067, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1895, "endOffset": 1910, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 4}, {"startOffset": 3415, "endOffset": 3421, "count": 0}], "isBlockCoverage": true}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "343", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 3}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 4}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 3}], "isBlockCoverage": true}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 4}, {"startOffset": 1144, "endOffset": 1237, "count": 0}], "isBlockCoverage": true}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "383", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 7}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 7}, {"startOffset": 735, "endOffset": 1054, "count": 51}, {"startOffset": 858, "endOffset": 888, "count": 0}, {"startOffset": 993, "endOffset": 1017, "count": 7}, {"startOffset": 1017, "endOffset": 1054, "count": 44}, {"startOffset": 1078, "endOffset": 1116, "count": 0}], "isBlockCoverage": true}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}