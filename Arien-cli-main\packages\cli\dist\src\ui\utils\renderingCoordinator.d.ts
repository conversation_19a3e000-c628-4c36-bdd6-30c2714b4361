/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { HistoryItem, HistoryItemWithoutId } from '../types.js';
/**
 * Utility class to coordinate rendering between Static and pending items
 * to prevent UI duplications and rendering conflicts.
 */
export declare class RenderingCoordinator {
    /**
     * Deduplicates pending items against existing history to prevent
     * rendering the same content in both Static and pending areas.
     */
    static deduplicatePendingItems(pendingItems: HistoryItemWithoutId[], history: HistoryItem[]): HistoryItemWithoutId[];
    /**
     * Generates a unique hash for an item based on its content
     * to enable efficient deduplication comparisons.
     */
    private static generateContentHash;
    /**
     * Checks if two items represent the same content, ignoring IDs.
     */
    static areItemsEqual(item1: HistoryItem | HistoryItemWithoutId, item2: HistoryItem | HistoryItemWithoutId): boolean;
    /**
     * Optimizes item ordering for rendering performance.
     * Ensures that similar content types are grouped together.
     */
    static optimizeItemOrder(items: HistoryItemWithoutId[]): HistoryItemWithoutId[];
    /**
     * Validates that pending items don't conflict with rendering constraints.
     */
    static validatePendingItems(pendingItems: HistoryItemWithoutId[], maxPendingItems?: number): HistoryItemWithoutId[];
    /**
     * Creates a stable key for React rendering that helps prevent
     * unnecessary re-renders and maintains component state.
     */
    static generateStableKey(item: HistoryItem | HistoryItemWithoutId, index: number, prefix?: string): string;
    /**
     * Checks if items require immediate static rendering (for performance).
     */
    static shouldRenderInStatic(item: HistoryItemWithoutId): boolean;
}
