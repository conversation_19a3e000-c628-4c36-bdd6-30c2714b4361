{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useHistoryManager.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 901, "endOffset": 10334, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 983, "endOffset": 1175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1051, "endOffset": 1095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1259, "endOffset": 2018, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1327, "endOffset": 1371, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1551, "endOffset": 1615, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2127, "endOffset": 3058, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2195, "endOffset": 2239, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2559, "endOffset": 2688, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3134, "endOffset": 3920, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3202, "endOffset": 3246, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3457, "endOffset": 3533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3613, "endOffset": 3690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4037, "endOffset": 4663, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4105, "endOffset": 4149, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4329, "endOffset": 4393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4487, "endOffset": 4570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4725, "endOffset": 5491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4793, "endOffset": 4837, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5131, "endOffset": 5248, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5363, "endOffset": 5411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5579, "endOffset": 6857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5647, "endOffset": 5691, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6252, "endOffset": 6485, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6957, "endOffset": 8064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7025, "endOffset": 7069, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7534, "endOffset": 7711, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8153, "endOffset": 8817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8221, "endOffset": 8265, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8512, "endOffset": 8633, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8914, "endOffset": 9591, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8982, "endOffset": 9026, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9287, "endOffset": 9408, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9688, "endOffset": 10330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9756, "endOffset": 9800, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10035, "endOffset": 10156, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useHistoryManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 24}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "useHistory", "ranges": [{"startOffset": 589, "endOffset": 3787, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 817, "endOffset": 935, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1002, "endOffset": 1051, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1119, "endOffset": 3136, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1260, "endOffset": 3111, "count": 20}, {"startOffset": 1315, "endOffset": 3060, "count": 10}, {"startOffset": 1420, "endOffset": 1446, "count": 5}, {"startOffset": 1447, "endOffset": 1480, "count": 1}, {"startOffset": 1482, "endOffset": 1527, "count": 1}, {"startOffset": 1527, "endOffset": 1570, "count": 9}, {"startOffset": 1570, "endOffset": 1606, "count": 5}, {"startOffset": 1608, "endOffset": 1673, "count": 5}, {"startOffset": 1637, "endOffset": 1672, "count": 4}, {"startOffset": 1674, "endOffset": 1707, "count": 3}, {"startOffset": 1709, "endOffset": 1754, "count": 3}, {"startOffset": 1754, "endOffset": 1795, "count": 6}, {"startOffset": 1795, "endOffset": 1821, "count": 0}, {"startOffset": 1822, "endOffset": 1855, "count": 0}, {"startOffset": 1856, "endOffset": 1891, "count": 0}, {"startOffset": 1893, "endOffset": 1938, "count": 0}, {"startOffset": 1938, "endOffset": 1980, "count": 6}, {"startOffset": 1980, "endOffset": 2007, "count": 0}, {"startOffset": 2008, "endOffset": 2041, "count": 0}, {"startOffset": 2042, "endOffset": 2077, "count": 0}, {"startOffset": 2079, "endOffset": 2124, "count": 0}, {"startOffset": 2124, "endOffset": 2171, "count": 6}, {"startOffset": 2171, "endOffset": 2203, "count": 0}, {"startOffset": 2204, "endOffset": 2271, "count": 0}, {"startOffset": 2273, "endOffset": 2318, "count": 0}, {"startOffset": 2318, "endOffset": 3005, "count": 6}, {"startOffset": 3005, "endOffset": 3050, "count": 1}, {"startOffset": 3060, "endOffset": 3110, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2433, "endOffset": 2967, "count": 7}, {"startOffset": 2527, "endOffset": 2556, "count": 2}, {"startOffset": 2558, "endOffset": 2600, "count": 1}, {"startOffset": 2600, "endOffset": 2645, "count": 6}, {"startOffset": 2645, "endOffset": 2665, "count": 1}, {"startOffset": 2666, "endOffset": 2689, "count": 0}, {"startOffset": 2691, "endOffset": 2928, "count": 0}, {"startOffset": 2928, "endOffset": 2966, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3230, "endOffset": 3548, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3275, "endOffset": 3533, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3308, "endOffset": 3532, "count": 2}, {"startOffset": 3350, "endOffset": 3498, "count": 1}, {"startOffset": 3413, "endOffset": 3428, "count": 0}, {"startOffset": 3498, "endOffset": 3531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3621, "endOffset": 3689, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}