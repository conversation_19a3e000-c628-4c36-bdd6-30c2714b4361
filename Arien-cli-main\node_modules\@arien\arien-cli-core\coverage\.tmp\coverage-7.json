{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/memoryDiscovery.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 64817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 64817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 422, "endOffset": 689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1468, "endOffset": 21555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1823, "endOffset": 2564, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2668, "endOffset": 2946, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3073, "endOffset": 4132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3254, "endOffset": 3384, "count": 4}, {"startOffset": 3304, "endOffset": 3336, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3426, "endOffset": 3573, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4252, "endOffset": 5362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4491, "endOffset": 4620, "count": 4}, {"startOffset": 4540, "endOffset": 4572, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4662, "endOffset": 4807, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5466, "endOffset": 7277, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5775, "endOffset": 5951, "count": 4}, {"startOffset": 5850, "endOffset": 5903, "count": 0}], "isBlockCoverage": true}, {"functionName": "isDirectory", "ranges": [{"startOffset": 5882, "endOffset": 5892, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5991, "endOffset": 6148, "count": 4}, {"startOffset": 6068, "endOffset": 6100, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6190, "endOffset": 6434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7383, "endOffset": 9263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7737, "endOffset": 7870, "count": 4}, {"startOffset": 7809, "endOffset": 7823, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7912, "endOffset": 8097, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8138, "endOffset": 8674, "count": 1}, {"startOffset": 8174, "endOffset": 8460, "count": 0}, {"startOffset": 8486, "endOffset": 8650, "count": 0}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 8259, "endOffset": 8269, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 8296, "endOffset": 8307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 8381, "endOffset": 8392, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 8419, "endOffset": 8429, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 8571, "endOffset": 8581, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 8608, "endOffset": 8619, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9390, "endOffset": 11202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9682, "endOffset": 9858, "count": 4}, {"startOffset": 9757, "endOffset": 9810, "count": 0}], "isBlockCoverage": true}, {"functionName": "isDirectory", "ranges": [{"startOffset": 9789, "endOffset": 9799, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9898, "endOffset": 10053, "count": 4}, {"startOffset": 9973, "endOffset": 10005, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10095, "endOffset": 10323, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11315, "endOffset": 13310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11654, "endOffset": 11785, "count": 4}, {"startOffset": 11724, "endOffset": 11738, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11827, "endOffset": 11996, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12037, "endOffset": 12627, "count": 1}, {"startOffset": 12073, "endOffset": 12386, "count": 0}, {"startOffset": 12412, "endOffset": 12603, "count": 0}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 12185, "endOffset": 12195, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 12222, "endOffset": 12233, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 12307, "endOffset": 12318, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 12345, "endOffset": 12355, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 12524, "endOffset": 12534, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 12561, "endOffset": 12572, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13441, "endOffset": 17148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14321, "endOffset": 14612, "count": 4}, {"startOffset": 14396, "endOffset": 14449, "count": 0}, {"startOffset": 14511, "endOffset": 14564, "count": 0}], "isBlockCoverage": true}, {"functionName": "isDirectory", "ranges": [{"startOffset": 14428, "endOffset": 14438, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 14543, "endOffset": 14553, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14652, "endOffset": 14939, "count": 4}, {"startOffset": 14859, "endOffset": 14891, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14981, "endOffset": 15347, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15388, "endOffset": 15820, "count": 1}, {"startOffset": 15424, "endOffset": 15579, "count": 0}, {"startOffset": 15605, "endOffset": 15796, "count": 0}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 15500, "endOffset": 15511, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 15538, "endOffset": 15548, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 15717, "endOffset": 15727, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 15754, "endOffset": 15765, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17242, "endOffset": 19394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17683, "endOffset": 17895, "count": 4}, {"startOffset": 17738, "endOffset": 17752, "count": 0}, {"startOffset": 17798, "endOffset": 17848, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17937, "endOffset": 18063, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18104, "endOffset": 18733, "count": 1}, {"startOffset": 18140, "endOffset": 18427, "count": 0}, {"startOffset": 18460, "endOffset": 18651, "count": 0}, {"startOffset": 18681, "endOffset": 18709, "count": 0}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 18225, "endOffset": 18236, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 18263, "endOffset": 18273, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 18348, "endOffset": 18359, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 18386, "endOffset": 18396, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFile", "ranges": [{"startOffset": 18572, "endOffset": 18582, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 18609, "endOffset": 18620, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19502, "endOffset": 20447, "count": 1}, {"startOffset": 19687, "endOffset": 20170, "count": 250}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19612, "endOffset": 19625, "count": 15}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 19758, "endOffset": 19769, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDirectory", "ranges": [{"startOffset": 19792, "endOffset": 19802, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19858, "endOffset": 20027, "count": 1}, {"startOffset": 19894, "endOffset": 19910, "count": 0}, {"startOffset": 19994, "endOffset": 20004, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20525, "endOffset": 21551, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20640, "endOffset": 20770, "count": 4}, {"startOffset": 20690, "endOffset": 20722, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20812, "endOffset": 20962, "count": 1}, {"startOffset": 20914, "endOffset": 20961, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/memoryDiscovery.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 11}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1084, "endOffset": 1148, "count": 14}], "isBlockCoverage": true}, {"functionName": "warn", "ranges": [{"startOffset": 1223, "endOffset": 1285, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1361, "endOffset": 1425, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1477, "endOffset": 2618, "count": 11}, {"startOffset": 1595, "endOffset": 2616, "count": 44}, {"startOffset": 1737, "endOffset": 1805, "count": 0}, {"startOffset": 1914, "endOffset": 1940, "count": 0}, {"startOffset": 1998, "endOffset": 2019, "count": 0}, {"startOffset": 2056, "endOffset": 2450, "count": 0}, {"startOffset": 2557, "endOffset": 2583, "count": 11}, {"startOffset": 2583, "endOffset": 2616, "count": 33}], "isBlockCoverage": true}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2619, "endOffset": 6572, "count": 11}, {"startOffset": 3267, "endOffset": 3366, "count": 1}, {"startOffset": 3386, "endOffset": 3439, "count": 1}, {"startOffset": 3547, "endOffset": 3713, "count": 0}, {"startOffset": 3757, "endOffset": 3866, "count": 1}, {"startOffset": 3959, "endOffset": 4025, "count": 1}, {"startOffset": 4128, "endOffset": 4172, "count": 0}, {"startOffset": 4242, "endOffset": 4301, "count": 33}, {"startOffset": 4303, "endOffset": 5665, "count": 33}, {"startOffset": 4326, "endOffset": 4444, "count": 3}, {"startOffset": 4557, "endOffset": 4755, "count": 0}, {"startOffset": 4950, "endOffset": 5211, "count": 0}, {"startOffset": 5251, "endOffset": 5383, "count": 3}, {"startOffset": 5435, "endOffset": 5596, "count": 11}, {"startOffset": 5470, "endOffset": 5573, "count": 1}, {"startOffset": 5596, "endOffset": 5665, "count": 22}, {"startOffset": 5981, "endOffset": 6008, "count": 1}, {"startOffset": 6016, "endOffset": 6151, "count": 0}, {"startOffset": 6191, "endOffset": 6225, "count": 0}, {"startOffset": 6288, "endOffset": 6326, "count": 1}, {"startOffset": 6392, "endOffset": 6549, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5691, "endOffset": 5713, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6573, "endOffset": 7466, "count": 1}, {"startOffset": 6850, "endOffset": 6945, "count": 0}, {"startOffset": 6952, "endOffset": 7441, "count": 0}], "isBlockCoverage": true}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7467, "endOffset": 8106, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7594, "endOffset": 7636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7642, "endOffset": 8055, "count": 1}, {"startOffset": 7739, "endOffset": 7765, "count": 0}, {"startOffset": 7922, "endOffset": 7937, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8064, "endOffset": 8089, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8107, "endOffset": 9283, "count": 11}, {"startOffset": 8255, "endOffset": 8354, "count": 1}, {"startOffset": 8614, "endOffset": 8741, "count": 10}, {"startOffset": 8635, "endOffset": 8689, "count": 1}, {"startOffset": 8741, "endOffset": 8950, "count": 1}, {"startOffset": 8950, "endOffset": 9039, "count": 0}, {"startOffset": 9039, "endOffset": 9056, "count": 1}, {"startOffset": 9056, "endOffset": 9090, "count": 0}, {"startOffset": 9096, "endOffset": 9202, "count": 0}, {"startOffset": 9202, "endOffset": 9282, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/bfsFileSearch.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 11}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 618, "endOffset": 680, "count": 1}], "isBlockCoverage": true}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 684, "endOffset": 1893, "count": 11}, {"startOffset": 1105, "endOffset": 1128, "count": 0}, {"startOffset": 1197, "endOffset": 1283, "count": 1}, {"startOffset": 1408, "endOffset": 1437, "count": 0}, {"startOffset": 1472, "endOffset": 1865, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "322", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 55}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 13}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 15}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 11}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 12}, {"startOffset": 838, "endOffset": 846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3261, "endOffset": 3562, "count": 15}, {"startOffset": 3338, "endOffset": 3454, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3421, "endOffset": 3442, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 3564, "endOffset": 3727, "count": 11}, {"startOffset": 3648, "endOffset": 3691, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 3729, "endOffset": 3888, "count": 12}, {"startOffset": 3810, "endOffset": 3850, "count": 0}], "isBlockCoverage": true}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 3890, "endOffset": 4049, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4050, "endOffset": 4347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 4435, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 4478, "endOffset": 4626, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 4636, "endOffset": 6391, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6394, "endOffset": 7669, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "323", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 1}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 1}, {"startOffset": 1031, "endOffset": 1241, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 1}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 1}, {"startOffset": 1528, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "327", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 1}, {"startOffset": 735, "endOffset": 1054, "count": 3}, {"startOffset": 858, "endOffset": 888, "count": 0}, {"startOffset": 993, "endOffset": 1017, "count": 1}, {"startOffset": 1017, "endOffset": 1054, "count": 2}, {"startOffset": 1078, "endOffset": 1116, "count": 0}], "isBlockCoverage": true}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}