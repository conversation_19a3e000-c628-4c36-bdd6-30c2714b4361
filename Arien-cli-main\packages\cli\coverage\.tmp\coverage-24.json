{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useLoadingIndicator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17343, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17343, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1227, "endOffset": 6793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1274, "endOffset": 1331, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1372, "endOffset": 1517, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1460, "endOffset": 1511, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1984, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1677, "endOffset": 1771, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2059, "endOffset": 2957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2140, "endOffset": 2240, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2571, "endOffset": 2706, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3072, "endOffset": 4112, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 3163, "endOffset": 3248, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3389, "endOffset": 3476, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3588, "endOffset": 3698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3944, "endOffset": 4031, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4258, "endOffset": 5650, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 4349, "endOffset": 4434, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4575, "endOffset": 4662, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4773, "endOffset": 4883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5122, "endOffset": 5220, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5483, "endOffset": 5570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5769, "endOffset": 6789, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 5860, "endOffset": 5945, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6086, "endOffset": 6173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6285, "endOffset": 6377, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6622, "endOffset": 6709, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useLoadingIndicator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 24}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "useLoadingIndicator", "ranges": [{"startOffset": 1001, "endOffset": 2771, "count": 24}, {"startOffset": 2694, "endOffset": 2715, "count": 4}, {"startOffset": 2716, "endOffset": 2738, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1820, "endOffset": 2544, "count": 16}, {"startOffset": 1930, "endOffset": 1999, "count": 1}, {"startOffset": 2001, "endOffset": 2091, "count": 1}, {"startOffset": 2091, "endOffset": 2487, "count": 15}, {"startOffset": 2162, "endOffset": 2246, "count": 3}, {"startOffset": 2248, "endOffset": 2338, "count": 1}, {"startOffset": 2338, "endOffset": 2487, "count": 14}, {"startOffset": 2428, "endOffset": 2487, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2026, "endOffset": 2050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2273, "endOffset": 2297, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1326", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 156}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1327", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useTimer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 24}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTimer", "ranges": [{"startOffset": 600, "endOffset": 1828, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 942, "endOffset": 1780, "count": 11}, {"startOffset": 1029, "endOffset": 1108, "count": 2}, {"startOffset": 1152, "endOffset": 1163, "count": 3}, {"startOffset": 1165, "endOffset": 1202, "count": 1}, {"startOffset": 1229, "endOffset": 1261, "count": 3}, {"startOffset": 1321, "endOffset": 1512, "count": 6}, {"startOffset": 1351, "endOffset": 1401, "count": 0}, {"startOffset": 1512, "endOffset": 1637, "count": 5}, {"startOffset": 1548, "endOffset": 1631, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1440, "endOffset": 1499, "count": 91}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1471, "endOffset": 1489, "count": 91}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1650, "endOffset": 1775, "count": 11}, {"startOffset": 1686, "endOffset": 1769, "count": 6}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1330", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/usePhraseCycler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 5}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 428, "count": 1}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 474, "endOffset": 523, "count": 24}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "usePhraseCycler", "ranges": [{"startOffset": 6464, "endOffset": 7975, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6725, "endOffset": 7917, "count": 9}, {"startOffset": 6752, "endOffset": 6965, "count": 2}, {"startOffset": 6858, "endOffset": 6959, "count": 0}, {"startOffset": 6965, "endOffset": 7747, "count": 7}, {"startOffset": 6985, "endOffset": 7537, "count": 5}, {"startOffset": 7024, "endOffset": 7083, "count": 0}, {"startOffset": 7537, "endOffset": 7747, "count": 2}, {"startOffset": 7582, "endOffset": 7683, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7312, "endOffset": 7502, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7760, "endOffset": 7912, "count": 9}, {"startOffset": 7805, "endOffset": 7906, "count": 5}], "isBlockCoverage": true}], "startOffset": 209}]}