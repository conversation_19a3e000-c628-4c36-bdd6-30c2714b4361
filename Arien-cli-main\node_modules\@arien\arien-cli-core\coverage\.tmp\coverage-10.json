{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/logger.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 416, "endOffset": 458, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupLogAndCheckpointFiles", "ranges": [{"startOffset": 1523, "endOffset": 1694, "count": 49}, {"startOffset": 1672, "endOffset": 1692, "count": 0}], "isBlockCoverage": true}, {"functionName": "readLogFile", "ranges": [{"startOffset": 1695, "endOffset": 1957, "count": 8}, {"startOffset": 1823, "endOffset": 1860, "count": 6}, {"startOffset": 1860, "endOffset": 1955, "count": 2}, {"startOffset": 1933, "endOffset": 1955, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2003, "endOffset": 21817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2107, "endOffset": 2522, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2563, "endOffset": 2763, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2803, "endOffset": 2862, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2916, "endOffset": 8453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3027, "endOffset": 3516, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3124, "endOffset": 3134, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3142, "endOffset": 3153, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3306, "endOffset": 3316, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3324, "endOffset": 3335, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3631, "endOffset": 4967, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5105, "endOffset": 5731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5791, "endOffset": 6321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6435, "endOffset": 7379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6631, "endOffset": 6646, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7244, "endOffset": 7318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7495, "endOffset": 8447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7736, "endOffset": 7751, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8309, "endOffset": 8386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8507, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8617, "endOffset": 9379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9499, "endOffset": 10127, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10203, "endOffset": 10818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10427, "endOffset": 10442, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10940, "endOffset": 12488, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11965, "endOffset": 11987, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11994, "endOffset": 12009, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12139, "endOffset": 12174, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12180, "endOffset": 12196, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12609, "endOffset": 13404, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12844, "endOffset": 12859, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13477, "endOffset": 15618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13586, "endOffset": 14926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15017, "endOffset": 15217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15308, "endOffset": 15612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15676, "endOffset": 17388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15930, "endOffset": 16188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16292, "endOffset": 16736, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16821, "endOffset": 17382, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17045, "endOffset": 17060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17446, "endOffset": 20701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17636, "endOffset": 17793, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17901, "endOffset": 18042, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18146, "endOffset": 18753, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18864, "endOffset": 19013, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19127, "endOffset": 19330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19432, "endOffset": 20029, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19635, "endOffset": 19650, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20126, "endOffset": 20695, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20350, "endOffset": 20365, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20750, "endOffset": 21813, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20820, "endOffset": 21807, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21038, "endOffset": 21053, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/logger.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 21}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 37}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 922, "endOffset": 1017, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1062, "endOffset": 1227, "count": 37}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1303, "endOffset": 1363, "count": 37}], "isBlockCoverage": true}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1366, "endOffset": 2690, "count": 49}, {"startOffset": 1416, "endOffset": 1492, "count": 0}, {"startOffset": 1603, "endOffset": 1692, "count": 25}, {"startOffset": 1692, "endOffset": 1914, "count": 1}, {"startOffset": 1914, "endOffset": 2167, "count": 23}, {"startOffset": 2167, "endOffset": 2686, "count": 25}, {"startOffset": 2253, "endOffset": 2281, "count": 24}, {"startOffset": 2281, "endOffset": 2551, "count": 1}, {"startOffset": 2551, "endOffset": 2686, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1956, "endOffset": 2151, "count": 28}], "isBlockCoverage": true}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2693, "endOffset": 3041, "count": 2}, {"startOffset": 2760, "endOffset": 2767, "count": 0}, {"startOffset": 3009, "endOffset": 3037, "count": 0}], "isBlockCoverage": true}, {"functionName": "initialize", "ranges": [{"startOffset": 3044, "endOffset": 4220, "count": 34}, {"startOffset": 3091, "endOffset": 3112, "count": 1}, {"startOffset": 3112, "endOffset": 3594, "count": 33}, {"startOffset": 3594, "endOffset": 3603, "count": 9}, {"startOffset": 3603, "endOffset": 3652, "count": 24}, {"startOffset": 3652, "endOffset": 3722, "count": 33}, {"startOffset": 3722, "endOffset": 3747, "count": 24}, {"startOffset": 3749, "endOffset": 3847, "count": 24}, {"startOffset": 3847, "endOffset": 4002, "count": 33}, {"startOffset": 4002, "endOffset": 4064, "count": 1}, {"startOffset": 4065, "endOffset": 4068, "count": 32}, {"startOffset": 4107, "endOffset": 4216, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3901, "endOffset": 3946, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4032, "endOffset": 4058, "count": 2}], "isBlockCoverage": true}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4223, "endOffset": 5900, "count": 16}, {"startOffset": 4288, "endOffset": 4439, "count": 0}, {"startOffset": 4537, "endOffset": 4691, "count": 0}, {"startOffset": 4874, "endOffset": 4934, "count": 7}, {"startOffset": 4935, "endOffset": 4938, "count": 9}, {"startOffset": 5297, "endOffset": 5515, "count": 0}, {"startOffset": 5726, "endOffset": 5798, "count": 15}, {"startOffset": 5798, "endOffset": 5896, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4755, "endOffset": 4801, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4910, "endOffset": 4928, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5049, "endOffset": 5268, "count": 17}, {"startOffset": 5096, "endOffset": 5138, "count": 11}, {"startOffset": 5139, "endOffset": 5181, "count": 0}, {"startOffset": 5182, "endOffset": 5268, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 5903, "endOffset": 6240, "count": 4}, {"startOffset": 5964, "endOffset": 6239, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6003, "endOffset": 6035, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6053, "endOffset": 6204, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6210, "endOffset": 6234, "count": 4}], "isBlockCoverage": true}, {"functionName": "logMessage", "ranges": [{"startOffset": 6243, "endOffset": 6903, "count": 18}, {"startOffset": 6303, "endOffset": 6331, "count": 16}, {"startOffset": 6333, "endOffset": 6460, "count": 2}, {"startOffset": 6460, "endOffset": 6783, "count": 16}, {"startOffset": 6783, "endOffset": 6870, "count": 15}, {"startOffset": 6877, "endOffset": 6899, "count": 1}], "isBlockCoverage": true}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 6906, "endOffset": 7198, "count": 7}, {"startOffset": 6981, "endOffset": 7044, "count": 0}, {"startOffset": 7060, "endOffset": 7105, "count": 4}, {"startOffset": 7105, "endOffset": 7197, "count": 3}], "isBlockCoverage": true}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 7201, "endOffset": 7703, "count": 3}, {"startOffset": 7269, "endOffset": 7296, "count": 2}, {"startOffset": 7298, "endOffset": 7441, "count": 1}, {"startOffset": 7441, "endOffset": 7613, "count": 2}, {"startOffset": 7613, "endOffset": 7699, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 7706, "endOffset": 8588, "count": 6}, {"startOffset": 7760, "endOffset": 7787, "count": 5}, {"startOffset": 7789, "endOffset": 7933, "count": 1}, {"startOffset": 7933, "endOffset": 8078, "count": 5}, {"startOffset": 8078, "endOffset": 8173, "count": 3}, {"startOffset": 8173, "endOffset": 8329, "count": 0}, {"startOffset": 8329, "endOffset": 8365, "count": 2}, {"startOffset": 8365, "endOffset": 8584, "count": 3}, {"startOffset": 8451, "endOffset": 8479, "count": 2}, {"startOffset": 8479, "endOffset": 8584, "count": 1}], "isBlockCoverage": true}, {"functionName": "close", "ranges": [{"startOffset": 8591, "endOffset": 8776, "count": 40}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 33}, {"startOffset": 910, "endOffset": 918, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 33}], "isBlockCoverage": true}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 33}], "isBlockCoverage": true}], "startOffset": 209}]}