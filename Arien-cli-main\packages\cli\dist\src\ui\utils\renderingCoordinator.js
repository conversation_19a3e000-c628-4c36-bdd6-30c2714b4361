/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Utility class to coordinate rendering between Static and pending items
 * to prevent UI duplications and rendering conflicts.
 */
export class RenderingCoordinator {
    /**
     * Deduplicates pending items against existing history to prevent
     * rendering the same content in both Static and pending areas.
     */
    static deduplicatePendingItems(pendingItems, history) {
        // Create a set of content hashes from history for fast lookup
        const historyHashes = new Set(history.map(item => this.generateContentHash(item)));
        // Filter out pending items that duplicate history content
        const nonDuplicatedItems = pendingItems.filter(item => !historyHashes.has(this.generateContentHash(item)));
        // Also deduplicate within pending items themselves
        const seen = new Set();
        return nonDuplicatedItems.filter(item => {
            const hash = this.generateContentHash(item);
            if (seen.has(hash)) {
                return false;
            }
            seen.add(hash);
            return true;
        });
    }
    /**
     * Generates a unique hash for an item based on its content
     * to enable efficient deduplication comparisons.
     */
    static generateContentHash(item) {
        // For text-based items, use type + text
        if ('text' in item && item.text) {
            return `${item.type}:${item.text}`;
        }
        // For structured items, serialize the entire content
        const { id: _id, ...contentWithoutId } = item;
        return `${item.type}:${JSON.stringify(contentWithoutId)}`;
    }
    /**
     * Checks if two items represent the same content, ignoring IDs.
     */
    static areItemsEqual(item1, item2) {
        return this.generateContentHash(item1) === this.generateContentHash(item2);
    }
    /**
     * Optimizes item ordering for rendering performance.
     * Ensures that similar content types are grouped together.
     */
    static optimizeItemOrder(items) {
        // Sort items to group similar types together for better rendering performance
        return [...items].sort((a, b) => {
            // Prioritize user messages first
            if (a.type === 'user' && b.type !== 'user')
                return -1;
            if (b.type === 'user' && a.type !== 'user')
                return 1;
            // Then Arien responses
            if ((a.type === 'arien' || a.type === 'arien_content') &&
                !(b.type === 'arien' || b.type === 'arien_content'))
                return -1;
            if ((b.type === 'arien' || b.type === 'arien_content') &&
                !(a.type === 'arien' || a.type === 'arien_content'))
                return 1;
            // Then tool groups
            if (a.type === 'tool_group' && b.type !== 'tool_group')
                return -1;
            if (b.type === 'tool_group' && a.type !== 'tool_group')
                return 1;
            // Info and error messages last
            return 0;
        });
    }
    /**
     * Validates that pending items don't conflict with rendering constraints.
     */
    static validatePendingItems(pendingItems, maxPendingItems = 10) {
        // Limit the number of pending items to prevent performance issues
        if (pendingItems.length > maxPendingItems) {
            console.warn(`Too many pending items (${pendingItems.length}), limiting to ${maxPendingItems}`);
            return pendingItems.slice(-maxPendingItems);
        }
        // Filter out invalid items
        return pendingItems.filter(item => {
            // Ensure required properties exist
            if (!item.type) {
                console.warn('Pending item missing type:', item);
                return false;
            }
            // Validate text-based items
            if ('text' in item && typeof item.text !== 'string') {
                console.warn('Pending item has invalid text property:', item);
                return false;
            }
            // Validate tool group items
            if (item.type === 'tool_group' && !('tools' in item)) {
                console.warn('Tool group item missing tools property:', item);
                return false;
            }
            return true;
        });
    }
    /**
     * Creates a stable key for React rendering that helps prevent
     * unnecessary re-renders and maintains component state.
     */
    static generateStableKey(item, index, prefix = '') {
        const hash = this.generateContentHash(item);
        const timestamp = Date.now();
        return `${prefix}${hash.substring(0, 8)}-${index}-${timestamp}`;
    }
    /**
     * Checks if items require immediate static rendering (for performance).
     */
    static shouldRenderInStatic(item) {
        // Large text content should be rendered statically for performance
        if ('text' in item && item.text && item.text.length > 500) {
            return true;
        }
        // Completed tool groups should be static
        if (item.type === 'tool_group' && 'tools' in item) {
            return true;
        }
        // User messages should typically be static once confirmed
        if (item.type === 'user') {
            return true;
        }
        return false;
    }
}
//# sourceMappingURL=renderingCoordinator.js.map