{"version": 3, "file": "dependency-installer.js", "sourceRoot": "", "sources": ["../../../src/config/dependency-installer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAErC,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAgBlC;;GAEG;AACH,MAAM,OAAO,mBAAmB;IACtB,MAAM,CAAU,oBAAoB,GAAyC;QACnF,KAAK,EAAE;YACL;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,mCAAmC;gBAChD,cAAc,EAAE,gBAAgB;gBAChC,aAAa,EAAE,eAAe;aAC/B;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,oDAAoD;gBACjE,cAAc,EAAE,4DAA4D;gBAC5E,aAAa,EAAE,eAAe;aAC/B;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,iDAAiD;gBAC9D,cAAc,EAAE,iDAAiD;gBACjE,aAAa,EAAE,eAAe;aAC/B;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,gDAAgD;gBAC7D,cAAc,EAAE,qCAAqC;gBACrD,aAAa,EAAE,eAAe;aAC/B;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,+CAA+C;gBAC5D,cAAc,EAAE,kBAAkB;gBAClC,aAAa,EAAE,eAAe;aAC/B;SACF;QACD,KAAK,EAAE;YACL;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,+DAA+D;gBAC5E,cAAc,EAAE,wDAAwD;gBACxE,aAAa,EAAE,eAAe;aAC/B;SACF;QACD,KAAK,EAAE;YACL;gBACE,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,4CAA4C;gBACzD,cAAc,EAAE,qDAAqD;gBACrE,aAAa,EAAE,eAAe;aAC/B;SACF;KACF,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,OAAe,EACf,cAAuB,KAAK;QAE5B,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO;gBACP,OAAO,EAAE,4CAA4C,OAAO,GAAG;aAChE,CAAC;QACJ,CAAC;QAED,sCAAsC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE1D,+BAA+B;QAC/B,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,WAAW,KAAK,CAAC,CAAC;gBAEjE,4DAA4D;gBAC5D,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;oBAC3E,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,WAAW,+BAA+B,CAAC,CAAC;oBACrE,SAAS;gBACX,CAAC;gBAED,mCAAmC;gBACnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE;oBAChE,OAAO,EAAE,KAAK,EAAE,mBAAmB;oBACnC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;iBAChD,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAC;gBACjD,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;gBACtD,CAAC;gBAED,qCAAqC;gBACrC,IAAI,CAAC;oBACH,qEAAqE;oBACrE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;oBAExD,qCAAqC;oBACrC,MAAM,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE;wBACpC,OAAO,EAAE,KAAK;wBACd,GAAG,EAAE;4BACH,GAAG,OAAO,CAAC,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE;yBAC5B;qBACF,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO;wBACP,OAAO,EAAE,0BAA0B,MAAM,CAAC,WAAW,EAAE;qBACxD,CAAC;gBACJ,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC,uCAAuC,WAAW,EAAE,CAAC,CAAC;oBAEnE,oDAAoD;oBACpD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;wBACjC,IAAI,CAAC;4BACH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;4BACxD,MAAM,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE;gCACpC,OAAO,EAAE,KAAK;gCACd,GAAG,EAAE;oCACH,GAAG,OAAO,CAAC,GAAG;oCACd,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE;iCAC5B;6BACF,CAAC,CAAC;4BAEH,OAAO;gCACL,OAAO,EAAE,IAAI;gCACb,OAAO;gCACP,OAAO,EAAE,0BAA0B,MAAM,CAAC,WAAW,gBAAgB;6BACtE,CAAC;wBACJ,CAAC;wBAAC,OAAO,UAAU,EAAE,CAAC;4BACpB,OAAO,CAAC,IAAI,CAAC,6CAA6C,UAAU,EAAE,CAAC,CAAC;wBAC1E,CAAC;oBACH,CAAC;oBACD,SAAS;gBACX,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC;gBAC1E,SAAS;YACX,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO;YACP,OAAO,EAAE,sBAAsB,OAAO,2BAA2B;YACjE,KAAK,EAAE,kCAAkC;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAe;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnD,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,OAAe;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,+CAA+C,OAAO,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,2BAA2B,CACtC,QAAkB,EAClB,cAAuB,KAAK;QAE5B,MAAM,OAAO,GAA4C,EAAE,CAAC;QAE5D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;YAEpD,wCAAwC;YACxC,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3D,OAAO,CAAC,OAAO,CAAC,GAAG;oBACjB,OAAO,EAAE,IAAI;oBACb,OAAO;oBACP,OAAO,EAAE,GAAG,OAAO,uBAAuB;iBAC3C,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,uBAAuB,CAAC,CAAC;gBACjD,SAAS;YACX,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,mCAAmC;gBACnC,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,+CAA+C,CAAC,CAAC;YAC3E,CAAC;YAED,uBAAuB;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAClE,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;YAE1B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,OAAgD;QAClF,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,oBAAoB,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAE7C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACtB,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,8EAA8E,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,OAA6B;QAChE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAElC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,uCAAuC;YACvC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,mEAAmE;gBACnE,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBACnD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBACnD,OAAO,MAAM,GAAG,MAAM,CAAC;YACzB,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBAC/C,OAAO,MAAM,GAAG,MAAM,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,iDAAiD;gBACjD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBACjD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBACjD,OAAO,MAAM,GAAG,MAAM,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,WAAmB;QAChD,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,GAAG,CAAC;QACnD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,EAAE,CAAC;QAC9C,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,EAAE,CAAC;QAC7C,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAC3C,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,EAAE,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,WAAmB;QAC5C,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,CAAC;QAC7C,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAC3C,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,EAAE,CAAC;QAClD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,WAAmB;QAC9C,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,CAAC;QAC7C,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAC3C,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,EAAE,CAAC;QAClD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc;QAC3B,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QAC3C,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,oCAAoC;YACpC,eAAe,CAAC,IAAI,CAClB,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,eAAe,EACzC,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,yBAAyB,EACpD,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,mBAAmB,EACzC,gCAAgC,CACjC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,eAAe,CAAC,IAAI,CAClB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,EAChC,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,EAChC,gBAAgB,EAChB,mBAAmB,CACpB,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,OAAO,CAAC,WAAW,EAAE,GAAG,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC"}