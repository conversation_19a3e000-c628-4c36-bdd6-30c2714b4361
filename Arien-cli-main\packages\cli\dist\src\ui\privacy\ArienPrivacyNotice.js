import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
* @license
* Copyright 2025 Google LLC
* SPDX-License-Identifier: Apache-2.0
*/
import { Box, Newline, Text, useInput } from 'ink';
import { Colors } from '../colors.js';
export const ArienPrivacyNotice = ({ onExit }) => {
    useInput((input, key) => {
        if (key.escape) {
            onExit();
        }
    });
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Arien AI API Key Notice" }), _jsx(Newline, {}), _jsxs(Text, { children: ["By using the Arien AI API", _jsx(Text, { color: Colors.AccentBlue, children: "[1]" }), ", Arien AI Studio", _jsx(Text, { color: Colors.AccentRed, children: "[2]" }), ", and the other Arien AI developer services that reference these terms (collectively, the \"APIs\" or \"Services\"), you are agreeing to Arien AI APIs Terms of Service (the \"API Terms\")", _jsx(Text, { color: Colors.AccentGreen, children: "[3]" }), ", and the Arien AI Additional Terms of Service (the \"Additional Terms\")", _jsx(Text, { color: Colors.AccentPurple, children: "[4]" }), "."] }), _jsx(Newline, {}), _jsxs(Text, { children: [_jsx(Text, { color: Colors.AccentBlue, children: "[1]" }), ' ', "https://arien.ai/docs/api_overview"] }), _jsxs(Text, { children: [_jsx(Text, { color: Colors.AccentRed, children: "[2]" }), " https://studio.arien.ai/"] }), _jsxs(Text, { children: [_jsx(Text, { color: Colors.AccentGreen, children: "[3]" }), ' ', "https://arien.ai/terms"] }), _jsxs(Text, { children: [_jsx(Text, { color: Colors.AccentPurple, children: "[4]" }), ' ', "https://arien.ai/api/terms"] }), _jsx(Newline, {}), _jsx(Text, { color: Colors.Gray, children: "Press Esc to exit." })] }));
};
//# sourceMappingURL=ArienPrivacyNotice.js.map