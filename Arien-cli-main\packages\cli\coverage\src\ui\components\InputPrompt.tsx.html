
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/ui/components/InputPrompt.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/ui/components</a> InputPrompt.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/397</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/397</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >/**<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
&nbsp;
<span class="cstat-no" title="statement not covered" >import React, { useCallback, useEffect, useState } from 'react';</span>
<span class="cstat-no" title="statement not covered" >import { Box, Text } from 'ink';</span>
<span class="cstat-no" title="statement not covered" >import { Colors } from '../colors.js';</span>
<span class="cstat-no" title="statement not covered" >import { SuggestionsDisplay } from './SuggestionsDisplay.js';</span>
<span class="cstat-no" title="statement not covered" >import { useInputHistory } from '../hooks/useInputHistory.js';</span>
import { TextBuffer } from './shared/text-buffer.js';
<span class="cstat-no" title="statement not covered" >import { cpSlice, cpLen } from '../utils/textUtils.js';</span>
<span class="cstat-no" title="statement not covered" >import chalk from 'chalk';</span>
<span class="cstat-no" title="statement not covered" >import stringWidth from 'string-width';</span>
<span class="cstat-no" title="statement not covered" >import process from 'node:process';</span>
<span class="cstat-no" title="statement not covered" >import { useShellHistory } from '../hooks/useShellHistory.js';</span>
<span class="cstat-no" title="statement not covered" >import { useCompletion } from '../hooks/useCompletion.js';</span>
<span class="cstat-no" title="statement not covered" >import { useKeypress, Key } from '../hooks/useKeypress.js';</span>
<span class="cstat-no" title="statement not covered" >import { isAtCommand, isSlashCommand } from '../utils/commandUtils.js';</span>
import { SlashCommand } from '../hooks/slashCommandProcessor.js';
import { Config } from '@arien/arien-cli-core';
&nbsp;
export interface InputPromptProps {
  buffer: TextBuffer;
  onSubmit: (value: string) =&gt; void;
  userMessages: readonly string[];
  onClearScreen: () =&gt; void;
  config: Config; // Added config for useCompletion
  slashCommands: SlashCommand[]; // Added slashCommands for useCompletion
  placeholder?: string;
  focus?: boolean;
  inputWidth: number;
  suggestionsWidth: number;
  shellModeActive: boolean;
  setShellModeActive: (value: boolean) =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >export const InputPrompt: React.FC&lt;InputPromptProps&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  buffer,</span>
<span class="cstat-no" title="statement not covered" >  onSubmit,</span>
<span class="cstat-no" title="statement not covered" >  userMessages,</span>
<span class="cstat-no" title="statement not covered" >  onClearScreen,</span>
<span class="cstat-no" title="statement not covered" >  config,</span>
<span class="cstat-no" title="statement not covered" >  slashCommands,</span>
<span class="cstat-no" title="statement not covered" >  placeholder = '  Type your message or @path/to/file',</span>
<span class="cstat-no" title="statement not covered" >  focus = true,</span>
<span class="cstat-no" title="statement not covered" >  inputWidth,</span>
<span class="cstat-no" title="statement not covered" >  suggestionsWidth,</span>
<span class="cstat-no" title="statement not covered" >  shellModeActive,</span>
<span class="cstat-no" title="statement not covered" >  setShellModeActive,</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [justNavigatedHistory, setJustNavigatedHistory] = useState(false);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const completion = useCompletion(</span>
<span class="cstat-no" title="statement not covered" >    buffer.text,</span>
<span class="cstat-no" title="statement not covered" >    config.getTargetDir(),</span>
<span class="cstat-no" title="statement not covered" >    isAtCommand(buffer.text) || isSlashCommand(buffer.text),</span>
<span class="cstat-no" title="statement not covered" >    slashCommands,</span>
<span class="cstat-no" title="statement not covered" >    config,</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const resetCompletionState = completion.resetCompletionState;</span>
<span class="cstat-no" title="statement not covered" >  const shellHistory = useShellHistory(config.getProjectRoot());</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSubmitAndClear = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (submittedValue: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (shellModeActive) {</span>
<span class="cstat-no" title="statement not covered" >        shellHistory.addCommandToHistory(submittedValue);</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Clear the buffer *before* calling onSubmit to prevent potential re-submission
      // if onSubmit triggers a re-render while the buffer still holds the old value.
<span class="cstat-no" title="statement not covered" >      buffer.setText('');</span>
<span class="cstat-no" title="statement not covered" >      onSubmit(submittedValue);</span>
<span class="cstat-no" title="statement not covered" >      resetCompletionState();</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [onSubmit, buffer, resetCompletionState, shellModeActive, shellHistory],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const customSetTextAndResetCompletionSignal = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (newText: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      buffer.setText(newText);</span>
<span class="cstat-no" title="statement not covered" >      setJustNavigatedHistory(true);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [buffer, setJustNavigatedHistory],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const inputHistory = useInputHistory({</span>
<span class="cstat-no" title="statement not covered" >    userMessages,</span>
<span class="cstat-no" title="statement not covered" >    onSubmit: handleSubmitAndClear,</span>
<span class="cstat-no" title="statement not covered" >    isActive: !completion.showSuggestions &amp;&amp; !shellModeActive,</span>
<span class="cstat-no" title="statement not covered" >    currentQuery: buffer.text,</span>
<span class="cstat-no" title="statement not covered" >    onChange: customSetTextAndResetCompletionSignal,</span>
<span class="cstat-no" title="statement not covered" >  });</span>
&nbsp;
  // Effect to reset completion if history navigation just occurred and set the text
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (justNavigatedHistory) {</span>
<span class="cstat-no" title="statement not covered" >      resetCompletionState();</span>
<span class="cstat-no" title="statement not covered" >      setJustNavigatedHistory(false);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [</span>
<span class="cstat-no" title="statement not covered" >    justNavigatedHistory,</span>
<span class="cstat-no" title="statement not covered" >    buffer.text,</span>
<span class="cstat-no" title="statement not covered" >    resetCompletionState,</span>
<span class="cstat-no" title="statement not covered" >    setJustNavigatedHistory,</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const completionSuggestions = completion.suggestions;</span>
<span class="cstat-no" title="statement not covered" >  const handleAutocomplete = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (indexToUse: number) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (indexToUse &lt; 0 || indexToUse &gt;= completionSuggestions.length) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      const query = buffer.text;</span>
<span class="cstat-no" title="statement not covered" >      const selectedSuggestion = completionSuggestions[indexToUse];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (query.trimStart().startsWith('/')) {</span>
<span class="cstat-no" title="statement not covered" >        const parts = query.trimStart().substring(1).split(' ');</span>
<span class="cstat-no" title="statement not covered" >        const commandName = parts[0];</span>
<span class="cstat-no" title="statement not covered" >        const slashIndex = query.indexOf('/');</span>
<span class="cstat-no" title="statement not covered" >        const base = query.substring(0, slashIndex + 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const command = slashCommands.find((cmd) =&gt; cmd.name === commandName);</span>
<span class="cstat-no" title="statement not covered" >        if (command &amp;&amp; command.completion) {</span>
<span class="cstat-no" title="statement not covered" >          const newValue = `${base}${commandName} ${selectedSuggestion.value}`;</span>
<span class="cstat-no" title="statement not covered" >          buffer.setText(newValue);</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >          const newValue = base + selectedSuggestion.value;</span>
<span class="cstat-no" title="statement not covered" >          buffer.setText(newValue);</span>
<span class="cstat-no" title="statement not covered" >          handleSubmitAndClear(newValue);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        const atIndex = query.lastIndexOf('@');</span>
<span class="cstat-no" title="statement not covered" >        if (atIndex === -1) return;</span>
<span class="cstat-no" title="statement not covered" >        const pathPart = query.substring(atIndex + 1);</span>
<span class="cstat-no" title="statement not covered" >        const lastSlashIndexInPath = pathPart.lastIndexOf('/');</span>
<span class="cstat-no" title="statement not covered" >        let autoCompleteStartIndex = atIndex + 1;</span>
<span class="cstat-no" title="statement not covered" >        if (lastSlashIndexInPath !== -1) {</span>
<span class="cstat-no" title="statement not covered" >          autoCompleteStartIndex += lastSlashIndexInPath + 1;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        buffer.replaceRangeByOffset(</span>
<span class="cstat-no" title="statement not covered" >          autoCompleteStartIndex,</span>
<span class="cstat-no" title="statement not covered" >          buffer.text.length,</span>
<span class="cstat-no" title="statement not covered" >          selectedSuggestion.value,</span>
<span class="cstat-no" title="statement not covered" >        );</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      resetCompletionState();</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [</span>
<span class="cstat-no" title="statement not covered" >      resetCompletionState,</span>
<span class="cstat-no" title="statement not covered" >      handleSubmitAndClear,</span>
<span class="cstat-no" title="statement not covered" >      buffer,</span>
<span class="cstat-no" title="statement not covered" >      completionSuggestions,</span>
<span class="cstat-no" title="statement not covered" >      slashCommands,</span>
<span class="cstat-no" title="statement not covered" >    ],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleInput = useCallback(</span>
<span class="cstat-no" title="statement not covered" >    (key: Key) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (!focus) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      const query = buffer.text;</span>
&nbsp;
      // Handle paste events first to ensure they don't get intercepted by other key handlers
<span class="cstat-no" title="statement not covered" >      if (key.paste &amp;&amp; key.sequence) {</span>
<span class="cstat-no" title="statement not covered" >        buffer.handleInput(key);</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (key.sequence === '!' &amp;&amp; query === '' &amp;&amp; !completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        setShellModeActive(!shellModeActive);</span>
<span class="cstat-no" title="statement not covered" >        buffer.setText(''); // Clear the '!' from input</span>
<span class="cstat-no" title="statement not covered" >        return true;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        if (key.name === 'up') {</span>
<span class="cstat-no" title="statement not covered" >          completion.navigateUp();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.name === 'down') {</span>
<span class="cstat-no" title="statement not covered" >          completion.navigateDown();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.name === 'tab') {</span>
<span class="cstat-no" title="statement not covered" >          if (completion.suggestions.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            const targetIndex =</span>
<span class="cstat-no" title="statement not covered" >              completion.activeSuggestionIndex === -1</span>
<span class="cstat-no" title="statement not covered" >                ? 0</span>
<span class="cstat-no" title="statement not covered" >                : completion.activeSuggestionIndex;</span>
<span class="cstat-no" title="statement not covered" >            if (targetIndex &lt; completion.suggestions.length) {</span>
<span class="cstat-no" title="statement not covered" >              handleAutocomplete(targetIndex);</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.name === 'return') {</span>
<span class="cstat-no" title="statement not covered" >          if (completion.activeSuggestionIndex &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >            handleAutocomplete(completion.activeSuggestionIndex);</span>
<span class="cstat-no" title="statement not covered" >          } else if (query.trim()) {</span>
<span class="cstat-no" title="statement not covered" >            handleSubmitAndClear(query);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
        // Keybindings when suggestions are not shown
<span class="cstat-no" title="statement not covered" >        if (key.ctrl &amp;&amp; key.name === 'l') {</span>
<span class="cstat-no" title="statement not covered" >          onClearScreen();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.ctrl &amp;&amp; key.name === 'p') {</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateUp();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.ctrl &amp;&amp; key.name === 'n') {</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateDown();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (key.name === 'escape') {</span>
<span class="cstat-no" title="statement not covered" >          if (shellModeActive) {</span>
<span class="cstat-no" title="statement not covered" >            setShellModeActive(false);</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          completion.resetCompletionState();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Ctrl+A (Home)
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'a') {</span>
<span class="cstat-no" title="statement not covered" >        buffer.move('home');</span>
<span class="cstat-no" title="statement not covered" >        buffer.moveToOffset(0);</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Ctrl+E (End)
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'e') {</span>
<span class="cstat-no" title="statement not covered" >        buffer.move('end');</span>
<span class="cstat-no" title="statement not covered" >        buffer.moveToOffset(cpLen(buffer.text));</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Ctrl+L (Clear Screen)
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'l') {</span>
<span class="cstat-no" title="statement not covered" >        onClearScreen();</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Ctrl+P (History Up)
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'p' &amp;&amp; !completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        inputHistory.navigateUp();</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Ctrl+N (History Down)
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'n' &amp;&amp; !completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        inputHistory.navigateDown();</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Core text editing from MultilineTextEditor's useInput
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'k') {</span>
<span class="cstat-no" title="statement not covered" >        buffer.killLineRight();</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      if (key.ctrl &amp;&amp; key.name === 'u') {</span>
<span class="cstat-no" title="statement not covered" >        buffer.killLineLeft();</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      const isCtrlX =</span>
<span class="cstat-no" title="statement not covered" >        (key.ctrl &amp;&amp; (key.name === 'x' || key.sequence === '\x18')) ||</span>
<span class="cstat-no" title="statement not covered" >        key.sequence === '\x18';</span>
<span class="cstat-no" title="statement not covered" >      const isCtrlEFromEditor =</span>
<span class="cstat-no" title="statement not covered" >        (key.ctrl &amp;&amp; (key.name === 'e' || key.sequence === '\x05')) ||</span>
<span class="cstat-no" title="statement not covered" >        key.sequence === '\x05' ||</span>
<span class="cstat-no" title="statement not covered" >        (!key.ctrl &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          key.name === 'e' &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          key.sequence.length === 1 &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          key.sequence.charCodeAt(0) === 5);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isCtrlX || isCtrlEFromEditor) {</span>
<span class="cstat-no" title="statement not covered" >        if (isCtrlEFromEditor &amp;&amp; !(key.ctrl &amp;&amp; key.name === 'e')) {</span>
          // Avoid double handling Ctrl+E
<span class="cstat-no" title="statement not covered" >          buffer.openInExternalEditor();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (isCtrlX) {</span>
<span class="cstat-no" title="statement not covered" >          buffer.openInExternalEditor();</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (</span>
<span class="cstat-no" title="statement not covered" >        process.env['TEXTBUFFER_DEBUG'] === '1' ||</span>
<span class="cstat-no" title="statement not covered" >        process.env['TEXTBUFFER_DEBUG'] === 'true'</span>
<span class="cstat-no" title="statement not covered" >      ) {</span>
<span class="cstat-no" title="statement not covered" >        console.log('[InputPromptCombined] event', { key });</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Ctrl+Enter for newline, Enter for submit
<span class="cstat-no" title="statement not covered" >      if (key.name === 'return') {</span>
<span class="cstat-no" title="statement not covered" >        const [row, col] = buffer.cursor;</span>
<span class="cstat-no" title="statement not covered" >        const line = buffer.lines[row];</span>
<span class="cstat-no" title="statement not covered" >        const charBefore = col &gt; 0 ? cpSlice(line, col - 1, col) : '';</span>
<span class="cstat-no" title="statement not covered" >        if (key.ctrl || key.meta || charBefore === '\\' || key.paste) {</span>
          // Ctrl+Enter or escaped newline
<span class="cstat-no" title="statement not covered" >          if (charBefore === '\\') {</span>
<span class="cstat-no" title="statement not covered" >            buffer.backspace();</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          buffer.newline();</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
          // Enter for submit
<span class="cstat-no" title="statement not covered" >          if (query.trim()) {</span>
<span class="cstat-no" title="statement not covered" >            handleSubmitAndClear(query);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Standard arrow navigation within the buffer
<span class="cstat-no" title="statement not covered" >      if (key.name === 'up' &amp;&amp; !completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        if (shellModeActive) {</span>
<span class="cstat-no" title="statement not covered" >          const prevCommand = shellHistory.getPreviousCommand();</span>
<span class="cstat-no" title="statement not covered" >          if (prevCommand !== null) {</span>
<span class="cstat-no" title="statement not covered" >            buffer.setText(prevCommand);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          (buffer.allVisualLines.length === 1 || // Always navigate for single line</span>
<span class="cstat-no" title="statement not covered" >            (buffer.visualCursor[0] === 0 &amp;&amp; buffer.visualScrollRow === 0)) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateUp</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateUp();</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >          buffer.move('up');</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      if (key.name === 'down' &amp;&amp; !completion.showSuggestions) {</span>
<span class="cstat-no" title="statement not covered" >        if (shellModeActive) {</span>
<span class="cstat-no" title="statement not covered" >          const nextCommand = shellHistory.getNextCommand();</span>
<span class="cstat-no" title="statement not covered" >          if (nextCommand !== null) {</span>
<span class="cstat-no" title="statement not covered" >            buffer.setText(nextCommand);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (</span>
<span class="cstat-no" title="statement not covered" >          (buffer.allVisualLines.length === 1 || // Always navigate for single line</span>
<span class="cstat-no" title="statement not covered" >            buffer.visualCursor[0] === buffer.allVisualLines.length - 1) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateDown</span>
<span class="cstat-no" title="statement not covered" >        ) {</span>
<span class="cstat-no" title="statement not covered" >          inputHistory.navigateDown();</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >          buffer.move('down');</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Fallback to buffer's default input handling
<span class="cstat-no" title="statement not covered" >      buffer.handleInput(key);</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    [</span>
<span class="cstat-no" title="statement not covered" >      focus,</span>
<span class="cstat-no" title="statement not covered" >      buffer,</span>
<span class="cstat-no" title="statement not covered" >      completion,</span>
<span class="cstat-no" title="statement not covered" >      shellModeActive,</span>
<span class="cstat-no" title="statement not covered" >      setShellModeActive,</span>
<span class="cstat-no" title="statement not covered" >      onClearScreen,</span>
<span class="cstat-no" title="statement not covered" >      inputHistory,</span>
<span class="cstat-no" title="statement not covered" >      handleAutocomplete,</span>
<span class="cstat-no" title="statement not covered" >      handleSubmitAndClear,</span>
<span class="cstat-no" title="statement not covered" >      shellHistory,</span>
<span class="cstat-no" title="statement not covered" >    ],</span>
<span class="cstat-no" title="statement not covered" >  );</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useKeypress(handleInput, { isActive: focus });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const linesToRender = buffer.viewportVisualLines;</span>
<span class="cstat-no" title="statement not covered" >  const [cursorVisualRowAbsolute, cursorVisualColAbsolute] =</span>
<span class="cstat-no" title="statement not covered" >    buffer.visualCursor;</span>
<span class="cstat-no" title="statement not covered" >  const scrollVisualRow = buffer.visualScrollRow;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;Box</span>
<span class="cstat-no" title="statement not covered" >        borderStyle="round"</span>
<span class="cstat-no" title="statement not covered" >        borderColor={</span>
<span class="cstat-no" title="statement not covered" >          completion.showSuggestions</span>
<span class="cstat-no" title="statement not covered" >            ? Colors.AccentCyan</span>
<span class="cstat-no" title="statement not covered" >            : shellModeActive</span>
<span class="cstat-no" title="statement not covered" >            ? Colors.AccentYellow</span>
<span class="cstat-no" title="statement not covered" >            : Colors.AccentBlue</span>
        }
<span class="cstat-no" title="statement not covered" >        paddingX={1}</span>
      &gt;
<span class="cstat-no" title="statement not covered" >        &lt;Text</span>
<span class="cstat-no" title="statement not covered" >          color={</span>
<span class="cstat-no" title="statement not covered" >            completion.showSuggestions</span>
<span class="cstat-no" title="statement not covered" >              ? Colors.AccentCyan</span>
<span class="cstat-no" title="statement not covered" >              : shellModeActive</span>
<span class="cstat-no" title="statement not covered" >              ? Colors.AccentYellow</span>
<span class="cstat-no" title="statement not covered" >              : Colors.AccentPurple</span>
          }
        &gt;
<span class="cstat-no" title="statement not covered" >          {completion.showSuggestions</span>
<span class="cstat-no" title="statement not covered" >            ? '&gt; '</span>
<span class="cstat-no" title="statement not covered" >            : shellModeActive</span>
<span class="cstat-no" title="statement not covered" >            ? '! '</span>
<span class="cstat-no" title="statement not covered" >            : '&gt; '}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Text&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Box flexGrow={1} flexDirection="column"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {buffer.text.length === 0 &amp;&amp; placeholder ? (</span>
<span class="cstat-no" title="statement not covered" >            focus ? (</span>
<span class="cstat-no" title="statement not covered" >              &lt;Text&gt;</span>
<span class="cstat-no" title="statement not covered" >                {chalk.inverse(placeholder.slice(0, 1))}</span>
<span class="cstat-no" title="statement not covered" >                &lt;Text color={Colors.Gray}&gt;{placeholder.slice(1)}&lt;/Text&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Text&gt;</span>
            ) : (
<span class="cstat-no" title="statement not covered" >              &lt;Text color={Colors.Gray}&gt;{placeholder}&lt;/Text&gt;</span>
            )
          ) : (
<span class="cstat-no" title="statement not covered" >            linesToRender.map((lineText, visualIdxInRenderedSet) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              const cursorVisualRow = cursorVisualRowAbsolute - scrollVisualRow;</span>
<span class="cstat-no" title="statement not covered" >              let display = cpSlice(lineText, 0, inputWidth);</span>
<span class="cstat-no" title="statement not covered" >              const currentVisualWidth = stringWidth(display);</span>
<span class="cstat-no" title="statement not covered" >              if (currentVisualWidth &lt; inputWidth) {</span>
<span class="cstat-no" title="statement not covered" >                display = display + ' '.repeat(inputWidth - currentVisualWidth);</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              if (visualIdxInRenderedSet === cursorVisualRow) {</span>
<span class="cstat-no" title="statement not covered" >                const relativeVisualColForHighlight = cursorVisualColAbsolute;</span>
<span class="cstat-no" title="statement not covered" >                if (relativeVisualColForHighlight &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >                  if (relativeVisualColForHighlight &lt; cpLen(display)) {</span>
<span class="cstat-no" title="statement not covered" >                    const charToHighlight =</span>
<span class="cstat-no" title="statement not covered" >                      cpSlice(</span>
<span class="cstat-no" title="statement not covered" >                        display,</span>
<span class="cstat-no" title="statement not covered" >                        relativeVisualColForHighlight,</span>
<span class="cstat-no" title="statement not covered" >                        relativeVisualColForHighlight + 1,</span>
<span class="cstat-no" title="statement not covered" >                      ) || ' ';</span>
<span class="cstat-no" title="statement not covered" >                    const highlighted = chalk.inverse(charToHighlight);</span>
<span class="cstat-no" title="statement not covered" >                    display =</span>
<span class="cstat-no" title="statement not covered" >                      cpSlice(display, 0, relativeVisualColForHighlight) +</span>
<span class="cstat-no" title="statement not covered" >                      highlighted +</span>
<span class="cstat-no" title="statement not covered" >                      cpSlice(display, relativeVisualColForHighlight + 1);</span>
<span class="cstat-no" title="statement not covered" >                  } else if (</span>
<span class="cstat-no" title="statement not covered" >                    relativeVisualColForHighlight === cpLen(display) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >                    cpLen(display) === inputWidth</span>
<span class="cstat-no" title="statement not covered" >                  ) {</span>
<span class="cstat-no" title="statement not covered" >                    display = display + chalk.inverse(' ');</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              return (</span>
<span class="cstat-no" title="statement not covered" >                &lt;Text key={`line-${visualIdxInRenderedSet}`}&gt;{display}&lt;/Text&gt;</span>
              );
<span class="cstat-no" title="statement not covered" >            })</span>
          )}
<span class="cstat-no" title="statement not covered" >        &lt;/Box&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/Box&gt;</span>
<span class="cstat-no" title="statement not covered" >      {completion.showSuggestions &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;Box&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;SuggestionsDisplay</span>
<span class="cstat-no" title="statement not covered" >            suggestions={completion.suggestions}</span>
<span class="cstat-no" title="statement not covered" >            activeIndex={completion.activeSuggestionIndex}</span>
<span class="cstat-no" title="statement not covered" >            isLoading={completion.isLoadingSuggestions}</span>
<span class="cstat-no" title="statement not covered" >            width={suggestionsWidth}</span>
<span class="cstat-no" title="statement not covered" >            scrollOffset={completion.visibleStartIndex}</span>
<span class="cstat-no" title="statement not covered" >            userInput={buffer.text}</span>
<span class="cstat-no" title="statement not covered" >          /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Box&gt;</span>
      )}
<span class="cstat-no" title="statement not covered" >    &lt;/&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-02T11:18:45.867Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    