{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/turn.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 399, "endOffset": 680, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 740, "endOffset": 796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 874, "endOffset": 996, "count": 1}], "isBlockCoverage": true}, {"functionName": "getResponseText", "ranges": [{"startOffset": 902, "endOffset": 993, "count": 9}, {"startOffset": 927, "endOffset": 953, "count": 5}, {"startOffset": 984, "endOffset": 993, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 973, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1419, "endOffset": 12649, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1912, "endOffset": 2252, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2218, "endOffset": 2244, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2293, "endOffset": 2352, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2407, "endOffset": 2677, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2505, "endOffset": 2671, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2724, "endOffset": 11904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2811, "endOffset": 3861, "count": 1}, {"startOffset": 3320, "endOffset": 3357, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2858, "endOffset": 3077, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3957, "endOffset": 5894, "count": 1}, {"startOffset": 4609, "endOffset": 4646, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4004, "endOffset": 4359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5987, "endOffset": 7010, "count": 1}, {"startOffset": 6676, "endOffset": 6713, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6087, "endOffset": 6455, "count": 1}, {"startOffset": 6447, "endOffset": 6454, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7113, "endOffset": 8254, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8351, "endOffset": 9583, "count": 1}, {"startOffset": 9001, "endOffset": 9038, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8398, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockResponseStream.functionCalls.name", "ranges": [{"startOffset": 8589, "endOffset": 8625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9679, "endOffset": 11898, "count": 1}, {"startOffset": 10261, "endOffset": 10298, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9726, "endOffset": 9995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11965, "endOffset": 12645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12049, "endOffset": 12639, "count": 1}, {"startOffset": 12533, "endOffset": 12542, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12273, "endOffset": 12343, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/turn.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 11}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 8}, {"startOffset": 362, "endOffset": 370, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 932, "endOffset": 1463, "count": 1}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1503, "endOffset": 1610, "count": 8}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1613, "endOffset": 1675, "count": 8}], "isBlockCoverage": true}, {"functionName": "run", "ranges": [{"startOffset": 1747, "endOffset": 4503, "count": 7}, {"startOffset": 1971, "endOffset": 2019, "count": 6}, {"startOffset": 2019, "endOffset": 3302, "count": 10}, {"startOffset": 2050, "endOffset": 2143, "count": 1}, {"startOffset": 2143, "endOffset": 2228, "count": 9}, {"startOffset": 2228, "endOffset": 2254, "count": 5}, {"startOffset": 2279, "endOffset": 2288, "count": 5}, {"startOffset": 2290, "endOffset": 2792, "count": 0}, {"startOffset": 2792, "endOffset": 2882, "count": 9}, {"startOffset": 2882, "endOffset": 2957, "count": 5}, {"startOffset": 2957, "endOffset": 3008, "count": 9}, {"startOffset": 3008, "endOffset": 3013, "count": 5}, {"startOffset": 3059, "endOffset": 3194, "count": 7}, {"startOffset": 3194, "endOffset": 3228, "count": 9}, {"startOffset": 3228, "endOffset": 3294, "count": 2}, {"startOffset": 3302, "endOffset": 3338, "count": 5}, {"startOffset": 3338, "endOffset": 3549, "count": 1}, {"startOffset": 3556, "endOffset": 4499, "count": 1}, {"startOffset": 3702, "endOffset": 3732, "count": 0}, {"startOffset": 3760, "endOffset": 3847, "count": 0}, {"startOffset": 4218, "endOffset": 4253, "count": 0}, {"startOffset": 4254, "endOffset": 4268, "count": 0}], "isBlockCoverage": true}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 4506, "endOffset": 4982, "count": 7}, {"startOffset": 4571, "endOffset": 4643, "count": 3}, {"startOffset": 4674, "endOffset": 4698, "count": 2}, {"startOffset": 4729, "endOffset": 4734, "count": 4}], "isBlockCoverage": true}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 4985, "endOffset": 5042, "count": 7}], "isBlockCoverage": true}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 5045, "endOffset": 5104, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 1}, {"startOffset": 1144, "endOffset": 1237, "count": 0}], "isBlockCoverage": true}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 1}, {"startOffset": 1457, "endOffset": 1861, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}