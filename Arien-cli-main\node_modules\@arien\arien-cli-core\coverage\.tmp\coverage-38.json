{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/arienRequest.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10510, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10510, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 603, "endOffset": 3880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 849, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 958, "endOffset": 1128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1193, "endOffset": 1389, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1448, "endOffset": 1639, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1710, "endOffset": 1919, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1985, "endOffset": 2183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2243, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2548, "endOffset": 2774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2842, "endOffset": 3100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3162, "endOffset": 3386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3442, "endOffset": 3623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3715, "endOffset": 3876, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/arienRequest.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 12}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1365, "count": 15}, {"startOffset": 477, "endOffset": 500, "count": 4}, {"startOffset": 500, "endOffset": 530, "count": 11}, {"startOffset": 530, "endOffset": 589, "count": 1}, {"startOffset": 589, "endOffset": 650, "count": 10}, {"startOffset": 650, "endOffset": 686, "count": 1}, {"startOffset": 686, "endOffset": 719, "count": 9}, {"startOffset": 719, "endOffset": 765, "count": 1}, {"startOffset": 765, "endOffset": 810, "count": 8}, {"startOffset": 810, "endOffset": 853, "count": 1}, {"startOffset": 853, "endOffset": 893, "count": 7}, {"startOffset": 893, "endOffset": 930, "count": 1}, {"startOffset": 930, "endOffset": 964, "count": 6}, {"startOffset": 964, "endOffset": 995, "count": 1}, {"startOffset": 995, "endOffset": 1033, "count": 5}, {"startOffset": 1033, "endOffset": 1095, "count": 1}, {"startOffset": 1095, "endOffset": 1137, "count": 4}, {"startOffset": 1137, "endOffset": 1207, "count": 1}, {"startOffset": 1207, "endOffset": 1243, "count": 3}, {"startOffset": 1243, "endOffset": 1292, "count": 1}, {"startOffset": 1292, "endOffset": 1322, "count": 2}, {"startOffset": 1322, "endOffset": 1364, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}