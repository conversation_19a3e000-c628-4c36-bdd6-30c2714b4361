{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/oauth2.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13642, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13642, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 388, "endOffset": 526, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1415, "endOffset": 4780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1481, "endOffset": 1714, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1841, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1905, "endOffset": 4776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2677, "endOffset": 2699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2885, "endOffset": 2901, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3016, "endOffset": 3062, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3167, "endOffset": 3323, "count": 1}, {"startOffset": 3240, "endOffset": 3273, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3367, "endOffset": 3446, "count": 1}, {"startOffset": 3405, "endOffset": 3438, "count": 0}], "isBlockCoverage": true}, {"functionName": "address", "ranges": [{"startOffset": 3505, "endOffset": 3535, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3636, "endOffset": 3708, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/oauth2.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 2061, "endOffset": 2667, "count": 1}, {"startOffset": 2271, "endOffset": 2295, "count": 0}], "isBlockCoverage": true}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 2669, "endOffset": 4557, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3076, "endOffset": 4499, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3153, "endOffset": 4468, "count": 1}, {"startOffset": 3242, "endOffset": 3412, "count": 0}, {"startOffset": 3548, "endOffset": 3735, "count": 0}, {"startOffset": 3772, "endOffset": 3911, "count": 0}, {"startOffset": 4299, "endOffset": 4373, "count": 0}, {"startOffset": 4382, "endOffset": 4420, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 4558, "endOffset": 5065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4609, "endOffset": 5061, "count": 1}, {"startOffset": 5023, "endOffset": 5057, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4860, "endOffset": 4923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4951, "endOffset": 4967, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4995, "endOffset": 5014, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 5067, "endOffset": 5536, "count": 1}, {"startOffset": 5297, "endOffset": 5501, "count": 0}], "isBlockCoverage": true}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 5537, "endOffset": 5878, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 5879, "endOffset": 6027, "count": 2}], "isBlockCoverage": true}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 6028, "endOffset": 6174, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}