{"version": 3, "file": "renderingCoordinator.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/renderingCoordinator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAC/B;;;OAGG;IACH,MAAM,CAAC,uBAAuB,CAC5B,YAAoC,EACpC,OAAsB;QAEtB,8DAA8D;QAC9D,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CACpD,CAAC;QAEF,0DAA0D;QAC1D,MAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAC5C,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;QAEF,mDAAmD;QACnD,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,mBAAmB,CAAC,IAAwC;QACzE,wCAAwC;QACxC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACrC,CAAC;QAED,qDAAqD;QACrD,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,gBAAgB,EAAE,GAAG,IAAmB,CAAC;QAC7D,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,KAAyC,EACzC,KAAyC;QAEzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,iBAAiB,CAAC,KAA6B;QACpD,8EAA8E;QAC9E,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,iCAAiC;YACjC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM;gBAAE,OAAO,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM;gBAAE,OAAO,CAAC,CAAC;YAErD,uBAAuB;YACvB,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC;gBAClD,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC;gBAAE,OAAO,CAAC,CAAC,CAAC;YACnE,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC;gBAClD,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC;gBAAE,OAAO,CAAC,CAAC;YAElE,mBAAmB;YACnB,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAO,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAO,CAAC,CAAC;YAEjE,+BAA+B;YAC/B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,YAAoC,EACpC,kBAA0B,EAAE;QAE5B,kEAAkE;QAClE,IAAI,YAAY,CAAC,MAAM,GAAG,eAAe,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,2BAA2B,YAAY,CAAC,MAAM,kBAAkB,eAAe,EAAE,CAAC,CAAC;YAChG,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,2BAA2B;QAC3B,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAChC,mCAAmC;YACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;gBACjD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4BAA4B;YAC5B,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,yCAAyC,EAAE,IAAI,CAAC,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4BAA4B;YAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC,yCAAyC,EAAE,IAAI,CAAC,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,iBAAiB,CACtB,IAAwC,EACxC,KAAa,EACb,SAAiB,EAAE;QAEnB,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAA0B;QACpD,mEAAmE;QACnE,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}