{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/App.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 497, "endOffset": 4083, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 645, "endOffset": 3890, "count": 10}, {"startOffset": 809, "endOffset": 840, "count": 0}, {"startOffset": 903, "endOffset": 917, "count": 0}, {"startOffset": 1549, "endOffset": 1557, "count": 0}, {"startOffset": 1648, "endOffset": 1673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1720, "endOffset": 1751, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1798, "endOffset": 1846, "count": 30}, {"startOffset": 1815, "endOffset": 1846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1895, "endOffset": 1913, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1964, "endOffset": 1999, "count": 40}, {"startOffset": 1985, "endOffset": 1999, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2053, "endOffset": 2063, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2135, "endOffset": 2164, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2214, "endOffset": 2233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2286, "endOffset": 2317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2368, "endOffset": 2388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2538, "endOffset": 2564, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2622, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2701, "endOffset": 2722, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2773, "endOffset": 2809, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2861, "endOffset": 2888, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2998, "endOffset": 3030, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3142, "endOffset": 3205, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3309, "endOffset": 3328, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3385, "endOffset": 3420, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3475, "endOffset": 3505, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3558, "endOffset": 3580, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3633, "endOffset": 3643, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3705, "endOffset": 3737, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3798, "endOffset": 3816, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4057, "endOffset": 4075, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4142, "endOffset": 4339, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4197, "endOffset": 4335, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4398, "endOffset": 4658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4453, "endOffset": 4654, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4712, "endOffset": 4860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4762, "endOffset": 4856, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4916, "endOffset": 5174, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6074, "endOffset": 17550, "count": 1}], "isBlockCoverage": true}, {"functionName": "createMockSettings", "ranges": [{"startOffset": 6171, "endOffset": 6515, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6556, "endOffset": 7237, "count": 10}, {"startOffset": 7026, "endOffset": 7113, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7094, "endOffset": 7105, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7278, "endOffset": 7423, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7551, "endOffset": 8350, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8482, "endOffset": 9282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9391, "endOffset": 10298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10430, "endOffset": 11352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11465, "endOffset": 12379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12502, "endOffset": 13404, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13508, "endOffset": 14365, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14464, "endOffset": 15353, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15417, "endOffset": 17546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15491, "endOffset": 15725, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15768, "endOffset": 15843, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15933, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16703, "endOffset": 17540, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/App.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 114724, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 114724, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 10}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "AppWrapper", "ranges": [{"startOffset": 7527, "endOffset": 8044, "count": 10}], "isBlockCoverage": true}, {"functionName": "App", "ranges": [{"startOffset": 8058, "endOffset": 49215, "count": 20}, {"startOffset": 22588, "endOffset": 22601, "count": 0}, {"startOffset": 24831, "endOffset": 25699, "count": 0}, {"startOffset": 27122, "endOffset": 27461, "count": 0}, {"startOffset": 28623, "endOffset": 31171, "count": 0}, {"startOffset": 31186, "endOffset": 31480, "count": 0}, {"startOffset": 31660, "endOffset": 32607, "count": 0}, {"startOffset": 32633, "endOffset": 34274, "count": 2}, {"startOffset": 32773, "endOffset": 33436, "count": 0}, {"startOffset": 33758, "endOffset": 33766, "count": 0}, {"startOffset": 34275, "endOffset": 45366, "count": 18}, {"startOffset": 34294, "endOffset": 34833, "count": 0}, {"startOffset": 34853, "endOffset": 35660, "count": 0}, {"startOffset": 35682, "endOffset": 37184, "count": 0}, {"startOffset": 37205, "endOffset": 37633, "count": 0}, {"startOffset": 38012, "endOffset": 38020, "count": 0}, {"startOffset": 38115, "endOffset": 38123, "count": 0}, {"startOffset": 38214, "endOffset": 38234, "count": 0}, {"startOffset": 38235, "endOffset": 38698, "count": 0}, {"startOffset": 39413, "endOffset": 39800, "count": 0}, {"startOffset": 39835, "endOffset": 40242, "count": 0}, {"startOffset": 40262, "endOffset": 40669, "count": 0}, {"startOffset": 41766, "endOffset": 41785, "count": 0}, {"startOffset": 41786, "endOffset": 42329, "count": 0}, {"startOffset": 42363, "endOffset": 42694, "count": 0}, {"startOffset": 43240, "endOffset": 44468, "count": 0}, {"startOffset": 44492, "endOffset": 45167, "count": 0}, {"startOffset": 45384, "endOffset": 45453, "count": 0}, {"startOffset": 45454, "endOffset": 47767, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8342, "endOffset": 8426, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9004, "endOffset": 9330, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11044, "endOffset": 11087, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11154, "endOffset": 11220, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11183, "endOffset": 11212, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11460, "endOffset": 11816, "count": 10}, {"startOffset": 11492, "endOffset": 11812, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11573, "endOffset": 11602, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12332, "endOffset": 12576, "count": 20}, {"startOffset": 12382, "endOffset": 12572, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12896, "endOffset": 12942, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13018, "endOffset": 14443, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14503, "endOffset": 14814, "count": 10}], "isBlockCoverage": true}, {"functionName": "checkModelChange", "ranges": [{"startOffset": 14540, "endOffset": 14686, "count": 10}, {"startOffset": 14633, "endOffset": 14680, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14780, "endOffset": 14809, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14879, "endOffset": 15589, "count": 10}], "isBlockCoverage": true}, {"functionName": "flashFallbackHandler", "ranges": [{"startOffset": 14920, "endOffset": 15526, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16409, "endOffset": 16595, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17086, "endOffset": 17729, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17793, "endOffset": 19186, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19355, "endOffset": 19447, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19527, "endOffset": 19774, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19867, "endOffset": 19937, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20435, "endOffset": 20891, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21243, "endOffset": 21400, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21602, "endOffset": 22481, "count": 20}], "isBlockCoverage": true}, {"functionName": "fetchUserMessages", "ranges": [{"startOffset": 21640, "endOffset": 22451, "count": 20}, {"startOffset": 21724, "endOffset": 21729, "count": 0}, {"startOffset": 22117, "endOffset": 22389, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21796, "endOffset": 21886, "count": 1}, {"startOffset": 21827, "endOffset": 21859, "count": 0}, {"startOffset": 21860, "endOffset": 21886, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21899, "endOffset": 21918, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22669, "endOffset": 22773, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23008, "endOffset": 23216, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23416, "endOffset": 23471, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23551, "endOffset": 23821, "count": 10}, {"startOffset": 23650, "endOffset": 23820, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23683, "endOffset": 23755, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23774, "endOffset": 23816, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23910, "endOffset": 24080, "count": 10}, {"startOffset": 23987, "endOffset": 24008, "count": 0}, {"startOffset": 24010, "endOffset": 24076, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24204, "endOffset": 24351, "count": 10}, {"startOffset": 24243, "endOffset": 24280, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24316, "endOffset": 24345, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24531, "endOffset": 24769, "count": 10}, {"startOffset": 24619, "endOffset": 24700, "count": 4}, {"startOffset": 24662, "endOffset": 24676, "count": 1}, {"startOffset": 24677, "endOffset": 24693, "count": 3}, {"startOffset": 24700, "endOffset": 24768, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24999, "endOffset": 25510, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27709, "endOffset": 28307, "count": 1}], "isBlockCoverage": true}, {"functionName": "children", "ranges": [{"startOffset": 28338, "endOffset": 28352, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28914, "endOffset": 29669, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31981, "endOffset": 32359, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTimeout", "ranges": [{"startOffset": 34428, "endOffset": 34586, "count": 0}], "isBlockCoverage": false}, {"functionName": "onExit", "ranges": [{"startOffset": 37335, "endOffset": 37368, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 45748, "endOffset": 45813, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 46001, "endOffset": 46066, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 248}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 12}, {"startOffset": 475, "endOffset": 483, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 1}, {"startOffset": 562, "endOffset": 570, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useTerminalSize.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4609, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4609, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 20}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTerminalSize", "ranges": [{"startOffset": 752, "endOffset": 1649, "count": 20}], "isBlockCoverage": true}, {"functionName": "getOptimalSize", "ranges": [{"startOffset": 806, "endOffset": 1107, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1225, "endOffset": 1626, "count": 10}], "isBlockCoverage": true}, {"functionName": "updateSize", "ranges": [{"startOffset": 1237, "endOffset": 1299, "count": 0}], "isBlockCoverage": false}, {"functionName": "debouncedUpdateSize", "ranges": [{"startOffset": 1327, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1516, "endOffset": 1621, "count": 10}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useLoadingIndicator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 20}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "useLoadingIndicator", "ranges": [{"startOffset": 1001, "endOffset": 2771, "count": 20}, {"startOffset": 2694, "endOffset": 2715, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1820, "endOffset": 2544, "count": 10}, {"startOffset": 1930, "endOffset": 1999, "count": 0}, {"startOffset": 2001, "endOffset": 2091, "count": 0}, {"startOffset": 2162, "endOffset": 2246, "count": 0}, {"startOffset": 2248, "endOffset": 2338, "count": 0}, {"startOffset": 2428, "endOffset": 2487, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2026, "endOffset": 2050, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2273, "endOffset": 2297, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useTimer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 20}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTimer", "ranges": [{"startOffset": 600, "endOffset": 1828, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 942, "endOffset": 1780, "count": 10}, {"startOffset": 1029, "endOffset": 1108, "count": 0}, {"startOffset": 1165, "endOffset": 1202, "count": 0}, {"startOffset": 1229, "endOffset": 1261, "count": 0}, {"startOffset": 1321, "endOffset": 1512, "count": 0}, {"startOffset": 1548, "endOffset": 1631, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1440, "endOffset": 1499, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1650, "endOffset": 1775, "count": 10}, {"startOffset": 1686, "endOffset": 1769, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/usePhraseCycler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 428, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 474, "endOffset": 523, "count": 20}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "usePhraseCycler", "ranges": [{"startOffset": 6464, "endOffset": 7975, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6725, "endOffset": 7917, "count": 10}, {"startOffset": 6752, "endOffset": 6965, "count": 0}, {"startOffset": 6985, "endOffset": 7537, "count": 0}, {"startOffset": 7582, "endOffset": 7683, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7312, "endOffset": 7502, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7760, "endOffset": 7912, "count": 10}, {"startOffset": 7805, "endOffset": 7906, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useThemeCommand.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10877, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10877, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 20}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "useThemeCommand", "ranges": [{"startOffset": 1002, "endOffset": 3356, "count": 20}, {"startOffset": 1220, "endOffset": 1266, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1378, "endOffset": 1945, "count": 10}, {"startOffset": 1421, "endOffset": 1721, "count": 2}, {"startOffset": 1477, "endOffset": 1700, "count": 1}, {"startOffset": 1721, "endOffset": 1799, "count": 8}, {"startOffset": 1799, "endOffset": 1901, "count": 0}, {"startOffset": 1901, "endOffset": 1941, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2054, "endOffset": 2372, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2731, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2848, "endOffset": 2899, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2994, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 8}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 411}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3189, "endOffset": 3602, "count": 68}, {"startOffset": 3512, "endOffset": 3552, "count": 28}, {"startOffset": 3552, "endOffset": 3601, "count": 40}], "isBlockCoverage": true}, {"functionName": "typeOrder", "ranges": [{"startOffset": 3225, "endOffset": 3410, "count": 136}, {"startOffset": 3271, "endOffset": 3305, "count": 68}, {"startOffset": 3316, "endOffset": 3351, "count": 68}, {"startOffset": 3362, "endOffset": 3392, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3633, "endOffset": 3700, "count": 26}], "isBlockCoverage": true}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 8}, {"startOffset": 4044, "endOffset": 4183, "count": 0}], "isBlockCoverage": true}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 8}, {"startOffset": 4239, "endOffset": 4274, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4313, "endOffset": 4348, "count": 48}], "isBlockCoverage": true}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 401}, {"startOffset": 4489, "endOffset": 4546, "count": 33}, {"startOffset": 4546, "endOffset": 4579, "count": 368}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 22}], "isBlockCoverage": true}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1578", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1579", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1580", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 33}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1581", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useEditorSettings.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6615, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6615, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 20}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "useEditorSettings", "ranges": [{"startOffset": 954, "endOffset": 2270, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1163, "endOffset": 1207, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1286, "endOffset": 1992, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2110, "endOffset": 2155, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1582", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 331, "count": 1}, {"startOffset": 321, "endOffset": 329, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 387, "endOffset": 468, "count": 1}, {"startOffset": 458, "endOffset": 466, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 528, "endOffset": 613, "count": 1}, {"startOffset": 603, "endOffset": 611, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1583", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 1}, {"startOffset": 302, "endOffset": 310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1584", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 2}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 516, "endOffset": 568, "count": 1}, {"startOffset": 558, "endOffset": 566, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5304, "endOffset": 5451, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5519, "endOffset": 5626, "count": 8}], "isBlockCoverage": true}, {"functionName": "MCPServerConfig", "ranges": [{"startOffset": 5632, "endOffset": 6190, "count": 8}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6213, "endOffset": 6887, "count": 0}], "isBlockCoverage": true}, {"functionName": "Config", "ranges": [{"startOffset": 6893, "endOffset": 9555, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshAuth", "ranges": [{"startOffset": 9560, "endOffset": 10741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 10746, "endOffset": 10799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGeneratorConfig", "ranges": [{"startOffset": 10804, "endOffset": 10883, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModel", "ranges": [{"startOffset": 10888, "endOffset": 10971, "count": 0}], "isBlockCoverage": false}, {"functionName": "setModel", "ranges": [{"startOffset": 10976, "endOffset": 11165, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModelSwitchedDuringSession", "ranges": [{"startOffset": 11170, "endOffset": 11256, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetModelToDefault", "ranges": [{"startOffset": 11261, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFlashFallbackHandler", "ranges": [{"startOffset": 11500, "endOffset": 11585, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEmbeddingModel", "ranges": [{"startOffset": 11590, "endOffset": 11653, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandbox", "ranges": [{"startOffset": 11658, "endOffset": 11707, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 11712, "endOffset": 11765, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 11770, "endOffset": 11825, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolRegistry", "ranges": [{"startOffset": 11830, "endOffset": 11906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 11911, "endOffset": 11964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestion", "ranges": [{"startOffset": 11969, "endOffset": 12020, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFullContext", "ranges": [{"startOffset": 12025, "endOffset": 12082, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreTools", "ranges": [{"startOffset": 12087, "endOffset": 12140, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExcludeTools", "ranges": [{"startOffset": 12145, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolDiscoveryCommand", "ranges": [{"startOffset": 12209, "endOffset": 12284, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolCallCommand", "ranges": [{"startOffset": 12289, "endOffset": 12354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServerCommand", "ranges": [{"startOffset": 12359, "endOffset": 12426, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServers", "ranges": [{"startOffset": 12431, "endOffset": 12486, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserMemory", "ranges": [{"startOffset": 12491, "endOffset": 12546, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUserMemory", "ranges": [{"startOffset": 12551, "endOffset": 12628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFileCount", "ranges": [{"startOffset": 12633, "endOffset": 12700, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFileCount", "ranges": [{"startOffset": 12705, "endOffset": 12778, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApprovalMode", "ranges": [{"startOffset": 12783, "endOffset": 12842, "count": 0}], "isBlockCoverage": false}, {"functionName": "setApprovalMode", "ranges": [{"startOffset": 12847, "endOffset": 12910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowMemoryUsage", "ranges": [{"startOffset": 12915, "endOffset": 12980, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessibility", "ranges": [{"startOffset": 12985, "endOffset": 13046, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryEnabled", "ranges": [{"startOffset": 13051, "endOffset": 13136, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryLogPromptsEnabled", "ranges": [{"startOffset": 13141, "endOffset": 13238, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryOtlpEndpoint", "ranges": [{"startOffset": 13243, "endOffset": 13377, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryTarget", "ranges": [{"startOffset": 13382, "endOffset": 13507, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 13512, "endOffset": 13569, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienDir", "ranges": [{"startOffset": 13574, "endOffset": 13699, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 13704, "endOffset": 13815, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnableRecursiveFileSearch", "ranges": [{"startOffset": 13820, "endOffset": 13919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 13924, "endOffset": 14018, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCheckpointingEnabled", "ranges": [{"startOffset": 14023, "endOffset": 14091, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProxy", "ranges": [{"startOffset": 14096, "endOffset": 14141, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWorkingDir", "ranges": [{"startOffset": 14146, "endOffset": 14194, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBugCommand", "ranges": [{"startOffset": 14199, "endOffset": 14254, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileService", "ranges": [{"startOffset": 14259, "endOffset": 14483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 14488, "endOffset": 14567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExtensionContextFilePaths", "ranges": [{"startOffset": 14572, "endOffset": 14657, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGitService", "ranges": [{"startOffset": 14662, "endOffset": 14899, "count": 0}], "isBlockCoverage": false}, {"functionName": "createToolRegistry", "ranges": [{"startOffset": 14903, "endOffset": 16829, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1585", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 1}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1472, "endOffset": 1648, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1680, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3230, "endOffset": 4131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1764", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1247, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1765", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 1}, {"startOffset": 386, "endOffset": 394, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3046, "endOffset": 3754, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 3756, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 6146, "endOffset": 6761, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 6763, "endOffset": 7401, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 7402, "endOffset": 7695, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 7696, "endOffset": 7846, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 7847, "endOffset": 7999, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1778", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 949, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1218, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2462, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1779", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1780", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 1}, {"startOffset": 412, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1248, "endOffset": 1284, "count": 0}], "isBlockCoverage": true}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1290, "endOffset": 1446, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1451, "endOffset": 1857, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1862, "endOffset": 2135, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 2140, "endOffset": 2230, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2235, "endOffset": 2331, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2336, "endOffset": 2455, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2460, "endOffset": 2588, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2593, "endOffset": 2807, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2812, "endOffset": 2867, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2872, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3344, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3770, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1785", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 959, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1138, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1238, "endOffset": 1406, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1408, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1784, "endOffset": 2250, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2251, "endOffset": 2484, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2485, "endOffset": 2605, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2606, "endOffset": 3139, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3140, "endOffset": 3197, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3198, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3340, "endOffset": 4349, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1786", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 2}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 1}, {"startOffset": 537, "endOffset": 545, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1787", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1049, "endOffset": 2840, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1788", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1433, "endOffset": 1487, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1493, "endOffset": 2732, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2737, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4882, "endOffset": 4933, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4939, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 5129, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 5575, "endOffset": 7177, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 7450, "endOffset": 7640, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 7734, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 7896, "endOffset": 8158, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 8225, "endOffset": 8283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1789", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 1}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 577, "endOffset": 679, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 1168, "endOffset": 1538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1647, "endOffset": 1813, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2269, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2753, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 3048, "endOffset": 3278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1790", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 1}, {"startOffset": 503, "endOffset": 511, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 1}, {"startOffset": 620, "endOffset": 628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 1}, {"startOffset": 743, "endOffset": 751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 1}, {"startOffset": 844, "endOffset": 852, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 1}, {"startOffset": 955, "endOffset": 963, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 1}, {"startOffset": 1060, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 1}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 1}, {"startOffset": 1258, "endOffset": 1266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3516, "endOffset": 3853, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4220, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4372, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4609, "endOffset": 4842, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4894, "endOffset": 5025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 5066, "endOffset": 5151, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5200, "endOffset": 5265, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5267, "endOffset": 7941, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 7943, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 20315, "endOffset": 20843, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1881", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 712, "endOffset": 827, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 833, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 867, "endOffset": 1405, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1410, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2753, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4273, "endOffset": 5686, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1882", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 1}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1373, "endOffset": 1398, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1404, "endOffset": 1434, "count": 1}], "isBlockCoverage": true}, {"functionName": "LSTool", "ranges": [{"startOffset": 1621, "endOffset": 2848, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3041, "endOffset": 3622, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3799, "endOffset": 4357, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldIgnore", "ranges": [{"startOffset": 4600, "endOffset": 5161, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5347, "endOffset": 5547, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorResult", "ranges": [{"startOffset": 5598, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5982, "endOffset": 10207, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1883", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 690, "endOffset": 2131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1884", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 2}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 1}, {"startOffset": 368, "endOffset": 376, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 1}, {"startOffset": 455, "endOffset": 463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 1}, {"startOffset": 544, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 1}, {"startOffset": 629, "endOffset": 637, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 1}, {"startOffset": 718, "endOffset": 726, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 1}, {"startOffset": 811, "endOffset": 819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 1}, {"startOffset": 910, "endOffset": 918, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1544, "endOffset": 1739, "count": 20}, {"startOffset": 1669, "endOffset": 1719, "count": 0}], "isBlockCoverage": true}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1907, "endOffset": 4318, "count": 20}, {"startOffset": 2020, "endOffset": 4317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2348, "endOffset": 2363, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4756, "endOffset": 5197, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5241, "endOffset": 5604, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5650, "endOffset": 5727, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5927, "endOffset": 6056, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6260, "endOffset": 6468, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1885", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1757, "endOffset": 1782, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1788, "endOffset": 1813, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1819, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3252, "endOffset": 4595, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4600, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4993, "endOffset": 6235, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1886", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1752, "endOffset": 1939, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2182, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3140, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4692, "endOffset": 6028, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6428, "endOffset": 11566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1891", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1747, "endOffset": 1891, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2136, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2242, "endOffset": 2413, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2415, "endOffset": 4312, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4314, "endOffset": 4828, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4830, "endOffset": 5085, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5087, "endOffset": 5603, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5605, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6151, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1987", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1988", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2536, "endOffset": 2549, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2555, "endOffset": 2590, "count": 1}], "isBlockCoverage": true}, {"functionName": "GrepTool", "ranges": [{"startOffset": 2799, "endOffset": 4210, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 4563, "endOffset": 5695, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5872, "endOffset": 6580, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6783, "endOffset": 9645, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 9926, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 11020, "endOffset": 12605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 12774, "endOffset": 13495, "count": 0}], "isBlockCoverage": false}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 13739, "endOffset": 22277, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2008", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 1}, {"startOffset": 665, "endOffset": 673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1059, "endOffset": 1144, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1146, "endOffset": 1362, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1488, "endOffset": 2306, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2308, "endOffset": 2566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2014", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2015", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1765, "endOffset": 2458, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2627, "endOffset": 2647, "count": 1}], "isBlockCoverage": true}, {"functionName": "GlobTool", "ranges": [{"startOffset": 2783, "endOffset": 4263, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 4338, "endOffset": 4932, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4999, "endOffset": 6375, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6445, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6975, "endOffset": 10877, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2016", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2525, "endOffset": 2548, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2554, "endOffset": 2591, "count": 0}], "isBlockCoverage": true}, {"functionName": "EditTool", "ranges": [{"startOffset": 2727, "endOffset": 6048, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 6257, "endOffset": 6690, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 6859, "endOffset": 7439, "count": 0}], "isBlockCoverage": false}, {"functionName": "_applyReplacement", "ranges": [{"startOffset": 7444, "endOffset": 8050, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateEdit", "ranges": [{"startOffset": 8345, "endOffset": 11949, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 12095, "endOffset": 13827, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 13832, "endOffset": 14832, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 15015, "endOffset": 17873, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureParentDirectoriesExist", "ranges": [{"startOffset": 17948, "endOffset": 18202, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 18207, "endOffset": 19573, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2018", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 2846, "endOffset": 7832, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 7834, "endOffset": 8426, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 8916, "endOffset": 11293, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 11899, "endOffset": 14728, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 15211, "endOffset": 17532, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 17964, "endOffset": 19887, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 19889, "endOffset": 20549, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 20627, "endOffset": 22935, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 22994, "endOffset": 23303, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 23305, "endOffset": 23427, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2019", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 399, "endOffset": 417, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2020", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2021", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2020, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2032, "endOffset": 2065, "count": 1}], "isBlockCoverage": true}, {"functionName": "ShellTool", "ranges": [{"startOffset": 2098, "endOffset": 4093, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4098, "endOffset": 4628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommandRoot", "ranges": [{"startOffset": 4633, "endOffset": 5152, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5157, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6114, "endOffset": 6982, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6987, "endOffset": 16021, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2022", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2893, "endOffset": 2943, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2905, "endOffset": 2931, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteFileTool", "ranges": [{"startOffset": 2949, "endOffset": 3800, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3805, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4316, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5811, "endOffset": 6184, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6268, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7961, "endOffset": 11962, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getCorrected<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 11967, "endOffset": 14133, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 14138, "endOffset": 15015, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2023", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1985, "endOffset": 2099, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2244, "endOffset": 2250, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2256, "endOffset": 2281, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebFetchTool", "ranges": [{"startOffset": 2287, "endOffset": 3153, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>back", "ranges": [{"startOffset": 3158, "endOffset": 5558, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 5563, "endOffset": 6212, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6217, "endOffset": 6469, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6474, "endOffset": 7711, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7716, "endOffset": 12476, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2024", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2025", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1150, "endOffset": 1154, "count": 0}], "isBlockCoverage": true}, {"functionName": "FetchError", "ranges": [{"startOffset": 1160, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1282, "endOffset": 1492, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1494, "endOffset": 2120, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2058", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3449, "endOffset": 3537, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3476, "endOffset": 3507, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 3789, "endOffset": 7980, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 7985, "endOffset": 9539, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 9544, "endOffset": 11151, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 11156, "endOffset": 19498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2059", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 1}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 1}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 1}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 2}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 1}, {"startOffset": 838, "endOffset": 846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 1}, {"startOffset": 923, "endOffset": 931, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3737, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 4068, "endOffset": 4241, "count": 1}, {"startOffset": 4154, "endOffset": 4203, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 4243, "endOffset": 4412, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4414, "endOffset": 4549, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4621, "endOffset": 4942, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5032, "endOffset": 5071, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5077, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5213, "endOffset": 7631, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7636, "endOffset": 9167, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2060", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1399, "endOffset": 1405, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1411, "endOffset": 1444, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebSearchTool", "ranges": [{"startOffset": 1450, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 2014, "endOffset": 2427, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2432, "endOffset": 2521, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2526, "endOffset": 6272, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2061", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 3118, "endOffset": 3236, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3261, "endOffset": 3429, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienClient", "ranges": [{"startOffset": 3435, "endOffset": 3752, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3757, "endOffset": 3962, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGenerator", "ranges": [{"startOffset": 3967, "endOffset": 4149, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 4154, "endOffset": 4231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChat", "ranges": [{"startOffset": 4236, "endOffset": 4369, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 4374, "endOffset": 4444, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 4449, "endOffset": 4526, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetChat", "ranges": [{"startOffset": 4531, "endOffset": 4625, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnvironment", "ranges": [{"startOffset": 4630, "endOffset": 6989, "count": 0}], "isBlockCoverage": false}, {"functionName": "startChat", "ranges": [{"startOffset": 6994, "endOffset": 8646, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 8651, "endOffset": 9822, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJson", "ranges": [{"startOffset": 9827, "endOffset": 12489, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 12494, "endOffset": 14052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateEmbedding", "ranges": [{"startOffset": 14057, "endOffset": 15157, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryCompressChat", "ranges": [{"startOffset": 15162, "endOffset": 17765, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 17957, "endOffset": 19078, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2062", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 995, "endOffset": 6814, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7082, "endOffset": 9356, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9777, "endOffset": 12254, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2063", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 1}, {"startOffset": 362, "endOffset": 370, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1677, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1805, "endOffset": 1881, "count": 0}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1887, "endOffset": 2008, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 2083, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 5910, "endOffset": 6524, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 6529, "endOffset": 6592, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 6597, "endOffset": 6662, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2064", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2065", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2803, "endOffset": 31135, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2066", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3232, "endOffset": 6198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2067", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 398, "endOffset": 569, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2068", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3468, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3779, "endOffset": 4875, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5082, "endOffset": 5277, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienChat", "ranges": [{"startOffset": 5283, "endOffset": 5561, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getRequestTextFromContents", "ranges": [{"startOffset": 5566, "endOffset": 5780, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiRequest", "ranges": [{"startOffset": 5785, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiResponse", "ranges": [{"startOffset": 6032, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiError", "ranges": [{"startOffset": 6279, "endOffset": 6640, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 6832, "endOffset": 7915, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessage", "ranges": [{"startOffset": 8567, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 12194, "endOffset": 14714, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 15750, "endOffset": 16039, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHistory", "ranges": [{"startOffset": 16092, "endOffset": 16141, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 16273, "endOffset": 16336, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 16341, "endOffset": 16400, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFinalUsageMetadata", "ranges": [{"startOffset": 16405, "endOffset": 16635, "count": 0}], "isBlockCoverage": false}, {"functionName": "processStreamResponse", "ranges": [{"startOffset": 16640, "endOffset": 18155, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordHistory", "ranges": [{"startOffset": 18160, "endOffset": 21626, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTextContent", "ranges": [{"startOffset": 21631, "endOffset": 21899, "count": 0}], "isBlockCoverage": false}, {"functionName": "isT<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 21904, "endOffset": 22184, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2069", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1000, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1711, "endOffset": 1795, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2103, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 5455, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 6244, "endOffset": 7452, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 7671, "endOffset": 7930, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 8175, "endOffset": 9298, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2070", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 1}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 1}, {"startOffset": 568, "endOffset": 576, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 1}, {"startOffset": 655, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 1}, {"startOffset": 748, "endOffset": 756, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2375, "endOffset": 2425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2427, "endOffset": 2532, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2533, "endOffset": 3720, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3722, "endOffset": 4490, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4492, "endOffset": 5628, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5630, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6272, "endOffset": 7445, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7447, "endOffset": 9239, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2105", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 4345, "endOffset": 4418, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 4420, "endOffset": 5097, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 5098, "endOffset": 7537, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7539, "endOffset": 7968, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2599", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1870, "endOffset": 1885, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1891, "endOffset": 2083, "count": 0}], "isBlockCoverage": true}, {"functionName": "ClearcutLogger", "ranges": [{"startOffset": 2139, "endOffset": 2196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstance", "ranges": [{"startOffset": 2208, "endOffset": 2496, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueueLogEvent", "ranges": [{"startOffset": 2601, "endOffset": 2804, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLogEvent", "ranges": [{"startOffset": 2809, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushIfNeeded", "ranges": [{"startOffset": 3091, "endOffset": 3251, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushToClearcut", "ranges": [{"startOffset": 3256, "endOffset": 5307, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeLogResponse", "ranges": [{"startOffset": 5396, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "logStartSessionEvent", "ranges": [{"startOffset": 6718, "endOffset": 9465, "count": 0}], "isBlockCoverage": false}, {"functionName": "logNewPromptEvent", "ranges": [{"startOffset": 9470, "endOffset": 9837, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCallEvent", "ranges": [{"startOffset": 9842, "endOffset": 11128, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequestEvent", "ranges": [{"startOffset": 11133, "endOffset": 11493, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponseEvent", "ranges": [{"startOffset": 11498, "endOffset": 13468, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiErrorEvent", "ranges": [{"startOffset": 13473, "endOffset": 14389, "count": 0}], "isBlockCoverage": false}, {"functionName": "logEndSessionEvent", "ranges": [{"startOffset": 14394, "endOffset": 14804, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdown", "ranges": [{"startOffset": 14809, "endOffset": 14945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2600", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 1}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 1}, {"startOffset": 691, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 1}, {"startOffset": 877, "endOffset": 885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 1}, {"startOffset": 968, "endOffset": 976, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 1}, {"startOffset": 1065, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1762, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1810, "endOffset": 2480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2512, "endOffset": 2837, "count": 0}], "isBlockCoverage": true}, {"functionName": "StartSessionEvent", "ranges": [{"startOffset": 2843, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4225, "endOffset": 4288, "count": 0}], "isBlockCoverage": true}, {"functionName": "EndSessionEvent", "ranges": [{"startOffset": 4294, "endOffset": 4475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4507, "endOffset": 4585, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserPromptEvent", "ranges": [{"startOffset": 4591, "endOffset": 4811, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4841, "endOffset": 4997, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolCallEvent", "ranges": [{"startOffset": 5003, "endOffset": 5542, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5574, "endOffset": 5650, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiRequestEvent", "ranges": [{"startOffset": 5656, "endOffset": 5870, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5900, "endOffset": 6019, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiErrorEvent", "ranges": [{"startOffset": 6025, "endOffset": 6372, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6405, "endOffset": 6654, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiResponseEvent", "ranges": [{"startOffset": 6660, "endOffset": 7382, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2601", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8055, "endOffset": 8354, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2602", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6096, "count": 1}, {"startOffset": 1116, "endOffset": 1121, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureArienDirExists", "ranges": [{"startOffset": 1282, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "readUserIdFromFile", "ranges": [{"startOffset": 1451, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeUserIdToFile", "ranges": [{"startOffset": 1683, "endOffset": 1791, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPersistentUserId", "ranges": [{"startOffset": 1967, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2603", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 1}, {"startOffset": 384, "endOffset": 392, "count": 0}], "isBlockCoverage": true}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 20}, {"startOffset": 720, "endOffset": 772, "count": 0}, {"startOffset": 781, "endOffset": 805, "count": 0}, {"startOffset": 814, "endOffset": 850, "count": 0}, {"startOffset": 859, "endOffset": 895, "count": 0}, {"startOffset": 904, "endOffset": 926, "count": 0}, {"startOffset": 935, "endOffset": 973, "count": 0}, {"startOffset": 982, "endOffset": 1006, "count": 0}, {"startOffset": 1015, "endOffset": 1069, "count": 0}, {"startOffset": 1078, "endOffset": 1154, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2741", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 992, "endOffset": 1061, "count": 0}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 1067, "endOffset": 1832, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1915, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 2463, "endOffset": 2630, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2706, "endOffset": 2879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2949, "endOffset": 3041, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2742", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1010, "endOffset": 1086, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1092, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 1200, "endOffset": 1618, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1623, "endOffset": 2177, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 2182, "endOffset": 2283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 2288, "endOffset": 2816, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2821, "endOffset": 2872, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2744", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1719, "endOffset": 1730, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1736, "endOffset": 1839, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1844, "endOffset": 2078, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 2083, "endOffset": 2483, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 2488, "endOffset": 2795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2936, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 4541, "endOffset": 4951, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 4956, "endOffset": 5098, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 5103, "endOffset": 5313, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 5318, "endOffset": 5597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2751", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 1}, {"startOffset": 511, "endOffset": 519, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 1}, {"startOffset": 636, "endOffset": 644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 1}, {"startOffset": 757, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 1}, {"startOffset": 894, "endOffset": 902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 1}, {"startOffset": 1019, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 1}, {"startOffset": 1132, "endOffset": 1140, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 1}, {"startOffset": 1354, "endOffset": 1362, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 1}, {"startOffset": 1463, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 1}, {"startOffset": 1578, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 1}, {"startOffset": 1699, "endOffset": 1707, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 1}, {"startOffset": 1816, "endOffset": 1824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 1}, {"startOffset": 1933, "endOffset": 1941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 1}, {"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 1}, {"startOffset": 2276, "endOffset": 2284, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 1}, {"startOffset": 2395, "endOffset": 2403, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 1}, {"startOffset": 2510, "endOffset": 2518, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 1}, {"startOffset": 2615, "endOffset": 2623, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 1}, {"startOffset": 2738, "endOffset": 2746, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4170, "endOffset": 4276, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2752", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1097, "endOffset": 1169, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1238, "endOffset": 1413, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1489, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1560, "endOffset": 2994, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2999, "endOffset": 3521, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3526, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4825, "endOffset": 7173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 7178, "endOffset": 7603, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 7608, "endOffset": 8759, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 8764, "endOffset": 9090, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 9095, "endOffset": 9609, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 9614, "endOffset": 10632, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 10637, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2753", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2754", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1290, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1501, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3277, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3586, "endOffset": 3748, "count": 0}], "isBlockCoverage": true}, {"functionName": "CoreToolScheduler", "ranges": [{"startOffset": 3754, "endOffset": 4222, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatusInternal", "ranges": [{"startOffset": 4227, "endOffset": 8661, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArgsInternal", "ranges": [{"startOffset": 8666, "endOffset": 8979, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRunning", "ranges": [{"startOffset": 8984, "endOffset": 9117, "count": 0}], "isBlockCoverage": false}, {"functionName": "schedule", "ranges": [{"startOffset": 9122, "endOffset": 11771, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmationResponse", "ranges": [{"startOffset": 11776, "endOffset": 13799, "count": 0}], "isBlockCoverage": false}, {"functionName": "attemptExecutionOfScheduledCalls", "ranges": [{"startOffset": 13804, "endOffset": 16328, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAndNotifyCompletion", "ranges": [{"startOffset": 16333, "endOffset": 17051, "count": 0}], "isBlockCoverage": false}, {"functionName": "notifyToolCallsUpdate", "ranges": [{"startOffset": 17056, "endOffset": 17192, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2755", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1626, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1702, "endOffset": 2744, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2745, "endOffset": 3774, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3775, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4335, "endOffset": 5000, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2756", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 1}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 1}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1159, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1160, "endOffset": 1380, "count": 6}, {"startOffset": 1267, "endOffset": 1288, "count": 0}, {"startOffset": 1343, "endOffset": 1378, "count": 4}], "isBlockCoverage": true}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1715, "endOffset": 1935, "count": 6}, {"startOffset": 1874, "endOffset": 1897, "count": 0}], "isBlockCoverage": true}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1937, "endOffset": 2168, "count": 6}, {"startOffset": 2109, "endOffset": 2148, "count": 5}, {"startOffset": 2148, "endOffset": 2167, "count": 1}], "isBlockCoverage": true}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2327, "endOffset": 2530, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2587, "endOffset": 4512, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4725, "endOffset": 6603, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2757", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 933, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2758", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 1}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1420, "endOffset": 1484, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1559, "endOffset": 1621, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1697, "endOffset": 1761, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1813, "endOffset": 2906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2907, "endOffset": 6602, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6603, "endOffset": 7464, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7465, "endOffset": 8104, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8105, "endOffset": 9189, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2759", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2760", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2761", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/slashCommandProcessor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 116707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 116707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 20}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "useSlashCommandProcessor", "ranges": [{"startOffset": 2461, "endOffset": 37458, "count": 20}, {"startOffset": 3202, "endOffset": 3272, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2850, "endOffset": 2993, "count": 10}, {"startOffset": 2914, "endOffset": 2992, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3338, "endOffset": 4653, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4739, "endOffset": 4879, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4983, "endOffset": 5618, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5704, "endOffset": 6100, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6171, "endOffset": 35277, "count": 20}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 6316, "endOffset": 6437, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 6567, "endOffset": 7391, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 7514, "endOffset": 7769, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 7867, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8048, "endOffset": 8125, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8238, "endOffset": 8317, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8427, "endOffset": 8507, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8634, "endOffset": 9183, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 9300, "endOffset": 16616, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 16762, "endOffset": 17753, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 17865, "endOffset": 20010, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 20067, "endOffset": 20145, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 20244, "endOffset": 21413, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 21512, "endOffset": 24069, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 24216, "endOffset": 28147, "count": 0}], "isBlockCoverage": false}, {"functionName": "completion", "ranges": [{"startOffset": 28169, "endOffset": 28234, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 28352, "endOffset": 29076, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 29245, "endOffset": 31003, "count": 0}], "isBlockCoverage": false}, {"functionName": "completion", "ranges": [{"startOffset": 31282, "endOffset": 31768, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 31786, "endOffset": 35235, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35839, "endOffset": 37340, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2762", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useStateAndRef.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3416, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3416, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 20}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStateAndRef", "ranges": [{"startOffset": 596, "endOffset": 1159, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 834, "endOffset": 1110, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2763", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/SessionContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 10}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 357, "endOffset": 406, "count": 40}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "addTokens", "ranges": [{"startOffset": 1025, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "SessionStatsProvider", "ranges": [{"startOffset": 1516, "endOffset": 4605, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2468, "endOffset": 3263, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3338, "endOffset": 4110, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4172, "endOffset": 4252, "count": 10}], "isBlockCoverage": true}, {"functionName": "useSessionStats", "ranges": [{"startOffset": 4631, "endOffset": 4863, "count": 40}, {"startOffset": 4742, "endOffset": 4842, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2764", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useShowMemoryCommand.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "createShowMemoryAction", "ranges": [{"startOffset": 519, "endOffset": 2526, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2765", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/generated/git-commit.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2766", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2767", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/utils/version.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCliVersion", "ranges": [{"startOffset": 509, "endOffset": 678, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2768", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/utils/package.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 10}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPackageJson", "ranges": [{"startOffset": 646, "endOffset": 907, "count": 10}, {"startOffset": 699, "endOffset": 728, "count": 0}, {"startOffset": 829, "endOffset": 846, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2801", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useAutoAcceptIndicator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 20}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "useAutoAcceptIndicator", "ranges": [{"startOffset": 978, "endOffset": 2040, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1239, "endOffset": 1302, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1364, "endOffset": 2002, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2802", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useConsoleMessages.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 20}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "useConsoleMessages", "ranges": [{"startOffset": 617, "endOffset": 2811, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 950, "endOffset": 1718, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1797, "endOffset": 1964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2060, "endOffset": 2158, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2264, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2542, "endOffset": 2726, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2584, "endOffset": 2720, "count": 10}, {"startOffset": 2645, "endOffset": 2712, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2803", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Header.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 20}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "Header", "ranges": [{"startOffset": 1349, "endOffset": 3572, "count": 10}, {"startOffset": 1660, "endOffset": 1700, "count": 0}, {"startOffset": 1820, "endOffset": 1981, "count": 0}, {"startOffset": 2323, "endOffset": 2988, "count": 9}, {"startOffset": 2989, "endOffset": 3350, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2825", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 399}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 95}], "isBlockCoverage": true}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 20}], "isBlockCoverage": true}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 21}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 38}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 17}], "isBlockCoverage": true}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 25}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 2}], "isBlockCoverage": true}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 21}], "isBlockCoverage": true}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 141}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 19}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2826", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AsciiArt.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 10}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 390, "count": 20}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "getGreetingBasedOnTime", "ranges": [{"startOffset": 955, "endOffset": 1275, "count": 2}, {"startOffset": 1077, "endOffset": 1107, "count": 0}, {"startOffset": 1142, "endOffset": 1174, "count": 0}, {"startOffset": 1239, "endOffset": 1273, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2827", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 30}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 10}, {"startOffset": 538, "endOffset": 546, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 30}, {"startOffset": 776, "endOffset": 795, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 866, "endOffset": 887, "count": 240}], "isBlockCoverage": true}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 10}], "isBlockCoverage": true}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 10}], "isBlockCoverage": true}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2828", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/LoadingIndicator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 18}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "LoadingIndicator", "ranges": [{"startOffset": 1321, "endOffset": 6619, "count": 18}, {"startOffset": 1539, "endOffset": 1561, "count": 0}, {"startOffset": 1592, "endOffset": 1601, "count": 0}, {"startOffset": 2774, "endOffset": 2779, "count": 0}, {"startOffset": 3709, "endOffset": 4135, "count": 0}, {"startOffset": 4357, "endOffset": 4378, "count": 0}, {"startOffset": 4966, "endOffset": 5288, "count": 0}, {"startOffset": 5522, "endOffset": 5603, "count": 0}, {"startOffset": 5604, "endOffset": 6415, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLoadingState", "ranges": [{"startOffset": 1653, "endOffset": 1954, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProgressIndicator", "ranges": [{"startOffset": 1987, "endOffset": 2173, "count": 18}, {"startOffset": 2030, "endOffset": 2172, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2829", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/StreamingContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 20}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 36}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStreamingContext", "ranges": [{"startOffset": 802, "endOffset": 1043, "count": 36}, {"startOffset": 914, "endOffset": 1022, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2830", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ArienRespondingSpinner.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 18}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienRespondingSpinner", "ranges": [{"startOffset": 1320, "endOffset": 2772, "count": 18}, {"startOffset": 1500, "endOffset": 1887, "count": 0}, {"startOffset": 1977, "endOffset": 2378, "count": 0}, {"startOffset": 2410, "endOffset": 2754, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2831", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/AnimatedIcon.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "AnimatedIcon", "ranges": [{"startOffset": 1088, "endOffset": 2004, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2832", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AutoAcceptIndicator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "AutoAcceptIndicator", "ranges": [{"startOffset": 1107, "endOffset": 2883, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2833", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ShellModeIndicator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "ShellModeIndicator", "ranges": [{"startOffset": 916, "endOffset": 2042, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2834", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/InputPrompt.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "InputPrompt", "ranges": [{"startOffset": 2569, "endOffset": 18090, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2835", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/SuggestionsDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 260, "endOffset": 317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 366, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "SuggestionsDisplay", "ranges": [{"startOffset": 1035, "endOffset": 8712, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2836", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useInputHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "useInputHistory", "ranges": [{"startOffset": 590, "endOffset": 2698, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2840", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useShellHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistoryFilePath", "ranges": [{"startOffset": 1076, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "readHistoryFile", "ranges": [{"startOffset": 1269, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeHistoryFile", "ranges": [{"startOffset": 1651, "endOffset": 1969, "count": 0}], "isBlockCoverage": false}, {"functionName": "useShellHistory", "ranges": [{"startOffset": 1970, "endOffset": 3787, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2841", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useCompletion.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCompletion", "ranges": [{"startOffset": 1366, "endOffset": 13229, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2842", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useKeypress.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "useKeypress", "ranges": [{"startOffset": 896, "endOffset": 2745, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2843", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/commandUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 338, "endOffset": 386, "count": 0}], "isBlockCoverage": false}, {"functionName": "isAtCommand", "ranges": [{"startOffset": 497, "endOffset": 607, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSlashCommand", "ranges": [{"startOffset": 632, "endOffset": 664, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2844", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Footer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 20}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "Footer", "ranges": [{"startOffset": 1525, "endOffset": 11880, "count": 20}, {"startOffset": 2877, "endOffset": 3291, "count": 0}, {"startOffset": 3725, "endOffset": 4407, "count": 0}, {"startOffset": 4879, "endOffset": 4942, "count": 0}, {"startOffset": 4943, "endOffset": 5340, "count": 0}, {"startOffset": 5404, "endOffset": 6291, "count": 0}, {"startOffset": 8773, "endOffset": 9853, "count": 0}, {"startOffset": 9899, "endOffset": 10934, "count": 0}, {"startOffset": 10960, "endOffset": 11272, "count": 0}], "isBlockCoverage": true}, {"functionName": "getContextColor", "ranges": [{"startOffset": 1877, "endOffset": 2090, "count": 20}, {"startOffset": 1911, "endOffset": 1957, "count": 0}, {"startOffset": 1984, "endOffset": 2033, "count": 0}], "isBlockCoverage": true}, {"functionName": "getContextIndicator", "ranges": [{"startOffset": 2122, "endOffset": 2226, "count": 20}, {"startOffset": 2156, "endOffset": 2168, "count": 0}, {"startOffset": 2195, "endOffset": 2207, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2845", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ConsoleSummaryDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "ConsoleSummaryDisplay", "ranges": [{"startOffset": 925, "endOffset": 2162, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2846", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/MemoryUsageDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "MemoryUsageDisplay", "ranges": [{"startOffset": 1376, "endOffset": 3489, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2847", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ThemeDialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32510, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32510, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "ThemeDialog", "ranges": [{"startOffset": 1757, "endOffset": 14090, "count": 2}, {"startOffset": 3565, "endOffset": 3606, "count": 0}, {"startOffset": 3671, "endOffset": 3837, "count": 0}, {"startOffset": 4416, "endOffset": 4442, "count": 0}, {"startOffset": 5078, "endOffset": 5094, "count": 0}, {"startOffset": 5364, "endOffset": 5367, "count": 0}, {"startOffset": 5778, "endOffset": 5781, "count": 0}, {"startOffset": 5823, "endOffset": 5826, "count": 0}, {"startOffset": 6419, "endOffset": 6425, "count": 0}, {"startOffset": 7864, "endOffset": 9487, "count": 0}, {"startOffset": 10517, "endOffset": 10520, "count": 0}, {"startOffset": 10572, "endOffset": 10575, "count": 0}, {"startOffset": 13365, "endOffset": 13390, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2077, "endOffset": 2315, "count": 26}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2466, "endOffset": 2558, "count": 8}], "isBlockCoverage": true}, {"functionName": "handleThemeSelect", "ranges": [{"startOffset": 2789, "endOffset": 2849, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScopeHighlight", "ranges": [{"startOffset": 2882, "endOffset": 2962, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScopeSelect", "ranges": [{"startOffset": 2992, "endOffset": 3073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3211, "endOffset": 3398, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2848", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/RadioButtonSelect.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12342, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12342, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 2}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "RadioButtonSelect", "ranges": [{"startOffset": 1087, "endOffset": 4562, "count": 2}, {"startOffset": 4001, "endOffset": 4005, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleSelect", "ranges": [{"startOffset": 1283, "endOffset": 1324, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleHighlight", "ranges": [{"startOffset": 1352, "endOffset": 1427, "count": 0}], "isBlockCoverage": false}, {"functionName": "DynamicRadioIndicator", "ranges": [{"startOffset": 1431, "endOffset": 2277, "count": 26}, {"startOffset": 1726, "endOffset": 1768, "count": 2}, {"startOffset": 1769, "endOffset": 1810, "count": 24}, {"startOffset": 1833, "endOffset": 1838, "count": 2}, {"startOffset": 1839, "endOffset": 1844, "count": 24}], "isBlockCoverage": true}, {"functionName": "CustomThemeItemComponent", "ranges": [{"startOffset": 2280, "endOffset": 3970, "count": 26}, {"startOffset": 2491, "endOffset": 2558, "count": 2}, {"startOffset": 2558, "endOffset": 2666, "count": 24}, {"startOffset": 2606, "endOffset": 2666, "count": 0}, {"startOffset": 3603, "endOffset": 3969, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2856", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/DiffRenderer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 2}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseDiffWithLineNumbers", "ranges": [{"startOffset": 1272, "endOffset": 2953, "count": 2}, {"startOffset": 1546, "endOffset": 2933, "count": 14}, {"startOffset": 1618, "endOffset": 1864, "count": 0}, {"startOffset": 1919, "endOffset": 1945, "count": 12}, {"startOffset": 1946, "endOffset": 1978, "count": 12}, {"startOffset": 1979, "endOffset": 2007, "count": 12}, {"startOffset": 2008, "endOffset": 2046, "count": 12}, {"startOffset": 2047, "endOffset": 2080, "count": 12}, {"startOffset": 2081, "endOffset": 2112, "count": 12}, {"startOffset": 2113, "endOffset": 2148, "count": 12}, {"startOffset": 2149, "endOffset": 2188, "count": 12}, {"startOffset": 2198, "endOffset": 2207, "count": 2}, {"startOffset": 2207, "endOffset": 2229, "count": 12}, {"startOffset": 2229, "endOffset": 2929, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3004, "endOffset": 5269, "count": 2}, {"startOffset": 3172, "endOffset": 3564, "count": 0}, {"startOffset": 4406, "endOffset": 5268, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4451, "endOffset": 4614, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4705, "endOffset": 4734, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4740, "endOffset": 4762, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderDiffContent", "ranges": [{"startOffset": 5297, "endOffset": 11501, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLanguageFromExtension", "ranges": [{"startOffset": 11536, "endOffset": 11892, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2857", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/CodeColorizer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 2}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "renderHastNode", "ranges": [{"startOffset": 1534, "endOffset": 3600, "count": 78}, {"startOffset": 1617, "endOffset": 1960, "count": 46}, {"startOffset": 1960, "endOffset": 1993, "count": 32}, {"startOffset": 1993, "endOffset": 3072, "count": 20}, {"startOffset": 2046, "endOffset": 2051, "count": 0}, {"startOffset": 2138, "endOffset": 2272, "count": 22}, {"startOffset": 2212, "endOffset": 2266, "count": 20}, {"startOffset": 2315, "endOffset": 2332, "count": 0}, {"startOffset": 3072, "endOffset": 3582, "count": 12}, {"startOffset": 3158, "endOffset": 3184, "count": 0}, {"startOffset": 3582, "endOffset": 3599, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2381, "endOffset": 2750, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3216, "endOffset": 3576, "count": 46}], "isBlockCoverage": true}, {"functionName": "colorizeCode", "ranges": [{"startOffset": 3601, "endOffset": 8195, "count": 2}, {"startOffset": 4104, "endOffset": 4252, "count": 0}, {"startOffset": 6381, "endOffset": 8193, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHighlightedLines", "ranges": [{"startOffset": 4292, "endOffset": 4413, "count": 12}, {"startOffset": 4346, "endOffset": 4376, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4699, "endOffset": 6132, "count": 12}, {"startOffset": 4932, "endOffset": 4938, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6838, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3058", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 2}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 2}, {"startOffset": 495, "endOffset": 503, "count": 0}], "isBlockCoverage": true}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1813, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1884, "endOffset": 2611, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2632, "endOffset": 7251, "count": 2}, {"startOffset": 2989, "endOffset": 3015, "count": 0}, {"startOffset": 3073, "endOffset": 3150, "count": 0}, {"startOffset": 3931, "endOffset": 3960, "count": 0}, {"startOffset": 3961, "endOffset": 3982, "count": 0}, {"startOffset": 4124, "endOffset": 4127, "count": 0}, {"startOffset": 4541, "endOffset": 4691, "count": 0}, {"startOffset": 5933, "endOffset": 5963, "count": 0}, {"startOffset": 5964, "endOffset": 6465, "count": 0}, {"startOffset": 6510, "endOffset": 6543, "count": 0}, {"startOffset": 6544, "endOffset": 7044, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRows", "ranges": [{"startOffset": 3154, "endOffset": 3670, "count": 12}, {"startOffset": 3248, "endOffset": 3269, "count": 0}, {"startOffset": 3328, "endOffset": 3438, "count": 0}, {"startOffset": 3587, "endOffset": 3669, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4241, "endOffset": 4426, "count": 1}, {"startOffset": 4279, "endOffset": 4316, "count": 0}, {"startOffset": 4349, "endOffset": 4355, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4375, "endOffset": 4421, "count": 1}, {"startOffset": 4408, "endOffset": 4414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4758, "endOffset": 5745, "count": 12}, {"startOffset": 5246, "endOffset": 5545, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4894, "endOffset": 5244, "count": 58}], "isBlockCoverage": true}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 7253, "endOffset": 10048, "count": 12}, {"startOffset": 7393, "endOffset": 7596, "count": 0}, {"startOffset": 7620, "endOffset": 8168, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRowChild", "ranges": [{"startOffset": 8264, "endOffset": 9905, "count": 214}, {"startOffset": 8339, "endOffset": 8360, "count": 0}, {"startOffset": 8399, "endOffset": 8430, "count": 156}, {"startOffset": 8432, "endOffset": 9058, "count": 58}, {"startOffset": 8488, "endOffset": 8513, "count": 0}, {"startOffset": 8564, "endOffset": 8569, "count": 0}, {"startOffset": 8638, "endOffset": 8714, "count": 46}, {"startOffset": 8714, "endOffset": 9037, "count": 12}, {"startOffset": 8806, "endOffset": 9029, "count": 0}, {"startOffset": 9058, "endOffset": 9125, "count": 156}, {"startOffset": 9125, "endOffset": 9200, "count": 0}, {"startOffset": 9200, "endOffset": 9260, "count": 156}, {"startOffset": 9260, "endOffset": 9430, "count": 86}, {"startOffset": 9430, "endOffset": 9486, "count": 70}, {"startOffset": 9486, "endOffset": 9615, "count": 0}, {"startOffset": 9615, "endOffset": 9722, "count": 70}, {"startOffset": 9722, "endOffset": 9736, "count": 24}, {"startOffset": 9737, "endOffset": 9774, "count": 46}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9357, "endOffset": 9401, "count": 86}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9850, "endOffset": 9894, "count": 104}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9988, "endOffset": 10027, "count": 24}], "isBlockCoverage": true}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 10049, "endOffset": 14127, "count": 12}, {"startOffset": 10184, "endOffset": 10218, "count": 0}, {"startOffset": 10220, "endOffset": 10258, "count": 0}, {"startOffset": 10533, "endOffset": 11190, "count": 0}, {"startOffset": 11271, "endOffset": 11325, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10369, "endOffset": 10497, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10615, "endOffset": 10928, "count": 0}], "isBlockCoverage": false}, {"functionName": "addWrappingPartToLines", "ranges": [{"startOffset": 11383, "endOffset": 11796, "count": 12}, {"startOffset": 11514, "endOffset": 11741, "count": 0}], "isBlockCoverage": true}, {"functionName": "addToWrappingPart", "ranges": [{"startOffset": 11799, "endOffset": 12055, "count": 104}, {"startOffset": 11873, "endOffset": 11929, "count": 92}, {"startOffset": 11931, "endOffset": 11996, "count": 58}, {"startOffset": 11996, "endOffset": 12051, "count": 46}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12079, "endOffset": 13998, "count": 46}, {"startOffset": 13955, "endOffset": 13994, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12178, "endOffset": 13915, "count": 46}, {"startOffset": 12230, "endOffset": 12273, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12340, "endOffset": 13907, "count": 126}, {"startOffset": 12371, "endOffset": 12378, "count": 22}, {"startOffset": 12378, "endOffset": 12505, "count": 104}, {"startOffset": 12505, "endOffset": 12529, "count": 0}, {"startOffset": 12531, "endOffset": 12646, "count": 0}, {"startOffset": 12646, "endOffset": 12688, "count": 104}, {"startOffset": 12688, "endOffset": 13790, "count": 0}, {"startOffset": 13790, "endOffset": 13899, "count": 104}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3059", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 2}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1212, "endOffset": 1276, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1305, "endOffset": 1371, "count": 2}], "isBlockCoverage": true}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1398, "endOffset": 3012, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3060", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/settings.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 260, "endOffset": 317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 416, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 465, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 560, "endOffset": 606, "count": 10}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 699, "count": 16}, {"startOffset": 689, "endOffset": 697, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 742, "endOffset": 788, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 877, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2236, "endOffset": 2360, "count": 1}], "isBlockCoverage": true}, {"functionName": "LoadedSettings", "ranges": [{"startOffset": 2408, "endOffset": 2579, "count": 16}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2582, "endOffset": 2620, "count": 16}], "isBlockCoverage": true}, {"functionName": "get merged", "ranges": [{"startOffset": 2624, "endOffset": 2667, "count": 98}], "isBlockCoverage": true}, {"functionName": "computeMergedSettings", "ranges": [{"startOffset": 2670, "endOffset": 2781, "count": 16}], "isBlockCoverage": true}, {"functionName": "forScope", "ranges": [{"startOffset": 2784, "endOffset": 3026, "count": 2}, {"startOffset": 2829, "endOffset": 2878, "count": 0}, {"startOffset": 2956, "endOffset": 3016, "count": 0}], "isBlockCoverage": true}, {"functionName": "setValue", "ranges": [{"startOffset": 3029, "endOffset": 3230, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveEnvVarsInString", "ranges": [{"startOffset": 3234, "endOffset": 3579, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveEnvVarsInObject", "ranges": [{"startOffset": 3580, "endOffset": 4178, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadSettings", "ranges": [{"startOffset": 4179, "endOffset": 6267, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveSettings", "ranges": [{"startOffset": 6269, "endOffset": 6743, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3062", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AuthDialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "AuthDialog", "ranges": [{"startOffset": 1646, "endOffset": 7279, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3063", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/auth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4522, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4522, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateAuthMethod", "ranges": [{"startOffset": 731, "endOffset": 1789, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3064", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/config.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45419, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45419, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 264, "endOffset": 325, "count": 1}, {"startOffset": 315, "endOffset": 323, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 416, "count": 1}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "isDebugMode", "ranges": [{"startOffset": 2379, "endOffset": 2506, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 2599, "endOffset": 2733, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 2808, "endOffset": 2895, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 2970, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 3134, "endOffset": 3224, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseArguments", "ranges": [{"startOffset": 3228, "endOffset": 5486, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadHierarchicalArienMemory", "ranges": [{"startOffset": 5487, "endOffset": 5919, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCliConfig", "ranges": [{"startOffset": 5921, "endOffset": 8847, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateMcpServerConfig", "ranges": [{"startOffset": 8849, "endOffset": 9389, "count": 0}], "isBlockCoverage": false}, {"functionName": "addMcpServer", "ranges": [{"startOffset": 9390, "endOffset": 9968, "count": 0}], "isBlockCoverage": false}, {"functionName": "mergeMcpServers", "ranges": [{"startOffset": 9969, "endOffset": 13124, "count": 0}], "isBlockCoverage": false}, {"functionName": "findEnvFile", "ranges": [{"startOffset": 13125, "endOffset": 14209, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadEnvironment", "ranges": [{"startOffset": 14210, "endOffset": 14407, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3112", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/sandboxConfig.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSandboxCommand", "ranges": [{"startOffset": 884, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandboxCommand", "ranges": [{"startOffset": 970, "endOffset": 2496, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadSandboxConfig", "ranges": [{"startOffset": 2497, "endOffset": 2902, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3119", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/built-in-mcp-servers.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 366, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 475, "endOffset": 529, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 584, "endOffset": 642, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 698, "endOffset": 757, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 813, "endOffset": 872, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBuiltInMcpServers", "ranges": [{"startOffset": 1290, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBuiltInMcpServersSync", "ranges": [{"startOffset": 1925, "endOffset": 2002, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConfigurableMcpServers", "ranges": [{"startOffset": 2004, "endOffset": 2086, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExperimentalMcpServers", "ranges": [{"startOffset": 2088, "endOffset": 2203, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3120", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/mcp-server-registry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 59147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 59147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 2}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 755, "endOffset": 6220, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVerifiedServers", "ranges": [{"startOffset": 6313, "endOffset": 6498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6410, "endOffset": 6472, "count": 6}], "isBlockCoverage": true}, {"functionName": "getExperimentalServers", "ranges": [{"startOffset": 6577, "endOffset": 6770, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConfigurableServers", "ranges": [{"startOffset": 6854, "endOffset": 7047, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6959, "endOffset": 7021, "count": 1}], "isBlockCoverage": true}, {"functionName": "getServerEntry", "ranges": [{"startOffset": 7117, "endOffset": 7253, "count": 0}], "isBlockCoverage": false}, {"functionName": "getServersByCategory", "ranges": [{"startOffset": 7309, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "getServersByTags", "ranges": [{"startOffset": 7660, "endOffset": 7969, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkServerRequirements", "ranges": [{"startOffset": 8041, "endOffset": 9254, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationInstructions", "ranges": [{"startOffset": 9325, "endOffset": 10553, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFallbackConfiguration", "ranges": [{"startOffset": 10653, "endOffset": 14401, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkCommandAvailability", "ranges": [{"startOffset": 14475, "endOffset": 15016, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCompatibleServers", "ranges": [{"startOffset": 15102, "endOffset": 18457, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3121", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/dependency-installer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 840, "endOffset": 2461, "count": 1}], "isBlockCoverage": true}, {"functionName": "installDependency", "ranges": [{"startOffset": 2544, "endOffset": 5162, "count": 0}], "isBlockCoverage": false}, {"functionName": "canAutoInstall", "ranges": [{"startOffset": 5243, "endOffset": 5433, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationInstructions", "ranges": [{"startOffset": 5508, "endOffset": 5763, "count": 0}], "isBlockCoverage": false}, {"functionName": "installMultipleDependencies", "ranges": [{"startOffset": 5832, "endOffset": 6702, "count": 0}], "isBlockCoverage": false}, {"functionName": "summarizeInstallationResults", "ranges": [{"startOffset": 6772, "endOffset": 7498, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortOptionsByPlatform", "ranges": [{"startOffset": 7575, "endOffset": 8242, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWindowsScore", "ranges": [{"startOffset": 8252, "endOffset": 8555, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMacScore", "ranges": [{"startOffset": 8565, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLinuxScore", "ranges": [{"startOffset": 8872, "endOffset": 9171, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdated<PERSON>ath", "ranges": [{"startOffset": 9265, "endOffset": 9970, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3122", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AuthInProgress.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "AuthInProgress", "ranges": [{"startOffset": 1283, "endOffset": 3614, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3125", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/EditorSettingsDialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "EditorSettingsDialog", "ranges": [{"startOffset": 1722, "endOffset": 11551, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3126", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/editors/editorSettingsManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5452, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5452, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 363, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 912, "endOffset": 928, "count": 1}], "isBlockCoverage": true}, {"functionName": "EditorSettingsManager", "ranges": [{"startOffset": 932, "endOffset": 1728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1217, "endOffset": 1716, "count": 6}, {"startOffset": 1446, "endOffset": 1477, "count": 0}, {"startOffset": 1517, "endOffset": 1537, "count": 4}, {"startOffset": 1538, "endOffset": 1551, "count": 2}, {"startOffset": 1675, "endOffset": 1697, "count": 2}], "isBlockCoverage": true}, {"functionName": "getAvailableEditorDisplays", "ranges": [{"startOffset": 1731, "endOffset": 1799, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3127", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Help.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 0}], "isBlockCoverage": false}, {"functionName": "Help", "ranges": [{"startOffset": 874, "endOffset": 20148, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3128", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Tips.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 20}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}, {"functionName": "Tips", "ranges": [{"startOffset": 874, "endOffset": 7063, "count": 10}, {"startOffset": 4299, "endOffset": 5491, "count": 4}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3129", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ConsolePatcher.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 20}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "useConsolePatcher", "ranges": [{"startOffset": 733, "endOffset": 1903, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 810, "endOffset": 1872, "count": 10}], "isBlockCoverage": true}, {"functionName": "formatArgs", "ranges": [{"startOffset": 1027, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchConsoleMethod", "ranges": [{"startOffset": 1115, "endOffset": 1396, "count": 40}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1141, "endOffset": 1396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1684, "endOffset": 1867, "count": 10}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3130", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/DetailedMessagesDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11251, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11251, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 260, "endOffset": 317, "count": 0}], "isBlockCoverage": false}, {"functionName": "DetailedMessagesDisplay", "ranges": [{"startOffset": 1069, "endOffset": 5682, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3131", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/HistoryItemDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "HistoryItemDisplay", "ranges": [{"startOffset": 2386, "endOffset": 7334, "count": 1}, {"startOffset": 2631, "endOffset": 2940, "count": 0}, {"startOffset": 2971, "endOffset": 3285, "count": 0}, {"startOffset": 3311, "endOffset": 3735, "count": 0}, {"startOffset": 3769, "endOffset": 4200, "count": 0}, {"startOffset": 4560, "endOffset": 4870, "count": 0}, {"startOffset": 4896, "endOffset": 5449, "count": 0}, {"startOffset": 5475, "endOffset": 5905, "count": 0}, {"startOffset": 5930, "endOffset": 6276, "count": 0}, {"startOffset": 6307, "endOffset": 6779, "count": 0}, {"startOffset": 6811, "endOffset": 7140, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3132", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/UserMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "UserMessage", "ranges": [{"startOffset": 895, "endOffset": 2701, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3133", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/UserShellMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "UserShellMessage", "ranges": [{"startOffset": 910, "endOffset": 2012, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3134", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienMessage", "ranges": [{"startOffset": 1165, "endOffset": 3123, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3135", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/MarkdownDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapTextAtWordBoundaries", "ranges": [{"startOffset": 1582, "endOffset": 2463, "count": 0}], "isBlockCoverage": false}, {"functionName": "MarkdownDisplayInternal", "ranges": [{"startOffset": 2497, "endOffset": 13454, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderInlineInternal", "ranges": [{"startOffset": 13485, "endOffset": 20366, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderCodeBlockInternal", "ranges": [{"startOffset": 20479, "endOffset": 23477, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderListItemInternal", "ranges": [{"startOffset": 23595, "endOffset": 25863, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3136", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/InfoMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "InfoMessage", "ranges": [{"startOffset": 895, "endOffset": 3045, "count": 1}, {"startOffset": 1148, "endOffset": 1190, "count": 0}, {"startOffset": 1268, "endOffset": 1310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3137", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ErrorMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7971, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7971, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "ErrorMessage", "ranges": [{"startOffset": 898, "endOffset": 4482, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3138", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ToolGroupMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToolGroupMessage", "ranges": [{"startOffset": 1506, "endOffset": 5798, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3139", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ToolMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToolMessage", "ranges": [{"startOffset": 3148, "endOffset": 10145, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10210, "endOffset": 11883, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11995, "endOffset": 14732, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14831, "endOffset": 15496, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3140", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ToolConfirmationMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 260, "endOffset": 317, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToolConfirmationMessage", "ranges": [{"startOffset": 1571, "endOffset": 14926, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3141", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessageContent.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienMessageContent", "ranges": [{"startOffset": 937, "endOffset": 2055, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3142", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/CompressionMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "CompressionMessage", "ranges": [{"startOffset": 1251, "endOffset": 3681, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3143", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AboutBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "AboutBox", "ranges": [{"startOffset": 1156, "endOffset": 15046, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3144", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/Separator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 335, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 431, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "Separator", "ranges": [{"startOffset": 1081, "endOffset": 1853, "count": 0}], "isBlockCoverage": false}, {"functionName": "DottedSeparator", "ranges": [{"startOffset": 1879, "endOffset": 2723, "count": 0}], "isBlockCoverage": false}, {"functionName": "SectionSeparator", "ranges": [{"startOffset": 2750, "endOffset": 4381, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3145", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/StatsDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatsDisplay", "ranges": [{"startOffset": 1188, "endOffset": 6975, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3146", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Stats.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 372, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 417, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatRow", "ranges": [{"startOffset": 1063, "endOffset": 2054, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatsColumn", "ranges": [{"startOffset": 2076, "endOffset": 6609, "count": 0}], "isBlockCoverage": false}, {"functionName": "DurationColumn", "ranges": [{"startOffset": 6634, "endOffset": 8198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3147", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/SessionSummaryDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "SessionSummaryDisplay", "ranges": [{"startOffset": 1382, "endOffset": 8525, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3148", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ContextSummaryDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 18}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "ContextSummaryDisplay", "ranges": [{"startOffset": 1102, "endOffset": 4873, "count": 18}, {"startOffset": 1238, "endOffset": 1243, "count": 14}, {"startOffset": 1282, "endOffset": 1305, "count": 12}, {"startOffset": 1307, "endOffset": 1751, "count": 9}, {"startOffset": 1751, "endOffset": 2409, "count": 6}, {"startOffset": 1850, "endOffset": 1871, "count": 5}, {"startOffset": 1872, "endOffset": 1883, "count": 1}, {"startOffset": 2159, "endOffset": 2164, "count": 4}, {"startOffset": 2165, "endOffset": 2169, "count": 2}, {"startOffset": 2409, "endOffset": 2437, "count": 9}, {"startOffset": 2437, "endOffset": 3438, "count": 4}, {"startOffset": 2675, "endOffset": 2680, "count": 2}, {"startOffset": 2681, "endOffset": 2685, "count": 2}, {"startOffset": 2917, "endOffset": 2938, "count": 0}, {"startOffset": 3438, "endOffset": 4872, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3931, "endOffset": 4663, "count": 10}, {"startOffset": 4070, "endOffset": 4433, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3149", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useHistoryManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 20}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "useHistory", "ranges": [{"startOffset": 589, "endOffset": 3787, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 817, "endOffset": 935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1002, "endOffset": 1051, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1119, "endOffset": 3136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1260, "endOffset": 3111, "count": 1}, {"startOffset": 1315, "endOffset": 3060, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2433, "endOffset": 2967, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3230, "endOffset": 3548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3621, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3150", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useGitBranchName.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 20}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "useGitBranchName", "ranges": [{"startOffset": 1189, "endOffset": 2793, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1373, "endOffset": 2040, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1447, "endOffset": 2034, "count": 10}, {"startOffset": 1558, "endOffset": 1648, "count": 0}, {"startOffset": 1650, "endOffset": 2026, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1793, "endOffset": 2003, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2110, "endOffset": 2744, "count": 10}], "isBlockCoverage": true}, {"functionName": "setupWatcher", "ranges": [{"startOffset": 2276, "endOffset": 2669, "count": 10}, {"startOffset": 2416, "endOffset": 2634, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2488, "endOffset": 2623, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2702, "endOffset": 2739, "count": 10}, {"startOffset": 2723, "endOffset": 2730, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3151", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useBracketedPaste.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3455, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3455, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 20}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "useBracketedPaste", "ranges": [{"startOffset": 700, "endOffset": 1190, "count": 20}], "isBlockCoverage": true}, {"functionName": "cleanup", "ranges": [{"startOffset": 726, "endOffset": 788, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 828, "endOffset": 1182, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1001, "endOffset": 1177, "count": 10}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3152", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/text-buffer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 149560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 149560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 351, "endOffset": 398, "count": 20}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "isWordChar", "ranges": [{"startOffset": 1988, "endOffset": 2093, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripUnsafeCharacters", "ranges": [{"startOffset": 2094, "endOffset": 2551, "count": 0}], "isBlockCoverage": false}, {"functionName": "clamp", "ranges": [{"startOffset": 2552, "endOffset": 2627, "count": 0}], "isBlockCoverage": false}, {"functionName": "dbg", "ranges": [{"startOffset": 2729, "endOffset": 2815, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateInitialCursorPosition", "ranges": [{"startOffset": 2816, "endOffset": 3460, "count": 10}, {"startOffset": 3122, "endOffset": 3125, "count": 0}, {"startOffset": 3213, "endOffset": 3459, "count": 0}], "isBlockCoverage": true}, {"functionName": "offsetToLogicalPos", "ranges": [{"startOffset": 3461, "endOffset": 4445, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateVisualLayout", "ranges": [{"startOffset": 4447, "endOffset": 9779, "count": 10}, {"startOffset": 9101, "endOffset": 9279, "count": 0}, {"startOffset": 9318, "endOffset": 9657, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4675, "endOffset": 8974, "count": 10}, {"startOffset": 5052, "endOffset": 8970, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTextBuffer", "ranges": [{"startOffset": 9780, "endOffset": 41080, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9981, "endOffset": 10069, "count": 10}, {"startOffset": 10054, "endOffset": 10060, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10161, "endOffset": 10225, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11351, "endOffset": 11372, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11459, "endOffset": 11513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11576, "endOffset": 11890, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11978, "endOffset": 12385, "count": 20}, {"startOffset": 12108, "endOffset": 12159, "count": 0}, {"startOffset": 12214, "endOffset": 12278, "count": 0}, {"startOffset": 12328, "endOffset": 12381, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12488, "endOffset": 12851, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12966, "endOffset": 13138, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13223, "endOffset": 13282, "count": 10}, {"startOffset": 13249, "endOffset": 13278, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13356, "endOffset": 13657, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13770, "endOffset": 14071, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14194, "endOffset": 15453, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15603, "endOffset": 18900, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19028, "endOffset": 19755, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19886, "endOffset": 20017, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20119, "endOffset": 20292, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20388, "endOffset": 21208, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21398, "endOffset": 21843, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21948, "endOffset": 24760, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24898, "endOffset": 25907, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26049, "endOffset": 26824, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27004, "endOffset": 27439, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27617, "endOffset": 27974, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28099, "endOffset": 33809, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 34081, "endOffset": 35453, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35574, "endOffset": 38019, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 38286, "endOffset": 38361, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38494, "endOffset": 38848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 38946, "endOffset": 39183, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 39818, "endOffset": 40643, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 40763, "endOffset": 40855, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 40947, "endOffset": 40995, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3153", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/UpdateNotification.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "UpdateNotification", "ranges": [{"startOffset": 916, "endOffset": 2469, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3154", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/updateCheck.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3920, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3920, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 10}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "checkForUpdates", "ranges": [{"startOffset": 706, "endOffset": 1500, "count": 10}, {"startOffset": 890, "endOffset": 916, "count": 0}, {"startOffset": 1233, "endOffset": 1388, "count": 0}, {"startOffset": 1411, "endOffset": 1498, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "3290", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/ShowMoreLines.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "ShowMoreLines", "ranges": [{"startOffset": 1297, "endOffset": 3684, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3291", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/privacy/PrivacyNotice.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6201, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6201, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "PrivacyNoticeText", "ranges": [{"startOffset": 1415, "endOffset": 2721, "count": 0}], "isBlockCoverage": false}, {"functionName": "PrivacyNotice", "ranges": [{"startOffset": 2745, "endOffset": 3372, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3292", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/privacy/ArienPrivacyNotice.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienPrivacyNotice", "ranges": [{"startOffset": 933, "endOffset": 8055, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3293", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/privacy/CloudPaidPrivacyNotice.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "CloudPaidPrivacyNotice", "ranges": [{"startOffset": 949, "endOffset": 6305, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3294", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/privacy/CloudFreePrivacyNotice.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "CloudFreePrivacyNotice", "ranges": [{"startOffset": 1388, "endOffset": 11092, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3295", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/usePrivacySettings.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "usePrivacySettings", "ranges": [{"startOffset": 1038, "endOffset": 2629, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistServer", "ranges": [{"startOffset": 2631, "endOffset": 2950, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTier", "ranges": [{"startOffset": 2951, "endOffset": 3371, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRemoteDataCollectionOptIn", "ranges": [{"startOffset": 3372, "endOffset": 3711, "count": 0}], "isBlockCoverage": false}, {"functionName": "setRemoteDataCollectionOptIn", "ranges": [{"startOffset": 3712, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "3296", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/renderingCoordinator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 60}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "deduplicatePendingItems", "ranges": [{"startOffset": 586, "endOffset": 1118, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 688, "endOffset": 728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 795, "endOffset": 855, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentHash", "ranges": [{"startOffset": 1254, "endOffset": 1487, "count": 1}, {"startOffset": 1368, "endOffset": 1486, "count": 0}], "isBlockCoverage": true}, {"functionName": "areItemsEqual", "ranges": [{"startOffset": 1576, "endOffset": 1689, "count": 0}], "isBlockCoverage": false}, {"functionName": "optimizeItemOrder", "ranges": [{"startOffset": 1829, "endOffset": 2443, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1883, "endOffset": 2437, "count": 0}], "isBlockCoverage": false}, {"functionName": "validatePendingItems", "ranges": [{"startOffset": 2542, "endOffset": 3324, "count": 20}, {"startOffset": 2648, "endOffset": 2809, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2842, "endOffset": 3318, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateStableKey", "ranges": [{"startOffset": 3470, "endOffset": 3671, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldRenderInStatic", "ranges": [{"startOffset": 3768, "endOffset": 4050, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}