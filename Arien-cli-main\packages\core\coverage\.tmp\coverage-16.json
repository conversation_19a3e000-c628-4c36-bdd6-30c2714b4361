{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/metrics.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22820, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22820, "count": 1}], "isBlockCoverage": true}, {"functionName": "originalOtelMockFactory", "ranges": [{"startOffset": 1143, "endOffset": 1301, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1358, "endOffset": 7626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1512, "endOffset": 2586, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1629, "endOffset": 1786, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2653, "endOffset": 5297, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSessionId", "ranges": [{"startOffset": 2706, "endOffset": 2729, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2818, "endOffset": 2989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3082, "endOffset": 3630, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3715, "endOffset": 4861, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4931, "endOffset": 5291, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5366, "endOffset": 7622, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSessionId", "ranges": [{"startOffset": 5419, "endOffset": 5442, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5531, "endOffset": 5787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5874, "endOffset": 6587, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6674, "endOffset": 7041, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7127, "endOffset": 7616, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/metrics.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17613, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17613, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 7}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 8}, {"startOffset": 467, "endOffset": 475, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 8}, {"startOffset": 685, "endOffset": 693, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 8}, {"startOffset": 1020, "endOffset": 1028, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1588, "endOffset": 1749, "count": 9}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 1976, "endOffset": 2072, "count": 15}], "isBlockCoverage": true}, {"functionName": "getMeter", "ranges": [{"startOffset": 2073, "endOffset": 2234, "count": 6}], "isBlockCoverage": true}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2236, "endOffset": 4028, "count": 6}, {"startOffset": 2301, "endOffset": 2308, "count": 0}, {"startOffset": 2351, "endOffset": 2358, "count": 0}], "isBlockCoverage": true}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4030, "endOffset": 4502, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4504, "endOffset": 4732, "count": 7}, {"startOffset": 4597, "endOffset": 4621, "count": 6}, {"startOffset": 4623, "endOffset": 4630, "count": 1}, {"startOffset": 4630, "endOffset": 4731, "count": 6}], "isBlockCoverage": true}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 4734, "endOffset": 5196, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5198, "endOffset": 5684, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 5686, "endOffset": 6125, "count": 4}, {"startOffset": 5798, "endOffset": 5822, "count": 3}, {"startOffset": 5824, "endOffset": 5831, "count": 1}, {"startOffset": 5831, "endOffset": 5934, "count": 3}, {"startOffset": 5934, "endOffset": 5959, "count": 1}, {"startOffset": 5959, "endOffset": 5987, "count": 3}, {"startOffset": 5987, "endOffset": 6018, "count": 2}, {"startOffset": 6018, "endOffset": 6047, "count": 3}, {"startOffset": 6047, "endOffset": 6080, "count": 1}, {"startOffset": 6080, "endOffset": 6124, "count": 3}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/constants.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4542, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4542, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 6}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 6}, {"startOffset": 980, "endOffset": 988, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 6}, {"startOffset": 1093, "endOffset": 1101, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 6}, {"startOffset": 1206, "endOffset": 1214, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 6}, {"startOffset": 1323, "endOffset": 1331, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 6}, {"startOffset": 1424, "endOffset": 1432, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 6}, {"startOffset": 1529, "endOffset": 1537, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 6}, {"startOffset": 1648, "endOffset": 1656, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}