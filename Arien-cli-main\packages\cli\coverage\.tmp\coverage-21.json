{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/LoadingIndicator.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 453, "endOffset": 1423, "count": 1}], "isBlockCoverage": true}, {"functionName": "ArienRespondingSpinner", "ranges": [{"startOffset": 488, "endOffset": 1420, "count": 10}, {"startOffset": 693, "endOffset": 1031, "count": 8}, {"startOffset": 1031, "endOffset": 1398, "count": 2}, {"startOffset": 1398, "endOffset": 1419, "count": 0}], "isBlockCoverage": true}, {"functionName": "renderWithContext", "ranges": [{"startOffset": 2475, "endOffset": 2922, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2957, "endOffset": 12979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3108, "endOffset": 3564, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3649, "endOffset": 4261, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4376, "endOffset": 5115, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5176, "endOffset": 5752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5820, "endOffset": 6390, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6442, "endOffset": 7259, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7328, "endOffset": 10595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10657, "endOffset": 11266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11317, "endOffset": 12110, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12181, "endOffset": 12975, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/LoadingIndicator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 13}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "LoadingIndicator", "ranges": [{"startOffset": 1321, "endOffset": 6619, "count": 13}, {"startOffset": 1539, "endOffset": 1561, "count": 3}, {"startOffset": 1561, "endOffset": 1592, "count": 10}, {"startOffset": 1592, "endOffset": 1601, "count": 2}, {"startOffset": 1602, "endOffset": 1625, "count": 8}, {"startOffset": 2774, "endOffset": 2779, "count": 2}, {"startOffset": 2780, "endOffset": 2784, "count": 8}, {"startOffset": 3288, "endOffset": 3708, "count": 10}, {"startOffset": 3709, "endOffset": 4135, "count": 0}, {"startOffset": 4357, "endOffset": 4378, "count": 2}, {"startOffset": 4379, "endOffset": 4416, "count": 8}, {"startOffset": 4966, "endOffset": 5288, "count": 1}, {"startOffset": 5522, "endOffset": 5603, "count": 1}, {"startOffset": 5604, "endOffset": 6415, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLoadingState", "ranges": [{"startOffset": 1653, "endOffset": 1954, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProgressIndicator", "ranges": [{"startOffset": 1987, "endOffset": 2173, "count": 10}, {"startOffset": 2020, "endOffset": 2030, "count": 0}, {"startOffset": 2056, "endOffset": 2068, "count": 2}, {"startOffset": 2068, "endOffset": 2095, "count": 8}, {"startOffset": 2095, "endOffset": 2108, "count": 6}, {"startOffset": 2108, "endOffset": 2149, "count": 2}, {"startOffset": 2149, "endOffset": 2172, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 20}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 10}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 10}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 20}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 20}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/StreamingContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 23}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 13}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStreamingContext", "ranges": [{"startOffset": 802, "endOffset": 1043, "count": 13}, {"startOffset": 914, "endOffset": 1022, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 57}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}