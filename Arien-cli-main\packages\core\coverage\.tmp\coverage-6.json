{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/prompts.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 372, "endOffset": 418, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 559, "endOffset": 597, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 647, "endOffset": 700, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 755, "endOffset": 802, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 863, "endOffset": 923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 974, "endOffset": 1028, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1084, "endOffset": 1135, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1189, "endOffset": 1249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1581, "endOffset": 6060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1684, "endOffset": 2037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2136, "endOffset": 2491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2593, "endOffset": 2957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3643, "endOffset": 4066, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4188, "endOffset": 4619, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4728, "endOffset": 5151, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5239, "endOffset": 5597, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5693, "endOffset": 6056, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/prompts.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 71023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 71023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 9}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 1906, "endOffset": 29283, "count": 9}, {"startOffset": 2167, "endOffset": 2180, "count": 0}, {"startOffset": 2202, "endOffset": 2242, "count": 0}, {"startOffset": 2244, "endOffset": 2510, "count": 0}, {"startOffset": 2549, "endOffset": 2615, "count": 0}, {"startOffset": 28814, "endOffset": 28827, "count": 0}, {"startOffset": 28854, "endOffset": 28899, "count": 0}, {"startOffset": 28901, "endOffset": 29135, "count": 0}, {"startOffset": 29171, "endOffset": 29202, "count": 2}, {"startOffset": 29203, "endOffset": 29234, "count": 1}, {"startOffset": 29235, "endOffset": 29239, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18717, "endOffset": 20401, "count": 9}, {"startOffset": 18917, "endOffset": 19441, "count": 1}, {"startOffset": 19441, "endOffset": 20397, "count": 8}, {"startOffset": 19469, "endOffset": 19987, "count": 1}, {"startOffset": 19987, "endOffset": 20397, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20408, "endOffset": 21895, "count": 9}, {"startOffset": 20511, "endOffset": 21875, "count": 1}, {"startOffset": 21875, "endOffset": 21894, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25328, "endOffset": 25544, "count": 9}, {"startOffset": 25431, "endOffset": 25524, "count": 1}, {"startOffset": 25524, "endOffset": 25543, "count": 8}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "323", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 9}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 9}, {"startOffset": 923, "endOffset": 931, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3261, "endOffset": 3562, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 3564, "endOffset": 3727, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 3729, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 3890, "endOffset": 4049, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4050, "endOffset": 4347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 4435, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 4478, "endOffset": 4626, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 4636, "endOffset": 6391, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6394, "endOffset": 7669, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}