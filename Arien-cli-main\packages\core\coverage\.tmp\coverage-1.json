{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errorReporting.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 827, "endOffset": 8142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 981, "endOffset": 1309, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1138, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1350, "endOffset": 1409, "count": 6}], "isBlockCoverage": true}, {"functionName": "getExpectedReportPath", "ranges": [{"startOffset": 1444, "endOffset": 1521, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1599, "endOffset": 2545, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2650, "endOffset": 3391, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3456, "endOffset": 4184, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4273, "endOffset": 5315, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5433, "endOffset": 7227, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5957, "endOffset": 6139, "count": 2}, {"startOffset": 6033, "endOffset": 6138, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7332, "endOffset": 8138, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errorReporting.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 6}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "reportError", "ranges": [{"startOffset": 695, "endOffset": 3299, "count": 6}, {"startOffset": 1094, "endOffset": 1167, "count": 4}, {"startOffset": 1167, "endOffset": 1374, "count": 2}, {"startOffset": 1203, "endOffset": 1220, "count": 1}, {"startOffset": 1221, "endOffset": 1242, "count": 1}, {"startOffset": 1244, "endOffset": 1374, "count": 1}, {"startOffset": 1441, "endOffset": 1483, "count": 3}, {"startOffset": 1600, "endOffset": 2508, "count": 1}, {"startOffset": 2331, "endOffset": 2491, "count": 0}, {"startOffset": 2508, "endOffset": 2606, "count": 5}, {"startOffset": 2606, "endOffset": 2688, "count": 4}, {"startOffset": 2688, "endOffset": 3297, "count": 1}, {"startOffset": 2999, "endOffset": 3287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}