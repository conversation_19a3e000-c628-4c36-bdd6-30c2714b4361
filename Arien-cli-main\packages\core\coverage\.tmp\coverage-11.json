{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-file.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29311, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29311, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 404, "endOffset": 679, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1376, "endOffset": 10129, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1506, "endOffset": 2097, "count": 15}], "isBlockCoverage": true}, {"functionName": "getFileService", "ranges": [{"startOffset": 1945, "endOffset": 1962, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2299, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2361, "endOffset": 5031, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2469, "endOffset": 2675, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2772, "endOffset": 3016, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3093, "endOffset": 3285, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3366, "endOffset": 3689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3768, "endOffset": 4063, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4145, "endOffset": 4733, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4842, "endOffset": 5025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5089, "endOffset": 5659, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5174, "endOffset": 5423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5508, "endOffset": 5653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5710, "endOffset": 10125, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5807, "endOffset": 6154, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6255, "endOffset": 7093, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7177, "endOffset": 8020, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8106, "endOffset": 8997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9093, "endOffset": 9656, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9760, "endOffset": 10119, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-file.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 15}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1268, "endOffset": 2593, "count": 15}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2625, "endOffset": 3888, "count": 14}, {"startOffset": 2787, "endOffset": 2847, "count": 1}, {"startOffset": 2847, "endOffset": 2953, "count": 13}, {"startOffset": 2953, "endOffset": 3070, "count": 2}, {"startOffset": 3070, "endOffset": 3151, "count": 11}, {"startOffset": 3151, "endOffset": 3255, "count": 1}, {"startOffset": 3255, "endOffset": 3290, "count": 10}, {"startOffset": 3290, "endOffset": 3310, "count": 5}, {"startOffset": 3312, "endOffset": 3372, "count": 1}, {"startOffset": 3372, "endOffset": 3406, "count": 9}, {"startOffset": 3406, "endOffset": 3426, "count": 4}, {"startOffset": 3428, "endOffset": 3483, "count": 2}, {"startOffset": 3483, "endOffset": 3604, "count": 7}, {"startOffset": 3604, "endOffset": 3866, "count": 1}, {"startOffset": 3866, "endOffset": 3887, "count": 6}], "isBlockCoverage": true}, {"functionName": "getDescription", "ranges": [{"startOffset": 3891, "endOffset": 4231, "count": 2}, {"startOffset": 4015, "endOffset": 4055, "count": 0}], "isBlockCoverage": true}, {"functionName": "execute", "ranges": [{"startOffset": 4234, "endOffset": 5414, "count": 6}, {"startOffset": 4353, "endOffset": 4509, "count": 2}, {"startOffset": 4509, "endOffset": 4711, "count": 4}, {"startOffset": 4711, "endOffset": 4889, "count": 1}, {"startOffset": 4889, "endOffset": 4947, "count": 3}, {"startOffset": 4947, "endOffset": 4985, "count": 2}, {"startOffset": 4986, "endOffset": 4994, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/schemaValidator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 14}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 609, "endOffset": 1607, "count": 14}, {"startOffset": 840, "endOffset": 936, "count": 1}, {"startOffset": 944, "endOffset": 1018, "count": 13}, {"startOffset": 1020, "endOffset": 1585, "count": 13}, {"startOffset": 1154, "endOffset": 1579, "count": 39}, {"startOffset": 1192, "endOffset": 1204, "count": 23}, {"startOffset": 1206, "endOffset": 1571, "count": 23}, {"startOffset": 1307, "endOffset": 1316, "count": 0}, {"startOffset": 1383, "endOffset": 1561, "count": 0}, {"startOffset": 1585, "endOffset": 1606, "count": 13}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 3}, {"startOffset": 455, "endOffset": 463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 3}, {"startOffset": 544, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 3}, {"startOffset": 1626, "endOffset": 3067, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1895, "endOffset": 1910, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 3}, {"startOffset": 3415, "endOffset": 3421, "count": 1}], "isBlockCoverage": true}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "322", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 15}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 28}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "323", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fileUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 1}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 1}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 1}, {"startOffset": 485, "endOffset": 493, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 1}, {"startOffset": 574, "endOffset": 582, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 1}, {"startOffset": 667, "endOffset": 675, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 1}, {"startOffset": 780, "endOffset": 788, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1290, "endOffset": 1470, "count": 3}, {"startOffset": 1459, "endOffset": 1467, "count": 0}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 1472, "endOffset": 2066, "count": 11}, {"startOffset": 1861, "endOffset": 1886, "count": 0}], "isBlockCoverage": true}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 2068, "endOffset": 2935, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 2937, "endOffset": 3773, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 3775, "endOffset": 7818, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "328", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/metrics.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 3}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 3}, {"startOffset": 1020, "endOffset": 1028, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1588, "endOffset": 1749, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 1976, "endOffset": 2072, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2073, "endOffset": 2234, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2236, "endOffset": 4028, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4030, "endOffset": 4502, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4504, "endOffset": 4732, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 4734, "endOffset": 5196, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5198, "endOffset": 5684, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 5686, "endOffset": 6125, "count": 3}, {"startOffset": 5798, "endOffset": 5822, "count": 0}, {"startOffset": 5831, "endOffset": 6124, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "424", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/constants.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "425", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 15}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 15}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 15}, {"startOffset": 1031, "endOffset": 1241, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 7}, {"startOffset": 2334, "endOffset": 2356, "count": 0}], "isBlockCoverage": true}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "426", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 15}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 15}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 15}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 15}, {"startOffset": 1492, "endOffset": 1528, "count": 0}, {"startOffset": 1560, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 15}], "isBlockCoverage": true}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 15}], "isBlockCoverage": true}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 7}, {"startOffset": 1930, "endOffset": 1940, "count": 0}, {"startOffset": 2004, "endOffset": 2031, "count": 0}, {"startOffset": 2133, "endOffset": 2192, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "428", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 15}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 15}, {"startOffset": 735, "endOffset": 1054, "count": 105}, {"startOffset": 858, "endOffset": 888, "count": 0}, {"startOffset": 993, "endOffset": 1017, "count": 15}, {"startOffset": 1017, "endOffset": 1054, "count": 90}, {"startOffset": 1078, "endOffset": 1116, "count": 0}], "isBlockCoverage": true}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}