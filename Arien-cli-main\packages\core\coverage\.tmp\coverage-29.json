{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/coreToolScheduler.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28322, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28322, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 795, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "MockTool", "ranges": [{"startOffset": 865, "endOffset": 943, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 946, "endOffset": 1243, "count": 2}, {"startOffset": 1220, "endOffset": 1242, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConfirm", "ranges": [{"startOffset": 1182, "endOffset": 1205, "count": 1}], "isBlockCoverage": true}, {"functionName": "execute", "ranges": [{"startOffset": 1246, "endOffset": 1391, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1450, "endOffset": 3524, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1563, "endOffset": 3520, "count": 1}], "isBlockCoverage": true}, {"functionName": "getTool", "ranges": [{"startOffset": 1691, "endOffset": 1705, "count": 1}], "isBlockCoverage": true}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 1738, "endOffset": 1746, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 1829, "endOffset": 1844, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolByName", "ranges": [{"startOffset": 1867, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolByDisplayName", "ranges": [{"startOffset": 1911, "endOffset": 1925, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTools", "ranges": [{"startOffset": 1943, "endOffset": 1951, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 1974, "endOffset": 1995, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 2016, "endOffset": 2024, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 2050, "endOffset": 2058, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 2238, "endOffset": 2261, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 2296, "endOffset": 2306, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 2328, "endOffset": 2339, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPreferredEditor", "ranges": [{"startOffset": 2573, "endOffset": 2587, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3591, "endOffset": 9208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3729, "endOffset": 4079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4168, "endOffset": 4534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4645, "endOffset": 5001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5079, "endOffset": 5574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5650, "endOffset": 6157, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6271, "endOffset": 6815, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6919, "endOffset": 7422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7538, "endOffset": 7919, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7994, "endOffset": 8308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8388, "endOffset": 8753, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8865, "endOffset": 9204, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/coreToolScheduler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 53388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 53388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 11}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 984, "endOffset": 1162, "count": 11}], "isBlockCoverage": true}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1163, "endOffset": 2687, "count": 11}, {"startOffset": 1283, "endOffset": 1309, "count": 4}, {"startOffset": 1310, "endOffset": 1325, "count": 2}, {"startOffset": 1326, "endOffset": 1338, "count": 9}, {"startOffset": 1384, "endOffset": 1464, "count": 2}, {"startOffset": 1464, "endOffset": 1505, "count": 9}, {"startOffset": 1505, "endOffset": 1690, "count": 2}, {"startOffset": 1690, "endOffset": 1733, "count": 7}, {"startOffset": 1733, "endOffset": 2073, "count": 0}, {"startOffset": 2073, "endOffset": 2109, "count": 7}, {"startOffset": 2109, "endOffset": 2137, "count": 5}, {"startOffset": 2139, "endOffset": 2457, "count": 3}, {"startOffset": 2189, "endOffset": 2199, "count": 2}, {"startOffset": 2200, "endOffset": 2238, "count": 1}, {"startOffset": 2239, "endOffset": 2251, "count": 0}, {"startOffset": 2457, "endOffset": 2499, "count": 4}, {"startOffset": 2499, "endOffset": 2686, "count": 2}], "isBlockCoverage": true}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 2717, "endOffset": 2955, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2985, "endOffset": 3133, "count": 1}], "isBlockCoverage": true}, {"functionName": "CoreToolScheduler", "ranges": [{"startOffset": 3137, "endOffset": 3575, "count": 1}], "isBlockCoverage": true}, {"functionName": "setStatusInternal", "ranges": [{"startOffset": 3578, "endOffset": 6686, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3678, "endOffset": 6609, "count": 2}, {"startOffset": 3860, "endOffset": 3897, "count": 0}, {"startOffset": 4077, "endOffset": 4412, "count": 0}, {"startOffset": 4421, "endOffset": 4720, "count": 0}, {"startOffset": 4729, "endOffset": 5011, "count": 1}, {"startOffset": 5020, "endOffset": 5238, "count": 0}, {"startOffset": 5247, "endOffset": 6031, "count": 1}, {"startOffset": 5347, "endOffset": 5355, "count": 0}, {"startOffset": 6040, "endOffset": 6260, "count": 0}, {"startOffset": 6269, "endOffset": 6487, "count": 0}, {"startOffset": 6496, "endOffset": 6595, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArgsInternal", "ranges": [{"startOffset": 6689, "endOffset": 6935, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRunning", "ranges": [{"startOffset": 6938, "endOffset": 7077, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6990, "endOffset": 7066, "count": 0}], "isBlockCoverage": false}, {"functionName": "schedule", "ranges": [{"startOffset": 7080, "endOffset": 9640, "count": 1}, {"startOffset": 7140, "endOffset": 7298, "count": 0}, {"startOffset": 7363, "endOffset": 7374, "count": 0}, {"startOffset": 8215, "endOffset": 8242, "count": 0}, {"startOffset": 8396, "endOffset": 8470, "count": 0}, {"startOffset": 9193, "endOffset": 9277, "count": 0}, {"startOffset": 9296, "endOffset": 9541, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7480, "endOffset": 8028, "count": 1}, {"startOffset": 7587, "endOffset": 7869, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConfirm", "ranges": [{"startOffset": 8833, "endOffset": 9009, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmationResponse", "ranges": [{"startOffset": 9643, "endOffset": 11473, "count": 1}, {"startOffset": 10344, "endOffset": 11417, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9770, "endOffset": 9840, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9998, "endOffset": 10127, "count": 1}, {"startOffset": 10052, "endOffset": 10064, "count": 0}], "isBlockCoverage": true}, {"functionName": "attemptExecutionOfScheduledCalls", "ranges": [{"startOffset": 11476, "endOffset": 13654, "count": 2}, {"startOffset": 11750, "endOffset": 13650, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11584, "endOffset": 11708, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11812, "endOffset": 11849, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11888, "endOffset": 13642, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAndNotifyCompletion", "ranges": [{"startOffset": 13657, "endOffset": 14291, "count": 3}, {"startOffset": 13905, "endOffset": 14287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13746, "endOffset": 13839, "count": 3}], "isBlockCoverage": true}, {"functionName": "notifyToolCallsUpdate", "ranges": [{"startOffset": 14294, "endOffset": 14414, "count": 4}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/config/config.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42315, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42315, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 2}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 516, "endOffset": 568, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3643, "endOffset": 3806, "count": 1}], "isBlockCoverage": true}, {"functionName": "MCPServerConfig", "ranges": [{"startOffset": 3855, "endOffset": 4191, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4212, "endOffset": 4817, "count": 0}], "isBlockCoverage": true}, {"functionName": "Config", "ranges": [{"startOffset": 4821, "endOffset": 7270, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshAuth", "ranges": [{"startOffset": 7273, "endOffset": 7805, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 7808, "endOffset": 7855, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGeneratorConfig", "ranges": [{"startOffset": 7858, "endOffset": 7931, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModel", "ranges": [{"startOffset": 7934, "endOffset": 8011, "count": 0}], "isBlockCoverage": false}, {"functionName": "setModel", "ranges": [{"startOffset": 8014, "endOffset": 8181, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModelSwitchedDuringSession", "ranges": [{"startOffset": 8184, "endOffset": 8264, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetModelToDefault", "ranges": [{"startOffset": 8267, "endOffset": 8440, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFlashFallbackHandler", "ranges": [{"startOffset": 8443, "endOffset": 8522, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEmbeddingModel", "ranges": [{"startOffset": 8525, "endOffset": 8582, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandbox", "ranges": [{"startOffset": 8585, "endOffset": 8628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 8631, "endOffset": 8678, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 8681, "endOffset": 8730, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolRegistry", "ranges": [{"startOffset": 8733, "endOffset": 8803, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 8806, "endOffset": 8853, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestion", "ranges": [{"startOffset": 8856, "endOffset": 8901, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFullContext", "ranges": [{"startOffset": 8904, "endOffset": 8955, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreTools", "ranges": [{"startOffset": 8958, "endOffset": 9005, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExcludeTools", "ranges": [{"startOffset": 9008, "endOffset": 9061, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolDiscoveryCommand", "ranges": [{"startOffset": 9064, "endOffset": 9133, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolCallCommand", "ranges": [{"startOffset": 9136, "endOffset": 9195, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServerCommand", "ranges": [{"startOffset": 9198, "endOffset": 9259, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServers", "ranges": [{"startOffset": 9262, "endOffset": 9311, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserMemory", "ranges": [{"startOffset": 9314, "endOffset": 9363, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUserMemory", "ranges": [{"startOffset": 9366, "endOffset": 9437, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFileCount", "ranges": [{"startOffset": 9440, "endOffset": 9501, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFileCount", "ranges": [{"startOffset": 9504, "endOffset": 9571, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApprovalMode", "ranges": [{"startOffset": 9574, "endOffset": 9627, "count": 0}], "isBlockCoverage": false}, {"functionName": "setApprovalMode", "ranges": [{"startOffset": 9630, "endOffset": 9687, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowMemoryUsage", "ranges": [{"startOffset": 9690, "endOffset": 9749, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessibility", "ranges": [{"startOffset": 9752, "endOffset": 9807, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryEnabled", "ranges": [{"startOffset": 9810, "endOffset": 9889, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryLogPromptsEnabled", "ranges": [{"startOffset": 9892, "endOffset": 9983, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryOtlpEndpoint", "ranges": [{"startOffset": 9986, "endOffset": 10114, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryTarget", "ranges": [{"startOffset": 10117, "endOffset": 10236, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 10239, "endOffset": 10290, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienDir", "ranges": [{"startOffset": 10293, "endOffset": 10412, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 10415, "endOffset": 10520, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnableRecursiveFileSearch", "ranges": [{"startOffset": 10523, "endOffset": 10616, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 10619, "endOffset": 10707, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCheckpointingEnabled", "ranges": [{"startOffset": 10710, "endOffset": 10772, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProxy", "ranges": [{"startOffset": 10775, "endOffset": 10814, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWorkingDir", "ranges": [{"startOffset": 10817, "endOffset": 10859, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBugCommand", "ranges": [{"startOffset": 10862, "endOffset": 10911, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileService", "ranges": [{"startOffset": 10914, "endOffset": 11118, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 11121, "endOffset": 11194, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExtensionContextFilePaths", "ranges": [{"startOffset": 11197, "endOffset": 11276, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGitService", "ranges": [{"startOffset": 11279, "endOffset": 11490, "count": 0}], "isBlockCoverage": false}, {"functionName": "createToolRegistry", "ranges": [{"startOffset": 11494, "endOffset": 13066, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/contentGenerator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1150, "endOffset": 1338, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1357, "endOffset": 2722, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 2724, "endOffset": 3591, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "518", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/codeAssist.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 923, "endOffset": 1379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "519", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/oauth2.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 2061, "endOffset": 2667, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 2669, "endOffset": 4557, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 4558, "endOffset": 5065, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 5067, "endOffset": 5536, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 5537, "endOffset": 5878, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 5879, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 6028, "endOffset": 6174, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "532", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 785, "endOffset": 954, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 958, "endOffset": 2044, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2046, "endOffset": 2384, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "533", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 650, "endOffset": 1264, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1333, "endOffset": 1498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1564, "endOffset": 1818, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "534", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1073, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1218, "endOffset": 1612, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1615, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 1905, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2010, "endOffset": 2118, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2121, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2249, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2392, "endOffset": 2614, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2617, "endOffset": 2666, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2669, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3065, "endOffset": 3418, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3421, "endOffset": 4521, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "539", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 863, "endOffset": 1014, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1016, "endOffset": 1105, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1107, "endOffset": 1258, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1260, "endOffset": 1620, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1622, "endOffset": 2051, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2052, "endOffset": 2191, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2192, "endOffset": 2299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2300, "endOffset": 2661, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 2662, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 2718, "endOffset": 2824, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 2825, "endOffset": 3738, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "540", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/config/models.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "541", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/modelCheck.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 550, "endOffset": 1915, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "542", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tool-registry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1054, "endOffset": 2309, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2312, "endOffset": 3987, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4014, "endOffset": 4077, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4081, "endOffset": 4132, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 4255, "endOffset": 4465, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 4607, "endOffset": 5768, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 6027, "endOffset": 6193, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 6279, "endOffset": 6342, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 6427, "endOffset": 6651, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 6710, "endOffset": 6762, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "543", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 3}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 1}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "544", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2369, "endOffset": 2570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2637, "endOffset": 2848, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 3028, "endOffset": 3117, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 3119, "endOffset": 3298, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 3300, "endOffset": 3495, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 3496, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 3630, "endOffset": 3713, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 3715, "endOffset": 3778, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 3780, "endOffset": 5751, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 5753, "endOffset": 14983, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 14984, "endOffset": 15383, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "635", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 629, "endOffset": 1194, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1197, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1245, "endOffset": 2342, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2345, "endOffset": 2669, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 2673, "endOffset": 3597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "636", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/ls.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26405, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26405, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 0}], "isBlockCoverage": false}, {"functionName": "LSTool", "ranges": [{"startOffset": 1196, "endOffset": 2267, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2270, "endOffset": 2300, "count": 1}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2482, "endOffset": 2916, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3081, "endOffset": 3597, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldIgnore", "ranges": [{"startOffset": 3826, "endOffset": 4230, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4404, "endOffset": 4594, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorResult", "ranges": [{"startOffset": 4641, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4984, "endOffset": 8083, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "637", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/schemaValidator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 609, "endOffset": 1607, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "638", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "639", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-file.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1268, "endOffset": 2593, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2625, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 3891, "endOffset": 4231, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4234, "endOffset": 5414, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "640", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fileUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1290, "endOffset": 1470, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 1472, "endOffset": 2066, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 2068, "endOffset": 2935, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 2937, "endOffset": 3773, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 3775, "endOffset": 7818, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "645", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/metrics.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1588, "endOffset": 1749, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 1976, "endOffset": 2072, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2073, "endOffset": 2234, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2236, "endOffset": 4028, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4030, "endOffset": 4502, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4504, "endOffset": 4732, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 4734, "endOffset": 5196, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5198, "endOffset": 5684, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 5686, "endOffset": 6125, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "741", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/constants.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "742", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/grep.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "GrepTool", "ranges": [{"startOffset": 1884, "endOffset": 3145, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3148, "endOffset": 3183, "count": 1}], "isBlockCoverage": true}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 3521, "endOffset": 4437, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4602, "endOffset": 5208, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5397, "endOffset": 7957, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 8224, "endOffset": 8799, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 9213, "endOffset": 10380, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 10537, "endOffset": 11166, "count": 0}], "isBlockCoverage": false}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 11398, "endOffset": 17940, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "762", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "768", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "769", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/glob.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1133, "endOffset": 1734, "count": 0}], "isBlockCoverage": false}, {"functionName": "GlobTool", "ranges": [{"startOffset": 1943, "endOffset": 3299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3302, "endOffset": 3322, "count": 1}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3390, "endOffset": 3924, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3983, "endOffset": 5237, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5299, "endOffset": 5714, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5785, "endOffset": 9073, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "770", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/edit.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50726, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50726, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1678, "endOffset": 1701, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1705, "endOffset": 1738, "count": 0}], "isBlockCoverage": true}, {"functionName": "EditTool", "ranges": [{"startOffset": 1864, "endOffset": 5043, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 5240, "endOffset": 5617, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5774, "endOffset": 6312, "count": 0}], "isBlockCoverage": false}, {"functionName": "_applyReplacement", "ranges": [{"startOffset": 6315, "endOffset": 6667, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateEdit", "ranges": [{"startOffset": 6948, "endOffset": 9730, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 9866, "endOffset": 11465, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 11468, "endOffset": 12384, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 12555, "endOffset": 14938, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureParentDirectoriesExist", "ranges": [{"startOffset": 15005, "endOffset": 15239, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 15242, "endOffset": 16384, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "772", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editCorrector.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 61494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 61494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 1970, "endOffset": 6224, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 6226, "endOffset": 6804, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 7181, "endOffset": 9495, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 9887, "endOffset": 12621, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 13073, "endOffset": 15329, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 15730, "endOffset": 17593, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 17595, "endOffset": 18207, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 18208, "endOffset": 19265, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 19267, "endOffset": 19510, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 19512, "endOffset": 19630, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "773", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/LruCache.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 397, "endOffset": 413, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 417, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 518, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 680, "endOffset": 984, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 987, "endOffset": 1024, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "774", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/diffOptions.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "775", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/shell.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "ShellTool", "ranges": [{"startOffset": 1488, "endOffset": 3425, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3428, "endOffset": 3461, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3465, "endOffset": 3502, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDescription", "ranges": [{"startOffset": 3506, "endOffset": 3792, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommandRoot", "ranges": [{"startOffset": 3795, "endOffset": 3917, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3920, "endOffset": 4812, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 4815, "endOffset": 5437, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5440, "endOffset": 11670, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "778", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/write-file.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteFileTool", "ranges": [{"startOffset": 1960, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2750, "endOffset": 2776, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2780, "endOffset": 2786, "count": 0}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2790, "endOffset": 3240, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3243, "endOffset": 4257, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4260, "endOffset": 4627, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 4703, "endOffset": 6237, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6240, "endOffset": 9462, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getCorrected<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 9465, "endOffset": 10932, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 10935, "endOffset": 11782, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "779", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/web-fetch.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33809, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33809, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1348, "endOffset": 1458, "count": 0}], "isBlockCoverage": false}, {"functionName": "WebFetchTool", "ranges": [{"startOffset": 1548, "endOffset": 2385, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2388, "endOffset": 2413, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>back", "ranges": [{"startOffset": 2417, "endOffset": 4461, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 4464, "endOffset": 5059, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5062, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 5283, "endOffset": 6201, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6204, "endOffset": 10135, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "780", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/generateContentResponseUtilities.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1510, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1512, "endOffset": 1785, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1787, "endOffset": 2098, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2100, "endOffset": 2359, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2361, "endOffset": 2555, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2557, "endOffset": 2763, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2765, "endOffset": 3146, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3148, "endOffset": 3547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "781", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fetch.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "FetchError", "ranges": [{"startOffset": 969, "endOffset": 1073, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1077, "endOffset": 1287, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1289, "endOffset": 1865, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "814", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-many-files.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 2422, "endOffset": 6250, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 6253, "endOffset": 6284, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6288, "endOffset": 6312, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateParams", "ranges": [{"startOffset": 6316, "endOffset": 7654, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 7657, "endOffset": 9012, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 9015, "endOffset": 15921, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "815", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 1}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3261, "endOffset": 3562, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 3564, "endOffset": 3727, "count": 1}, {"startOffset": 3648, "endOffset": 3691, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 3729, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 3890, "endOffset": 4049, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4050, "endOffset": 4347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 4435, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 4478, "endOffset": 4626, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 4636, "endOffset": 6391, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6394, "endOffset": 7669, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "816", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/web-search.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "WebSearchTool", "ranges": [{"startOffset": 989, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1522, "endOffset": 1555, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateParams", "ranges": [{"startOffset": 1559, "endOffset": 1944, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 1947, "endOffset": 2030, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2033, "endOffset": 4943, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "817", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 2061, "endOffset": 2167, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienClient", "ranges": [{"startOffset": 2190, "endOffset": 2479, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2482, "endOffset": 2617, "count": 0}], "isBlockCoverage": true}, {"functionName": "initialize", "ranges": [{"startOffset": 2621, "endOffset": 2828, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGenerator", "ranges": [{"startOffset": 2831, "endOffset": 2993, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 2996, "endOffset": 3067, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChat", "ranges": [{"startOffset": 3070, "endOffset": 3183, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 3186, "endOffset": 3250, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 3253, "endOffset": 3324, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetChat", "ranges": [{"startOffset": 3327, "endOffset": 3411, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnvironment", "ranges": [{"startOffset": 3414, "endOffset": 5342, "count": 0}], "isBlockCoverage": false}, {"functionName": "startChat", "ranges": [{"startOffset": 5345, "endOffset": 6815, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 6818, "endOffset": 7772, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJson", "ranges": [{"startOffset": 7775, "endOffset": 10180, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 10183, "endOffset": 11580, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateEmbedding", "ranges": [{"startOffset": 11583, "endOffset": 12598, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryCompressChat", "ranges": [{"startOffset": 12601, "endOffset": 14548, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 14730, "endOffset": 15531, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "818", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/getFolderStructure.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 822, "endOffset": 4467, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 4468, "endOffset": 5983, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 5984, "endOffset": 7871, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "819", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/turn.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 932, "endOffset": 1463, "count": 1}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1503, "endOffset": 1610, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1613, "endOffset": 1675, "count": 0}], "isBlockCoverage": true}, {"functionName": "run", "ranges": [{"startOffset": 1747, "endOffset": 4503, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 4506, "endOffset": 4982, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 4985, "endOffset": 5042, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 5045, "endOffset": 5104, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "820", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errorReporting.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 695, "endOffset": 3299, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "821", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/prompts.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 71023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 71023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 1906, "endOffset": 29283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "822", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/nextSpeakerChecker.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 2973, "endOffset": 4748, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "823", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/messageInspectors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1549, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1549, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 398, "endOffset": 549, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "824", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/arienChat.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 1507, "endOffset": 1780, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 1781, "endOffset": 2145, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 2146, "endOffset": 2417, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 2418, "endOffset": 3270, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienChat", "ranges": [{"startOffset": 3291, "endOffset": 3547, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3641, "endOffset": 3672, "count": 0}], "isBlockCoverage": true}, {"functionName": "_getRequestTextFromContents", "ranges": [{"startOffset": 3676, "endOffset": 3832, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiRequest", "ranges": [{"startOffset": 3835, "endOffset": 4067, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiResponse", "ranges": [{"startOffset": 4070, "endOffset": 4364, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiError", "ranges": [{"startOffset": 4367, "endOffset": 4772, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 4954, "endOffset": 5727, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessage", "ranges": [{"startOffset": 6337, "endOffset": 8622, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 9275, "endOffset": 10746, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 11734, "endOffset": 11886, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHistory", "ranges": [{"startOffset": 11931, "endOffset": 11974, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 12094, "endOffset": 12151, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 12154, "endOffset": 12207, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFinalUsageMetadata", "ranges": [{"startOffset": 12210, "endOffset": 12391, "count": 0}], "isBlockCoverage": false}, {"functionName": "processStreamResponse", "ranges": [{"startOffset": 12394, "endOffset": 13666, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordHistory", "ranges": [{"startOffset": 13669, "endOffset": 15900, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTextContent", "ranges": [{"startOffset": 15903, "endOffset": 16105, "count": 0}], "isBlockCoverage": false}, {"functionName": "isT<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 16108, "endOffset": 16322, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "825", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/retry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 662, "endOffset": 1047, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1048, "endOffset": 1130, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1131, "endOffset": 3083, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 3085, "endOffset": 3547, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 3548, "endOffset": 4434, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 4435, "endOffset": 4680, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 4681, "endOffset": 5599, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "826", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/loggers.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24150, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24150, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 1861, "endOffset": 1911, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 1913, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2010, "endOffset": 3125, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3127, "endOffset": 3855, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 3857, "endOffset": 4957, "count": 1}, {"startOffset": 4049, "endOffset": 4685, "count": 0}, {"startOffset": 4686, "endOffset": 4690, "count": 0}], "isBlockCoverage": true}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 4959, "endOffset": 5567, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 5569, "endOffset": 6704, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 6706, "endOffset": 8522, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "861", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/sdk.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15054, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15054, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 2898, "endOffset": 2969, "count": 1}], "isBlockCoverage": true}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 2971, "endOffset": 3368, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 3369, "endOffset": 5539, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 5541, "endOffset": 5926, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1355", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/clearcut-logger/clearcut-logger.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1302, "endOffset": 1317, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1321, "endOffset": 1504, "count": 1}], "isBlockCoverage": true}, {"functionName": "ClearcutLogger", "ranges": [{"startOffset": 1560, "endOffset": 1611, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInstance", "ranges": [{"startOffset": 1621, "endOffset": 1873, "count": 1}, {"startOffset": 1716, "endOffset": 1730, "count": 0}], "isBlockCoverage": true}, {"functionName": "enqueueLogEvent", "ranges": [{"startOffset": 1974, "endOffset": 2137, "count": 1}], "isBlockCoverage": true}, {"functionName": "createLogEvent", "ranges": [{"startOffset": 2140, "endOffset": 2376, "count": 1}], "isBlockCoverage": true}, {"functionName": "flushIfNeeded", "ranges": [{"startOffset": 2379, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushToClearcut", "ranges": [{"startOffset": 2522, "endOffset": 4018, "count": 1}, {"startOffset": 2579, "endOffset": 2641, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2737, "endOffset": 3672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3253, "endOffset": 3424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3449, "endOffset": 3643, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3679, "endOffset": 3911, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3919, "endOffset": 4012, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeLogResponse", "ranges": [{"startOffset": 4103, "endOffset": 4608, "count": 0}], "isBlockCoverage": false}, {"functionName": "logStartSessionEvent", "ranges": [{"startOffset": 4611, "endOffset": 6921, "count": 0}], "isBlockCoverage": false}, {"functionName": "logNewPromptEvent", "ranges": [{"startOffset": 6924, "endOffset": 7243, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCallEvent", "ranges": [{"startOffset": 7246, "endOffset": 8339, "count": 1}], "isBlockCoverage": true}, {"functionName": "logApiRequestEvent", "ranges": [{"startOffset": 8342, "endOffset": 8654, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponseEvent", "ranges": [{"startOffset": 8657, "endOffset": 10347, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiErrorEvent", "ranges": [{"startOffset": 10350, "endOffset": 11131, "count": 0}], "isBlockCoverage": false}, {"functionName": "logEndSessionEvent", "ranges": [{"startOffset": 11134, "endOffset": 11451, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdown", "ranges": [{"startOffset": 11454, "endOffset": 11580, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1356", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1457, "endOffset": 1637, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1664, "endOffset": 2278, "count": 1}, {"startOffset": 2056, "endOffset": 2160, "count": 0}, {"startOffset": 2165, "endOffset": 2223, "count": 0}, {"startOffset": 2228, "endOffset": 2272, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2308, "endOffset": 2609, "count": 0}], "isBlockCoverage": true}, {"functionName": "StartSessionEvent", "ranges": [{"startOffset": 2613, "endOffset": 3837, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3867, "endOffset": 3928, "count": 0}], "isBlockCoverage": true}, {"functionName": "EndSessionEvent", "ranges": [{"startOffset": 3932, "endOffset": 4117, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4147, "endOffset": 4221, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserPromptEvent", "ranges": [{"startOffset": 4225, "endOffset": 4445, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4473, "endOffset": 4615, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToolCallEvent", "ranges": [{"startOffset": 4619, "endOffset": 5111, "count": 1}, {"startOffset": 4878, "endOffset": 4882, "count": 0}, {"startOffset": 5002, "endOffset": 5010, "count": 0}, {"startOffset": 5048, "endOffset": 5057, "count": 0}, {"startOffset": 5100, "endOffset": 5106, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5141, "endOffset": 5213, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiRequestEvent", "ranges": [{"startOffset": 5217, "endOffset": 5431, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5459, "endOffset": 5568, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiErrorEvent", "ranges": [{"startOffset": 5572, "endOffset": 5907, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5938, "endOffset": 6167, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiResponseEvent", "ranges": [{"startOffset": 6171, "endOffset": 6861, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1357", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/clearcut-logger/event-metadata-key.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14459, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14459, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 6}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 4819, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 4846, "endOffset": 4999, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1358", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/user_id.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5185, "count": 1}, {"startOffset": 858, "endOffset": 863, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "ensureArienDirExists", "ranges": [{"startOffset": 1024, "endOffset": 1184, "count": 1}, {"startOffset": 1109, "endOffset": 1182, "count": 0}], "isBlockCoverage": true}, {"functionName": "readUserIdFromFile", "ranges": [{"startOffset": 1185, "endOffset": 1402, "count": 1}, {"startOffset": 1372, "endOffset": 1379, "count": 0}, {"startOffset": 1384, "endOffset": 1401, "count": 0}], "isBlockCoverage": true}, {"functionName": "writeUserIdToFile", "ranges": [{"startOffset": 1403, "endOffset": 1509, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPersistentUserId", "ranges": [{"startOffset": 1510, "endOffset": 1913, "count": 1}, {"startOffset": 1635, "endOffset": 1730, "count": 0}, {"startOffset": 1755, "endOffset": 1911, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1359", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/tokenLimits.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 522, "endOffset": 1005, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1497", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 0}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1498", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1500", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/gitService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1209, "endOffset": 1220, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1224, "endOffset": 1321, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1324, "endOffset": 1548, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 1551, "endOffset": 1913, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 1916, "endOffset": 2167, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2298, "endOffset": 3610, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 3613, "endOffset": 3978, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 3981, "endOffset": 4113, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 4116, "endOffset": 4308, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 4311, "endOffset": 4501, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1507", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2565, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2675, "endOffset": 2740, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2789, "endOffset": 2863, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3886, "endOffset": 4012, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1508", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/logger.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 922, "endOffset": 1017, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1062, "endOffset": 1227, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1303, "endOffset": 1363, "count": 0}], "isBlockCoverage": false}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1366, "endOffset": 2690, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2693, "endOffset": 3041, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3044, "endOffset": 4220, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4223, "endOffset": 5900, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 5903, "endOffset": 6240, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 6243, "endOffset": 6903, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 6906, "endOffset": 7198, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 7201, "endOffset": 7703, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 7706, "endOffset": 8588, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 8591, "endOffset": 8776, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1509", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/arienRequest.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1365, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1510", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/nonInteractiveToolExecutor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 643, "endOffset": 3345, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1511", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/memoryDiscovery.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1084, "endOffset": 1148, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1223, "endOffset": 1285, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1361, "endOffset": 1425, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1477, "endOffset": 2618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2619, "endOffset": 6572, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6573, "endOffset": 7466, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7467, "endOffset": 8106, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8107, "endOffset": 9283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1512", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/bfsFileSearch.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 618, "endOffset": 680, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 684, "endOffset": 1893, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1513", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 899, "endOffset": 1031, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1032, "endOffset": 1274, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1596, "endOffset": 1810, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1812, "endOffset": 2031, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2033, "endOffset": 2212, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2214, "endOffset": 3825, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 3827, "endOffset": 5224, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1514", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/session.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1515", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/modifiable-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1206, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1280, "endOffset": 2318, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2319, "endOffset": 3314, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3315, "endOffset": 3648, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 3649, "endOffset": 4328, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}