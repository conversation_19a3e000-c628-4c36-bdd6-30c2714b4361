{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/extension.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11165, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11165, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 428, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1060, "endOffset": 3240, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1124, "endOffset": 1498, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1513, "endOffset": 1679, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1745, "endOffset": 2509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2241, "endOffset": 2272, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2308, "endOffset": 2339, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2576, "endOffset": 3236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3069, "endOffset": 3100, "count": 1}], "isBlockCoverage": true}, {"functionName": "createExtension", "ranges": [{"startOffset": 3243, "endOffset": 3849, "count": 3}, {"startOffset": 3631, "endOffset": 3724, "count": 1}, {"startOffset": 3749, "endOffset": 3847, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1028", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/config/extension.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 2}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 378, "endOffset": 438, "count": 3}, {"startOffset": 428, "endOffset": 436, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 483, "endOffset": 531, "count": 2}, {"startOffset": 521, "endOffset": 529, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadExtensions", "ranges": [{"startOffset": 1049, "endOffset": 1640, "count": 2}, {"startOffset": 1345, "endOffset": 1610, "count": 3}], "isBlockCoverage": true}, {"functionName": "loadExtensionsFromDir", "ranges": [{"startOffset": 1642, "endOffset": 2165, "count": 4}, {"startOffset": 1820, "endOffset": 1940, "count": 2}, {"startOffset": 1940, "endOffset": 2141, "count": 3}, {"startOffset": 2141, "endOffset": 2164, "count": 2}], "isBlockCoverage": true}, {"functionName": "loadExtension", "ranges": [{"startOffset": 2166, "endOffset": 3432, "count": 3}, {"startOffset": 2272, "endOffset": 2394, "count": 0}, {"startOffset": 2548, "endOffset": 2699, "count": 0}, {"startOffset": 2883, "endOffset": 3021, "count": 0}, {"startOffset": 3297, "endOffset": 3430, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3080, "endOffset": 3158, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3167, "endOffset": 3237, "count": 3}], "isBlockCoverage": true}, {"functionName": "getContextFileNames", "ranges": [{"startOffset": 3433, "endOffset": 3661, "count": 3}, {"startOffset": 3503, "endOffset": 3533, "count": 2}, {"startOffset": 3533, "endOffset": 3625, "count": 1}, {"startOffset": 3625, "endOffset": 3660, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}