{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessage.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 481, "endOffset": 832, "count": 1}], "isBlockCoverage": true}, {"functionName": "MarkdownDisplay", "ranges": [{"startOffset": 509, "endOffset": 829, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 894, "endOffset": 1316, "count": 1}], "isBlockCoverage": true}, {"functionName": "AnimatedIcon", "ranges": [{"startOffset": 919, "endOffset": 1313, "count": 6}, {"startOffset": 1093, "endOffset": 1098, "count": 1}, {"startOffset": 1099, "endOffset": 1105, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1368, "endOffset": 1423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2014, "endOffset": 6226, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2188, "endOffset": 2617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2701, "endOffset": 3370, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3452, "endOffset": 4145, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4231, "endOffset": 4925, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5031, "endOffset": 5577, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5659, "endOffset": 6222, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1329", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 6}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienMessage", "ranges": [{"startOffset": 1165, "endOffset": 3123, "count": 6}], "isBlockCoverage": true}], "startOffset": 209}]}