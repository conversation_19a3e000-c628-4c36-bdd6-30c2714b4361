-------------------|---------|----------|---------|---------|-------------------
File               | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-------------------|---------|----------|---------|---------|-------------------
All files          |       0 |        0 |       0 |       0 |                   
 src               |       0 |        0 |       0 |       0 |                   
  arien.tsx        |       0 |        0 |       0 |       0 | 1-296             
  ...ractiveCli.ts |       0 |        0 |       0 |       0 | 1-156             
 src/config        |       0 |        0 |       0 |       0 |                   
  auth.ts          |       0 |        0 |       0 |       0 | 1-39              
  ...cp-servers.ts |       0 |        0 |       0 |       0 | 1-75              
  config.ts        |       0 |        0 |       0 |       0 | 1-422             
  ...-installer.ts |       0 |        0 |       0 |       0 | 1-209             
  ...cy-manager.ts |       0 |        0 |       0 |       0 | 1-303             
  extension.ts     |       0 |        0 |       0 |       0 | 1-115             
  ...ver-health.ts |       0 |        0 |       0 |       0 | 1-305             
  ...r-registry.ts |       0 |        0 |       0 |       0 | 1-561             
  ...-validator.ts |       0 |        0 |       0 |       0 | 1-279             
  sandboxConfig.ts |       0 |        0 |       0 |       0 | 1-107             
  settings.ts      |       0 |        0 |       0 |       0 | 1-265             
 src/generated     |       0 |        0 |       0 |       0 |                   
  git-commit.ts    |       0 |        0 |       0 |       0 | 1-9               
 src/ui            |       0 |        0 |       0 |       0 |                   
  App.tsx          |       0 |        0 |       0 |       0 | 1-918             
  colors.ts        |       0 |        0 |       0 |       0 | 1-50              
  constants.ts     |       0 |        0 |       0 |       0 | 1-15              
  types.ts         |       0 |        0 |       0 |       0 | 1-157             
 src/ui/components |       0 |        0 |       0 |       0 |                   
  AboutBox.tsx     |       0 |        0 |       0 |       0 | 1-122             
  ...ngSpinner.tsx |       0 |        0 |       0 |       0 | 1-37              
  AsciiArt.ts      |       0 |        0 |       0 |       0 | 1-36              
  AuthDialog.tsx   |       0 |        0 |       0 |       0 | 1-107             
  ...nProgress.tsx |       0 |        0 |       0 |       0 | 1-57              
  ...Indicator.tsx |       0 |        0 |       0 |       0 | 1-50              
  ...lePatcher.tsx |       0 |        0 |       0 |       0 | 1-60              
  ...ryDisplay.tsx |       0 |        0 |       0 |       0 | 1-35              
  ...ryDisplay.tsx |       0 |        0 |       0 |       0 | 1-70              
  ...esDisplay.tsx |       0 |        0 |       0 |       0 | 1-82              
  ...ngsDialog.tsx |       0 |        0 |       0 |       0 | 1-168             
  Footer.tsx       |       0 |        0 |       0 |       0 | 1-128             
  Header.tsx       |       0 |        0 |       0 |       0 | 1-59              
  Help.tsx         |       0 |        0 |       0 |       0 | 1-178             
  ...emDisplay.tsx |       0 |        0 |       0 |       0 | 1-95              
  InputPrompt.tsx  |       0 |        0 |       0 |       0 | 1-473             
  ...Indicator.tsx |       0 |        0 |       0 |       0 | 1-97              
  ...geDisplay.tsx |       0 |        0 |       0 |       0 | 1-50              
  ...ryDisplay.tsx |       0 |        0 |       0 |       0 | 1-88              
  ...Indicator.tsx |       0 |        0 |       0 |       0 | 1-17              
  ...MoreLines.tsx |       0 |        0 |       0 |       0 | 1-45              
  Stats.tsx        |       0 |        0 |       0 |       0 | 1-117             
  StatsDisplay.tsx |       0 |        0 |       0 |       0 | 1-97              
  ...nsDisplay.tsx |       0 |        0 |       0 |       0 | 1-108             
  ThemeDialog.tsx  |       0 |        0 |       0 |       0 | 1-258             
  Tips.tsx         |       0 |        0 |       0 |       0 | 1-54              
  ...ification.tsx |       0 |        0 |       0 |       0 | 1-26              
 ...nents/messages |       0 |        0 |       0 |       0 |                   
  ArienMessage.tsx |       0 |        0 |       0 |       0 | 1-45              
  ...geContent.tsx |       0 |        0 |       0 |       0 | 1-45              
  ...onMessage.tsx |       0 |        0 |       0 |       0 | 1-50              
  DiffRenderer.tsx |       0 |        0 |       0 |       0 | 1-312             
  ErrorMessage.tsx |       0 |        0 |       0 |       0 | 1-49              
  InfoMessage.tsx  |       0 |        0 |       0 |       0 | 1-40              
  ...onMessage.tsx |       0 |        0 |       0 |       0 | 1-250             
  ...upMessage.tsx |       0 |        0 |       0 |       0 | 1-123             
  ToolMessage.tsx  |       0 |        0 |       0 |       0 | 1-277             
  UserMessage.tsx  |       0 |        0 |       0 |       0 | 1-31              
  ...llMessage.tsx |       0 |        0 |       0 |       0 | 1-25              
 ...ponents/shared |       0 |        0 |       0 |       0 |                   
  AnimatedIcon.tsx |       0 |        0 |       0 |       0 | 1-41              
  MaxSizedBox.tsx  |       0 |        0 |       0 |       0 | 1-544             
  ProgressBar.tsx  |       0 |        0 |       0 |       0 | 1-78              
  ...Indicator.tsx |       0 |        0 |       0 |       0 | 1-79              
  ...tonSelect.tsx |       0 |        0 |       0 |       0 | 1-142             
  Separator.tsx    |       0 |        0 |       0 |       0 | 1-92              
  StatusBadge.tsx  |       0 |        0 |       0 |       0 | 1-85              
  index.ts         |       0 |        0 |       0 |       0 | 1-13              
  text-buffer.ts   |       0 |        0 |       0 |       0 | 1-1415            
 src/ui/contexts   |       0 |        0 |       0 |       0 |                   
  ...owContext.tsx |       0 |        0 |       0 |       0 | 1-87              
  ...onContext.tsx |       0 |        0 |       0 |       0 | 1-204             
  ...ngContext.tsx |       0 |        0 |       0 |       0 | 1-22              
 src/ui/editors    |       0 |        0 |       0 |       0 |                   
  ...ngsManager.ts |       0 |        0 |       0 |       0 | 1-69              
 src/ui/hooks      |       0 |        0 |       0 |       0 |                   
  ...dProcessor.ts |       0 |        0 |       0 |       0 | 1-425             
  ...dProcessor.ts |       0 |        0 |       0 |       0 | 1-348             
  ...dProcessor.ts |       0 |        0 |       0 |       0 | 1-1110            
  ...rienStream.ts |       0 |        0 |       0 |       0 | 1-868             
  ...uthCommand.ts |       0 |        0 |       0 |       0 | 1-87              
  ...tIndicator.ts |       0 |        0 |       0 |       0 | 1-49              
  ...ketedPaste.ts |       0 |        0 |       0 |       0 | 1-37              
  useCompletion.ts |       0 |        0 |       0 |       0 | 1-449             
  ...leMessages.ts |       0 |        0 |       0 |       0 | 1-89              
  ...orSettings.ts |       0 |        0 |       0 |       0 | 1-75              
  ...BranchName.ts |       0 |        0 |       0 |       0 | 1-79              
  ...oryManager.ts |       0 |        0 |       0 |       0 | 1-178             
  ...putHistory.ts |       0 |        0 |       0 |       0 | 1-111             
  useKeypress.ts   |       0 |        0 |       0 |       0 | 1-104             
  ...gIndicator.ts |       0 |        0 |       0 |       0 | 1-57              
  useLogger.ts     |       0 |        0 |       0 |       0 | 1-32              
  ...raseCycler.ts |       0 |        0 |       0 |       0 | 1-200             
  ...cySettings.ts |       0 |        0 |       0 |       0 | 1-135             
  ...lScheduler.ts |       0 |        0 |       0 |       0 | 1-312             
  ...oryCommand.ts |       0 |        0 |       0 |       0 | 1-7               
  ...ellHistory.ts |       0 |        0 |       0 |       0 | 1-103             
  ...oryCommand.ts |       0 |        0 |       0 |       0 | 1-75              
  ...tateAndRef.ts |       0 |        0 |       0 |       0 | 1-36              
  ...rminalSize.ts |       0 |        0 |       0 |       0 | 1-48              
  ...emeCommand.ts |       0 |        0 |       0 |       0 | 1-116             
  useTimer.ts      |       0 |        0 |       0 |       0 | 1-65              
 src/ui/privacy    |       0 |        0 |       0 |       0 |                   
  ...acyNotice.tsx |       0 |        0 |       0 |       0 | 1-58              
  ...acyNotice.tsx |       0 |        0 |       0 |       0 | 1-113             
  ...acyNotice.tsx |       0 |        0 |       0 |       0 | 1-55              
  ...acyNotice.tsx |       0 |        0 |       0 |       0 | 1-41              
 src/ui/themes     |       0 |        0 |       0 |       0 |                   
  ansi-light.ts    |       0 |        0 |       0 |       0 | 1-146             
  ansi.ts          |       0 |        0 |       0 |       0 | 1-155             
  atom-one-dark.ts |       0 |        0 |       0 |       0 | 1-143             
  ayu-light.ts     |       0 |        0 |       0 |       0 | 1-135             
  ayu.ts           |       0 |        0 |       0 |       0 | 1-109             
  default-light.ts |       0 |        0 |       0 |       0 | 1-106             
  default.ts       |       0 |        0 |       0 |       0 | 1-149             
  dracula.ts       |       0 |        0 |       0 |       0 | 1-120             
  github-dark.ts   |       0 |        0 |       0 |       0 | 1-143             
  github-light.ts  |       0 |        0 |       0 |       0 | 1-145             
  googlecode.ts    |       0 |        0 |       0 |       0 | 1-142             
  no-color.ts      |       0 |        0 |       0 |       0 | 1-91              
  ...-of-purple.ts |       0 |        0 |       0 |       0 | 1-348             
  theme-manager.ts |       0 |        0 |       0 |       0 | 1-125             
  theme.ts         |       0 |        0 |       0 |       0 | 1-341             
  xcode.ts         |       0 |        0 |       0 |       0 | 1-150             
 src/ui/utils      |       0 |        0 |       0 |       0 |                   
  ...Colorizer.tsx |       0 |        0 |       0 |       0 | 1-184             
  ...wnDisplay.tsx |       0 |        0 |       0 |       0 | 1-490             
  commandUtils.ts  |       0 |        0 |       0 |       0 | 1-26              
  errorParsing.ts  |       0 |        0 |       0 |       0 | 1-106             
  formatters.ts    |       0 |        0 |       0 |       0 | 1-63              
  ...nUtilities.ts |       0 |        0 |       0 |       0 | 1-125             
  ...oordinator.ts |       0 |        0 |       0 |       0 | 1-167             
  textUtils.ts     |       0 |        0 |       0 |       0 | 1-69              
  updateCheck.ts   |       0 |        0 |       0 |       0 | 1-36              
 src/utils         |       0 |        0 |       0 |       0 |                   
  cleanup.ts       |       0 |        0 |       0 |       0 | 1-19              
  package.ts       |       0 |        0 |       0 |       0 | 1-36              
  readStdin.ts     |       0 |        0 |       0 |       0 | 1-39              
  sandbox.ts       |       0 |        0 |       0 |       0 | 1-871             
  ...upWarnings.ts |       0 |        0 |       0 |       0 | 1-40              
  version.ts       |       0 |        0 |       0 |       0 | 1-12              
-------------------|---------|----------|---------|---------|-------------------
