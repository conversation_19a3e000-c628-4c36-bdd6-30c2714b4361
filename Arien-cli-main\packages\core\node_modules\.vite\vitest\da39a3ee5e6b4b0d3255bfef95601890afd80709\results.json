{"version": "3.2.4", "results": [[":src/utils/editCorrector.test.ts", {"duration": 81.60380000000032, "failed": false}], [":src/tools/edit.test.ts", {"duration": 275.09990000000016, "failed": false}], [":src/tools/write-file.test.ts", {"duration": 219.53489999999965, "failed": false}], [":src/tools/mcp-client.test.ts", {"duration": 858.0664000000002, "failed": true}], [":src/utils/memoryDiscovery.test.ts", {"duration": 90.5331000000001, "failed": true}], [":src/telemetry/loggers.test.ts", {"duration": 300.60779999999977, "failed": false}], [":src/core/logger.test.ts", {"duration": 717.2359999999999, "failed": false}], [":src/utils/fileUtils.test.ts", {"duration": 333.72850000000017, "failed": false}], [":src/tools/glob.test.ts", {"duration": 2460.4163, "failed": true}], [":src/core/client.test.ts", {"duration": 184.26029999999992, "failed": false}], [":src/tools/read-many-files.test.ts", {"duration": 534.5136000000002, "failed": false}], [":src/utils/retry.test.ts", {"duration": 134.74139999999989, "failed": false}], [":src/core/turn.test.ts", {"duration": 54.55600000000004, "failed": false}], [":src/utils/editor.test.ts", {"duration": 109.48940000000016, "failed": false}], [":src/utils/generateContentResponseUtilities.test.ts", {"duration": 31.739799999999832, "failed": false}], [":src/utils/getFolderStructure.test.ts", {"duration": 225.61009999999987, "failed": true}], [":src/tools/tool-registry.test.ts", {"duration": 225.00780000000032, "failed": false}], [":src/tools/modifiable-tool.test.ts", {"duration": 97.21640000000002, "failed": true}], [":src/tools/memoryTool.test.ts", {"duration": 60.56819999999993, "failed": false}], [":src/tools/mcp-tool.test.ts", {"duration": 68.50810000000001, "failed": false}], [":src/tools/grep.test.ts", {"duration": 1665.1841999999997, "failed": true}], [":src/services/gitService.test.ts", {"duration": 75.3587, "failed": true}], [":src/config/config.test.ts", {"duration": 287.6606999999999, "failed": false}], [":src/tools/read-file.test.ts", {"duration": 147.66049999999996, "failed": true}], [":src/core/coreToolScheduler.test.ts", {"duration": 173.84400000000005, "failed": false}], [":src/utils/nextSpeakerChecker.test.ts", {"duration": 86.20499999999993, "failed": false}], [":src/core/nonInteractiveToolExecutor.test.ts", {"duration": 235.78929999999946, "failed": false}], [":src/utils/errorReporting.test.ts", {"duration": 64.00679999999988, "failed": true}], [":src/telemetry/metrics.test.ts", {"duration": 115.93050000000005, "failed": false}], [":src/code_assist/converter.test.ts", {"duration": 19.940900000000056, "failed": false}], [":src/utils/gitIgnoreParser.test.ts", {"duration": 111.30290000000014, "failed": true}], [":src/config/flashFallback.test.ts", {"duration": 91.12260000000015, "failed": false}], [":src/utils/bfsFileSearch.test.ts", {"duration": 67.86739999999986, "failed": true}], [":src/code_assist/server.test.ts", {"duration": 67.15350000000012, "failed": false}], [":src/utils/flashFallback.integration.test.ts", {"duration": 295.8944999999999, "failed": false}], [":src/core/prompts.test.ts", {"duration": 92.52929999999992, "failed": true}], [":src/services/fileDiscoveryService.test.ts", {"duration": 56.668100000000095, "failed": true}], [":src/code_assist/oauth2.test.ts", {"duration": 36.73289999999997, "failed": false}], [":src/tools/web-fetch.test.ts", {"duration": 9.51130000000012, "failed": false}], [":src/core/arienRequest.test.ts", {"duration": 16.220199999999977, "failed": false}], [":src/telemetry/telemetry.test.ts", {"duration": 24.772899999999936, "failed": false}], [":src/core/contentGenerator.test.ts", {"duration": 16.504100000000108, "failed": false}], [":src/index.test.ts", {"duration": 8.7971, "failed": false}], [":src/core/arienChat.test.ts", {"duration": 0, "failed": true}]]}