{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 781, "endOffset": 7881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 849, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 938, "endOffset": 1634, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1716, "endOffset": 2392, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2460, "endOffset": 2796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2864, "endOffset": 3285, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3358, "endOffset": 3819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3890, "endOffset": 4300, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4382, "endOffset": 5372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5444, "endOffset": 7877, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5521, "endOffset": 6225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6313, "endOffset": 7051, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7139, "endOffset": 7871, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 7}, {"startOffset": 521, "endOffset": 529, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 3}, {"startOffset": 640, "endOffset": 648, "count": 0}], "isBlockCoverage": true}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 863, "endOffset": 1014, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1016, "endOffset": 1105, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1107, "endOffset": 1258, "count": 7}], "isBlockCoverage": true}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1260, "endOffset": 1620, "count": 3}], "isBlockCoverage": true}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1622, "endOffset": 2051, "count": 7}, {"startOffset": 1768, "endOffset": 1787, "count": 3}, {"startOffset": 1819, "endOffset": 1834, "count": 3}, {"startOffset": 1857, "endOffset": 1864, "count": 3}, {"startOffset": 1892, "endOffset": 1904, "count": 3}, {"startOffset": 1928, "endOffset": 1936, "count": 3}, {"startOffset": 1968, "endOffset": 1984, "count": 3}], "isBlockCoverage": true}, {"functionName": "toContents", "ranges": [{"startOffset": 2052, "endOffset": 2191, "count": 7}, {"startOffset": 2115, "endOffset": 2156, "count": 3}, {"startOffset": 2156, "endOffset": 2190, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2192, "endOffset": 2299, "count": 7}, {"startOffset": 2243, "endOffset": 2267, "count": 6}, {"startOffset": 2267, "endOffset": 2298, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2300, "endOffset": 2661, "count": 9}, {"startOffset": 2360, "endOffset": 2435, "count": 0}, {"startOffset": 2472, "endOffset": 2550, "count": 5}, {"startOffset": 2550, "endOffset": 2578, "count": 4}, {"startOffset": 2578, "endOffset": 2660, "count": 2}], "isBlockCoverage": true}, {"functionName": "toParts", "ranges": [{"startOffset": 2662, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 2718, "endOffset": 2824, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 2825, "endOffset": 3738, "count": 7}, {"startOffset": 2884, "endOffset": 2908, "count": 4}, {"startOffset": 2908, "endOffset": 3737, "count": 3}], "isBlockCoverage": true}], "startOffset": 209}]}