{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/InputPrompt.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22082, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22082, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1411, "endOffset": 8256, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1823, "endOffset": 4367, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1996, "endOffset": 2239, "count": 5}], "isBlockCoverage": true}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 4097, "endOffset": 4118, "count": 5}], "isBlockCoverage": true}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 4142, "endOffset": 4167, "count": 5}], "isBlockCoverage": true}, {"functionName": "wait", "ranges": [{"startOffset": 4385, "endOffset": 4447, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4410, "endOffset": 4446, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4527, "endOffset": 5067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5146, "endOffset": 5682, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5762, "endOffset": 6499, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6579, "endOffset": 7220, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7292, "endOffset": 8252, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/InputPrompt.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 5}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "InputPrompt", "ranges": [{"startOffset": 2569, "endOffset": 18090, "count": 5}, {"startOffset": 12214, "endOffset": 12255, "count": 0}, {"startOffset": 12274, "endOffset": 12317, "count": 4}, {"startOffset": 12318, "endOffset": 12359, "count": 1}, {"startOffset": 12568, "endOffset": 12609, "count": 0}, {"startOffset": 12628, "endOffset": 12671, "count": 4}, {"startOffset": 12672, "endOffset": 12715, "count": 1}, {"startOffset": 12768, "endOffset": 12774, "count": 0}, {"startOffset": 12793, "endOffset": 12799, "count": 4}, {"startOffset": 12800, "endOffset": 12806, "count": 1}, {"startOffset": 13278, "endOffset": 13292, "count": 3}, {"startOffset": 13293, "endOffset": 14490, "count": 3}, {"startOffset": 14115, "endOffset": 14490, "count": 0}, {"startOffset": 14491, "endOffset": 16457, "count": 2}, {"startOffset": 16970, "endOffset": 17890, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3396, "endOffset": 3607, "count": 2}, {"startOffset": 3445, "endOffset": 3512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3781, "endOffset": 3869, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4225, "endOffset": 4342, "count": 5}, {"startOffset": 4263, "endOffset": 4338, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4577, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6232, "endOffset": 11442, "count": 7}, {"startOffset": 6261, "endOffset": 6286, "count": 0}, {"startOffset": 6341, "endOffset": 6356, "count": 0}, {"startOffset": 6358, "endOffset": 6416, "count": 0}, {"startOffset": 6449, "endOffset": 6464, "count": 0}, {"startOffset": 6465, "endOffset": 6495, "count": 0}, {"startOffset": 6497, "endOffset": 6601, "count": 0}, {"startOffset": 6641, "endOffset": 7488, "count": 0}, {"startOffset": 7517, "endOffset": 7536, "count": 0}, {"startOffset": 7538, "endOffset": 7594, "count": 0}, {"startOffset": 7617, "endOffset": 7636, "count": 0}, {"startOffset": 7638, "endOffset": 7704, "count": 0}, {"startOffset": 7727, "endOffset": 7746, "count": 0}, {"startOffset": 7748, "endOffset": 7816, "count": 0}, {"startOffset": 7853, "endOffset": 8032, "count": 0}, {"startOffset": 8061, "endOffset": 8080, "count": 0}, {"startOffset": 8082, "endOffset": 8168, "count": 0}, {"startOffset": 8189, "endOffset": 8208, "count": 0}, {"startOffset": 8210, "endOffset": 8338, "count": 0}, {"startOffset": 8359, "endOffset": 8378, "count": 0}, {"startOffset": 8380, "endOffset": 8430, "count": 0}, {"startOffset": 8451, "endOffset": 8470, "count": 0}, {"startOffset": 8471, "endOffset": 8501, "count": 0}, {"startOffset": 8503, "endOffset": 8563, "count": 0}, {"startOffset": 8584, "endOffset": 8603, "count": 0}, {"startOffset": 8604, "endOffset": 8634, "count": 0}, {"startOffset": 8636, "endOffset": 8698, "count": 0}, {"startOffset": 8719, "endOffset": 8738, "count": 0}, {"startOffset": 8740, "endOffset": 8797, "count": 0}, {"startOffset": 8818, "endOffset": 8837, "count": 0}, {"startOffset": 8839, "endOffset": 8895, "count": 0}, {"startOffset": 8928, "endOffset": 8973, "count": 0}, {"startOffset": 9040, "endOffset": 9085, "count": 0}, {"startOffset": 9143, "endOffset": 9171, "count": 0}, {"startOffset": 9172, "endOffset": 9207, "count": 0}, {"startOffset": 9249, "endOffset": 9488, "count": 0}, {"startOffset": 9631, "endOffset": 9701, "count": 0}, {"startOffset": 9736, "endOffset": 10233, "count": 2}, {"startOffset": 9911, "endOffset": 9915, "count": 0}, {"startOffset": 9987, "endOffset": 10108, "count": 0}, {"startOffset": 10233, "endOffset": 10263, "count": 5}, {"startOffset": 10263, "endOffset": 10293, "count": 3}, {"startOffset": 10295, "endOffset": 10817, "count": 3}, {"startOffset": 10326, "endOffset": 10512, "count": 2}, {"startOffset": 10429, "endOffset": 10483, "count": 1}, {"startOffset": 10512, "endOffset": 10562, "count": 1}, {"startOffset": 10562, "endOffset": 10668, "count": 0}, {"startOffset": 10670, "endOffset": 10696, "count": 1}, {"startOffset": 10698, "endOffset": 10746, "count": 1}, {"startOffset": 10746, "endOffset": 10792, "count": 0}, {"startOffset": 10792, "endOffset": 10817, "count": 1}, {"startOffset": 10817, "endOffset": 10879, "count": 2}, {"startOffset": 10881, "endOffset": 11404, "count": 2}, {"startOffset": 10912, "endOffset": 11094, "count": 1}, {"startOffset": 11011, "endOffset": 11065, "count": 0}, {"startOffset": 11094, "endOffset": 11144, "count": 1}, {"startOffset": 11144, "endOffset": 11249, "count": 0}, {"startOffset": 11251, "endOffset": 11279, "count": 1}, {"startOffset": 11281, "endOffset": 11331, "count": 1}, {"startOffset": 11331, "endOffset": 11379, "count": 0}, {"startOffset": 11379, "endOffset": 11404, "count": 1}, {"startOffset": 11404, "endOffset": 11441, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14511, "endOffset": 16456, "count": 2}, {"startOffset": 15466, "endOffset": 15472, "count": 0}, {"startOffset": 15785, "endOffset": 16031, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 13}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 8}], "isBlockCoverage": true}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 3}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 13}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 13}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/SuggestionsDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 260, "endOffset": 317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 366, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "SuggestionsDisplay", "ranges": [{"startOffset": 1035, "endOffset": 8712, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useInputHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "useInputHistory", "ranges": [{"startOffset": 590, "endOffset": 2698, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1578", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 2}, {"startOffset": 538, "endOffset": 546, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 10}, {"startOffset": 617, "endOffset": 625, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 12}], "isBlockCoverage": true}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 2}], "isBlockCoverage": true}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 10}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1586", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useShellHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHistoryFilePath", "ranges": [{"startOffset": 1076, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "readHistoryFile", "ranges": [{"startOffset": 1269, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeHistoryFile", "ranges": [{"startOffset": 1651, "endOffset": 1969, "count": 0}], "isBlockCoverage": false}, {"functionName": "useShellHistory", "ranges": [{"startOffset": 1970, "endOffset": 3787, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1588", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 331, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 387, "endOffset": 468, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 528, "endOffset": 613, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1589", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1590", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 516, "endOffset": 568, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5304, "endOffset": 5451, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5519, "endOffset": 5626, "count": 0}], "isBlockCoverage": true}, {"functionName": "MCPServerConfig", "ranges": [{"startOffset": 5632, "endOffset": 6190, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6213, "endOffset": 6887, "count": 0}], "isBlockCoverage": true}, {"functionName": "Config", "ranges": [{"startOffset": 6893, "endOffset": 9555, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshAuth", "ranges": [{"startOffset": 9560, "endOffset": 10741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionId", "ranges": [{"startOffset": 10746, "endOffset": 10799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGeneratorConfig", "ranges": [{"startOffset": 10804, "endOffset": 10883, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModel", "ranges": [{"startOffset": 10888, "endOffset": 10971, "count": 0}], "isBlockCoverage": false}, {"functionName": "setModel", "ranges": [{"startOffset": 10976, "endOffset": 11165, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModelSwitchedDuringSession", "ranges": [{"startOffset": 11170, "endOffset": 11256, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetModelToDefault", "ranges": [{"startOffset": 11261, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFlashFallbackHandler", "ranges": [{"startOffset": 11500, "endOffset": 11585, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEmbeddingModel", "ranges": [{"startOffset": 11590, "endOffset": 11653, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSandbox", "ranges": [{"startOffset": 11658, "endOffset": 11707, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 11712, "endOffset": 11765, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectRoot", "ranges": [{"startOffset": 11770, "endOffset": 11825, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolRegistry", "ranges": [{"startOffset": 11830, "endOffset": 11906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugMode", "ranges": [{"startOffset": 11911, "endOffset": 11964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestion", "ranges": [{"startOffset": 11969, "endOffset": 12020, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFullContext", "ranges": [{"startOffset": 12025, "endOffset": 12082, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreTools", "ranges": [{"startOffset": 12087, "endOffset": 12140, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExcludeTools", "ranges": [{"startOffset": 12145, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolDiscoveryCommand", "ranges": [{"startOffset": 12209, "endOffset": 12284, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolCallCommand", "ranges": [{"startOffset": 12289, "endOffset": 12354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServerCommand", "ranges": [{"startOffset": 12359, "endOffset": 12426, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMcpServers", "ranges": [{"startOffset": 12431, "endOffset": 12486, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserMemory", "ranges": [{"startOffset": 12491, "endOffset": 12546, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUserMemory", "ranges": [{"startOffset": 12551, "endOffset": 12628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFileCount", "ranges": [{"startOffset": 12633, "endOffset": 12700, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFileCount", "ranges": [{"startOffset": 12705, "endOffset": 12778, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApprovalMode", "ranges": [{"startOffset": 12783, "endOffset": 12842, "count": 0}], "isBlockCoverage": false}, {"functionName": "setApprovalMode", "ranges": [{"startOffset": 12847, "endOffset": 12910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowMemoryUsage", "ranges": [{"startOffset": 12915, "endOffset": 12980, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessibility", "ranges": [{"startOffset": 12985, "endOffset": 13046, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryEnabled", "ranges": [{"startOffset": 13051, "endOffset": 13136, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryLogPromptsEnabled", "ranges": [{"startOffset": 13141, "endOffset": 13238, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryOtlpEndpoint", "ranges": [{"startOffset": 13243, "endOffset": 13377, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTelemetryTarget", "ranges": [{"startOffset": 13382, "endOffset": 13507, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienClient", "ranges": [{"startOffset": 13512, "endOffset": 13569, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienDir", "ranges": [{"startOffset": 13574, "endOffset": 13699, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 13704, "endOffset": 13815, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnableRecursiveFileSearch", "ranges": [{"startOffset": 13820, "endOffset": 13919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 13924, "endOffset": 14018, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCheckpointingEnabled", "ranges": [{"startOffset": 14023, "endOffset": 14091, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProxy", "ranges": [{"startOffset": 14096, "endOffset": 14141, "count": 0}], "isBlockCoverage": false}, {"functionName": "getWorkingDir", "ranges": [{"startOffset": 14146, "endOffset": 14194, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBugCommand", "ranges": [{"startOffset": 14199, "endOffset": 14254, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileService", "ranges": [{"startOffset": 14259, "endOffset": 14483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageStatisticsEnabled", "ranges": [{"startOffset": 14488, "endOffset": 14567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExtensionContextFilePaths", "ranges": [{"startOffset": 14572, "endOffset": 14657, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGitService", "ranges": [{"startOffset": 14662, "endOffset": 14899, "count": 0}], "isBlockCoverage": false}, {"functionName": "createToolRegistry", "ranges": [{"startOffset": 14903, "endOffset": 16829, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1591", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1472, "endOffset": 1648, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1680, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3230, "endOffset": 4131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1769", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1247, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1770", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3046, "endOffset": 3754, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 3756, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 6146, "endOffset": 6761, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 6763, "endOffset": 7401, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 7402, "endOffset": 7695, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 7696, "endOffset": 7846, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 7847, "endOffset": 7999, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1783", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 949, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1218, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2462, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1784", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1785", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1248, "endOffset": 1284, "count": 0}], "isBlockCoverage": true}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1290, "endOffset": 1446, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1451, "endOffset": 1857, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1862, "endOffset": 2135, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 2140, "endOffset": 2230, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2235, "endOffset": 2331, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2336, "endOffset": 2455, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2460, "endOffset": 2588, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2593, "endOffset": 2807, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2812, "endOffset": 2867, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2872, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3344, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3770, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1790", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 959, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1138, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1238, "endOffset": 1406, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1408, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1784, "endOffset": 2250, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2251, "endOffset": 2484, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2485, "endOffset": 2605, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2606, "endOffset": 3139, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3140, "endOffset": 3197, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3198, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3340, "endOffset": 4349, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1791", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1792", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1049, "endOffset": 2840, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1793", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1433, "endOffset": 1487, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredTool", "ranges": [{"startOffset": 1493, "endOffset": 2732, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2737, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4882, "endOffset": 4933, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolRegistry", "ranges": [{"startOffset": 4939, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerTool", "ranges": [{"startOffset": 5129, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverTools", "ranges": [{"startOffset": 5575, "endOffset": 7177, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionDeclarations", "ranges": [{"startOffset": 7450, "endOffset": 7640, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllTools", "ranges": [{"startOffset": 7734, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "getToolsByServer", "ranges": [{"startOffset": 7896, "endOffset": 8158, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTool", "ranges": [{"startOffset": 8225, "endOffset": 8283, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1794", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 13}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 577, "endOffset": 679, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 1168, "endOffset": 1538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get schema", "ranges": [{"startOffset": 1647, "endOffset": 1813, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 2269, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2753, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 3048, "endOffset": 3278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1795", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3516, "endOffset": 3853, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4220, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4372, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4609, "endOffset": 4842, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4894, "endOffset": 5025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 5066, "endOffset": 5151, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5200, "endOffset": 5265, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5267, "endOffset": 7941, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 7943, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 20315, "endOffset": 20843, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1886", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 712, "endOffset": 827, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 833, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 867, "endOffset": 1405, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1410, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2753, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4273, "endOffset": 5686, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1887", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1373, "endOffset": 1398, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1404, "endOffset": 1434, "count": 1}], "isBlockCoverage": true}, {"functionName": "LSTool", "ranges": [{"startOffset": 1621, "endOffset": 2848, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3041, "endOffset": 3622, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3799, "endOffset": 4357, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldIgnore", "ranges": [{"startOffset": 4600, "endOffset": 5161, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5347, "endOffset": 5547, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorResult", "ranges": [{"startOffset": 5598, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5982, "endOffset": 10207, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1888", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 690, "endOffset": 2131, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1889", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1544, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1907, "endOffset": 4318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4756, "endOffset": 5197, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5241, "endOffset": 5604, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5650, "endOffset": 5727, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5927, "endOffset": 6056, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6260, "endOffset": 6468, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1890", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1757, "endOffset": 1782, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1788, "endOffset": 1813, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadFileTool", "ranges": [{"startOffset": 1819, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3252, "endOffset": 4595, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4600, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 4993, "endOffset": 6235, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1891", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1752, "endOffset": 1939, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2182, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3140, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4692, "endOffset": 6028, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6428, "endOffset": 11566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1896", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1747, "endOffset": 1891, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2136, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2242, "endOffset": 2413, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2415, "endOffset": 4312, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4314, "endOffset": 4828, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4830, "endOffset": 5085, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5087, "endOffset": 5603, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5605, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6151, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1992", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1993", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 65003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2536, "endOffset": 2549, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2555, "endOffset": 2590, "count": 1}], "isBlockCoverage": true}, {"functionName": "GrepTool", "ranges": [{"startOffset": 2799, "endOffset": 4210, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveAndValidatePath", "ranges": [{"startOffset": 4563, "endOffset": 5695, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5872, "endOffset": 6580, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6783, "endOffset": 9645, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCommandAvailable", "ranges": [{"startOffset": 9926, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrepOutput", "ranges": [{"startOffset": 11020, "endOffset": 12605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 12774, "endOffset": 13495, "count": 0}], "isBlockCoverage": false}, {"functionName": "performGrepSearch", "ranges": [{"startOffset": 13739, "endOffset": 22277, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2013", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6926, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1059, "endOffset": 1144, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1146, "endOffset": 1362, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1488, "endOffset": 2306, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2308, "endOffset": 2566, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2019", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2020", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1765, "endOffset": 2458, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2596, "endOffset": 2621, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2627, "endOffset": 2647, "count": 1}], "isBlockCoverage": true}, {"functionName": "GlobTool", "ranges": [{"startOffset": 2783, "endOffset": 4263, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 4338, "endOffset": 4932, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4999, "endOffset": 6375, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6445, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6975, "endOffset": 10877, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2021", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2525, "endOffset": 2548, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2554, "endOffset": 2591, "count": 0}], "isBlockCoverage": true}, {"functionName": "EditTool", "ranges": [{"startOffset": 2727, "endOffset": 6048, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 6257, "endOffset": 6690, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 6859, "endOffset": 7439, "count": 0}], "isBlockCoverage": false}, {"functionName": "_applyReplacement", "ranges": [{"startOffset": 7444, "endOffset": 8050, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateEdit", "ranges": [{"startOffset": 8345, "endOffset": 11949, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 12095, "endOffset": 13827, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 13832, "endOffset": 14832, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 15015, "endOffset": 17873, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureParentDirectoriesExist", "ranges": [{"startOffset": 17948, "endOffset": 18202, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 18207, "endOffset": 19573, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2023", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 963, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1010, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1193, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 2846, "endOffset": 7832, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 7834, "endOffset": 8426, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 8916, "endOffset": 11293, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 11899, "endOffset": 14728, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 15211, "endOffset": 17532, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 17964, "endOffset": 19887, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 19889, "endOffset": 20549, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForArienBug", "ranges": [{"startOffset": 20627, "endOffset": 22935, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 22994, "endOffset": 23303, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 23305, "endOffset": 23427, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2024", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 399, "endOffset": 417, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2025", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2020, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2032, "endOffset": 2065, "count": 1}], "isBlockCoverage": true}, {"functionName": "ShellTool", "ranges": [{"startOffset": 2098, "endOffset": 4093, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 4098, "endOffset": 4628, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommandRoot", "ranges": [{"startOffset": 4633, "endOffset": 5152, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 5157, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6114, "endOffset": 6982, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6987, "endOffset": 16021, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2027", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2893, "endOffset": 2943, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2905, "endOffset": 2931, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteFileTool", "ranges": [{"startOffset": 2949, "endOffset": 3800, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3805, "endOffset": 4311, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 4316, "endOffset": 5806, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 5811, "endOffset": 6184, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6268, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7961, "endOffset": 11962, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getCorrected<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 11967, "endOffset": 14133, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModifyContext", "ranges": [{"startOffset": 14138, "endOffset": 15015, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2028", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1985, "endOffset": 2099, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2244, "endOffset": 2250, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2256, "endOffset": 2281, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebFetchTool", "ranges": [{"startOffset": 2287, "endOffset": 3153, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>back", "ranges": [{"startOffset": 3158, "endOffset": 5558, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 5563, "endOffset": 6212, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 6217, "endOffset": 6469, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 6474, "endOffset": 7711, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7716, "endOffset": 12476, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2029", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2030", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1150, "endOffset": 1154, "count": 0}], "isBlockCoverage": true}, {"functionName": "FetchError", "ranges": [{"startOffset": 1160, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1282, "endOffset": 1492, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1494, "endOffset": 2120, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2063", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3449, "endOffset": 3537, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3476, "endOffset": 3507, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 3789, "endOffset": 7980, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 7985, "endOffset": 9539, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 9544, "endOffset": 11151, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 11156, "endOffset": 19498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2064", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 1}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3737, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 4068, "endOffset": 4241, "count": 1}, {"startOffset": 4154, "endOffset": 4203, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 4243, "endOffset": 4412, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4414, "endOffset": 4549, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4621, "endOffset": 4942, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5032, "endOffset": 5071, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5077, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5213, "endOffset": 7631, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7636, "endOffset": 9167, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2065", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1399, "endOffset": 1405, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1411, "endOffset": 1444, "count": 1}], "isBlockCoverage": true}, {"functionName": "WebSearchTool", "ranges": [{"startOffset": 1450, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateParams", "ranges": [{"startOffset": 2014, "endOffset": 2427, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2432, "endOffset": 2521, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2526, "endOffset": 6272, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2066", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 3118, "endOffset": 3236, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3261, "endOffset": 3429, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienClient", "ranges": [{"startOffset": 3435, "endOffset": 3752, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3757, "endOffset": 3962, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentGenerator", "ranges": [{"startOffset": 3967, "endOffset": 4149, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 4154, "endOffset": 4231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChat", "ranges": [{"startOffset": 4236, "endOffset": 4369, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 4374, "endOffset": 4444, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 4449, "endOffset": 4526, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetChat", "ranges": [{"startOffset": 4531, "endOffset": 4625, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEnvironment", "ranges": [{"startOffset": 4630, "endOffset": 6989, "count": 0}], "isBlockCoverage": false}, {"functionName": "startChat", "ranges": [{"startOffset": 6994, "endOffset": 8646, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 8651, "endOffset": 9822, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJson", "ranges": [{"startOffset": 9827, "endOffset": 12489, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 12494, "endOffset": 14052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateEmbedding", "ranges": [{"startOffset": 14057, "endOffset": 15157, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryCompressChat", "ranges": [{"startOffset": 15162, "endOffset": 17765, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 17957, "endOffset": 19078, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2067", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 995, "endOffset": 6814, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7082, "endOffset": 9356, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9777, "endOffset": 12254, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2068", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 372, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1677, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1805, "endOffset": 1881, "count": 0}], "isBlockCoverage": true}, {"functionName": "Turn", "ranges": [{"startOffset": 1887, "endOffset": 2008, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 2083, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePendingFunctionCall", "ranges": [{"startOffset": 5910, "endOffset": 6524, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDebugResponses", "ranges": [{"startOffset": 6529, "endOffset": 6592, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsageMetadata", "ranges": [{"startOffset": 6597, "endOffset": 6662, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2069", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4498, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2070", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 72595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2803, "endOffset": 31135, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2071", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3232, "endOffset": 6198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2072", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 398, "endOffset": 569, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2073", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 66722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3468, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3779, "endOffset": 4875, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5082, "endOffset": 5277, "count": 0}], "isBlockCoverage": true}, {"functionName": "ArienChat", "ranges": [{"startOffset": 5283, "endOffset": 5561, "count": 0}], "isBlockCoverage": false}, {"functionName": "_getRequestTextFromContents", "ranges": [{"startOffset": 5566, "endOffset": 5780, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiRequest", "ranges": [{"startOffset": 5785, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiResponse", "ranges": [{"startOffset": 6032, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "_logApiError", "ranges": [{"startOffset": 6279, "endOffset": 6640, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFlashFallback", "ranges": [{"startOffset": 6832, "endOffset": 7915, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessage", "ranges": [{"startOffset": 8567, "endOffset": 11495, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendMessageStream", "ranges": [{"startOffset": 12194, "endOffset": 14714, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistory", "ranges": [{"startOffset": 15750, "endOffset": 16039, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHistory", "ranges": [{"startOffset": 16092, "endOffset": 16141, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHistory", "ranges": [{"startOffset": 16273, "endOffset": 16336, "count": 0}], "isBlockCoverage": false}, {"functionName": "setHistory", "ranges": [{"startOffset": 16341, "endOffset": 16400, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFinalUsageMetadata", "ranges": [{"startOffset": 16405, "endOffset": 16635, "count": 0}], "isBlockCoverage": false}, {"functionName": "processStreamResponse", "ranges": [{"startOffset": 16640, "endOffset": 18155, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordHistory", "ranges": [{"startOffset": 18160, "endOffset": 21626, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTextContent", "ranges": [{"startOffset": 21631, "endOffset": 21899, "count": 0}], "isBlockCoverage": false}, {"functionName": "isT<PERSON><PERSON><PERSON><PERSON>nt", "ranges": [{"startOffset": 21904, "endOffset": 22184, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2074", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1000, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1711, "endOffset": 1795, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2103, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 5455, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 6244, "endOffset": 7452, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 7671, "endOffset": 7930, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 8175, "endOffset": 9298, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2075", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2375, "endOffset": 2425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2427, "endOffset": 2532, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2533, "endOffset": 3720, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3722, "endOffset": 4490, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4492, "endOffset": 5628, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5630, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6272, "endOffset": 7445, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7447, "endOffset": 9239, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2110", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 4345, "endOffset": 4418, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 4420, "endOffset": 5097, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 5098, "endOffset": 7537, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7539, "endOffset": 7968, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2604", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1870, "endOffset": 1885, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1891, "endOffset": 2083, "count": 0}], "isBlockCoverage": true}, {"functionName": "ClearcutLogger", "ranges": [{"startOffset": 2139, "endOffset": 2196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstance", "ranges": [{"startOffset": 2208, "endOffset": 2496, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueueLogEvent", "ranges": [{"startOffset": 2601, "endOffset": 2804, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLogEvent", "ranges": [{"startOffset": 2809, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushIfNeeded", "ranges": [{"startOffset": 3091, "endOffset": 3251, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushToClearcut", "ranges": [{"startOffset": 3256, "endOffset": 5307, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeLogResponse", "ranges": [{"startOffset": 5396, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "logStartSessionEvent", "ranges": [{"startOffset": 6718, "endOffset": 9465, "count": 0}], "isBlockCoverage": false}, {"functionName": "logNewPromptEvent", "ranges": [{"startOffset": 9470, "endOffset": 9837, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCallEvent", "ranges": [{"startOffset": 9842, "endOffset": 11128, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequestEvent", "ranges": [{"startOffset": 11133, "endOffset": 11493, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponseEvent", "ranges": [{"startOffset": 11498, "endOffset": 13468, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiErrorEvent", "ranges": [{"startOffset": 13473, "endOffset": 14389, "count": 0}], "isBlockCoverage": false}, {"functionName": "logEndSessionEvent", "ranges": [{"startOffset": 14394, "endOffset": 14804, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdown", "ranges": [{"startOffset": 14809, "endOffset": 14945, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2605", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1762, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1810, "endOffset": 2480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2512, "endOffset": 2837, "count": 0}], "isBlockCoverage": true}, {"functionName": "StartSessionEvent", "ranges": [{"startOffset": 2843, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4225, "endOffset": 4288, "count": 0}], "isBlockCoverage": true}, {"functionName": "EndSessionEvent", "ranges": [{"startOffset": 4294, "endOffset": 4475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4507, "endOffset": 4585, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserPromptEvent", "ranges": [{"startOffset": 4591, "endOffset": 4811, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4841, "endOffset": 4997, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolCallEvent", "ranges": [{"startOffset": 5003, "endOffset": 5542, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5574, "endOffset": 5650, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiRequestEvent", "ranges": [{"startOffset": 5656, "endOffset": 5870, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5900, "endOffset": 6019, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiErrorEvent", "ranges": [{"startOffset": 6025, "endOffset": 6372, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6405, "endOffset": 6654, "count": 0}], "isBlockCoverage": true}, {"functionName": "ApiResponseEvent", "ranges": [{"startOffset": 6660, "endOffset": 7382, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2606", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8055, "endOffset": 8354, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2607", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6096, "count": 1}, {"startOffset": 1116, "endOffset": 1121, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureArienDirExists", "ranges": [{"startOffset": 1282, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "readUserIdFromFile", "ranges": [{"startOffset": 1451, "endOffset": 1682, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeUserIdToFile", "ranges": [{"startOffset": 1683, "endOffset": 1791, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPersistentUserId", "ranges": [{"startOffset": 1967, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2608", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2746", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 992, "endOffset": 1061, "count": 0}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 1067, "endOffset": 1832, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1915, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 2463, "endOffset": 2630, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2706, "endOffset": 2879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2949, "endOffset": 3041, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2747", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1010, "endOffset": 1086, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1092, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 1200, "endOffset": 1618, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1623, "endOffset": 2177, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 2182, "endOffset": 2283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 2288, "endOffset": 2816, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2821, "endOffset": 2872, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2749", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1719, "endOffset": 1730, "count": 0}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1736, "endOffset": 1839, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1844, "endOffset": 2078, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 2083, "endOffset": 2483, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 2488, "endOffset": 2795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2936, "endOffset": 4536, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 4541, "endOffset": 4951, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 4956, "endOffset": 5098, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 5103, "endOffset": 5313, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 5318, "endOffset": 5597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2756", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4170, "endOffset": 4276, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2757", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1097, "endOffset": 1169, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1238, "endOffset": 1413, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1489, "endOffset": 1555, "count": 0}], "isBlockCoverage": false}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1560, "endOffset": 2994, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2999, "endOffset": 3521, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3526, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4825, "endOffset": 7173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 7178, "endOffset": 7603, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 7608, "endOffset": 8759, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 8764, "endOffset": 9090, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 9095, "endOffset": 9609, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 9614, "endOffset": 10632, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 10637, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2758", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/arienRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2759", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1290, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1501, "endOffset": 3247, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3277, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3586, "endOffset": 3748, "count": 0}], "isBlockCoverage": true}, {"functionName": "CoreToolScheduler", "ranges": [{"startOffset": 3754, "endOffset": 4222, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatusInternal", "ranges": [{"startOffset": 4227, "endOffset": 8661, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArgsInternal", "ranges": [{"startOffset": 8666, "endOffset": 8979, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRunning", "ranges": [{"startOffset": 8984, "endOffset": 9117, "count": 0}], "isBlockCoverage": false}, {"functionName": "schedule", "ranges": [{"startOffset": 9122, "endOffset": 11771, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmationResponse", "ranges": [{"startOffset": 11776, "endOffset": 13799, "count": 0}], "isBlockCoverage": false}, {"functionName": "attemptExecutionOfScheduledCalls", "ranges": [{"startOffset": 13804, "endOffset": 16328, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAndNotifyCompletion", "ranges": [{"startOffset": 16333, "endOffset": 17051, "count": 0}], "isBlockCoverage": false}, {"functionName": "notifyToolCallsUpdate", "ranges": [{"startOffset": 17056, "endOffset": 17192, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2760", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1626, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1702, "endOffset": 2744, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2745, "endOffset": 3774, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3775, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4335, "endOffset": 5000, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2761", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1159, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1160, "endOffset": 1380, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1715, "endOffset": 1935, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1937, "endOffset": 2168, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2327, "endOffset": 2530, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2587, "endOffset": 4512, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4725, "endOffset": 6603, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2762", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 933, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2763", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1420, "endOffset": 1484, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1559, "endOffset": 1621, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1697, "endOffset": 1761, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1813, "endOffset": 2906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienMdFilePathsInternal", "ranges": [{"startOffset": 2907, "endOffset": 6602, "count": 0}], "isBlockCoverage": false}, {"functionName": "readArienMdFiles", "ranges": [{"startOffset": 6603, "endOffset": 7464, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7465, "endOffset": 8104, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8105, "endOffset": 9189, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2764", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2765", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2766", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useCompletion.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "useCompletion", "ranges": [{"startOffset": 1366, "endOffset": 13229, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2767", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useKeypress.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 5}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "useKeypress", "ranges": [{"startOffset": 896, "endOffset": 2745, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1123, "endOffset": 1174, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1229, "endOffset": 2710, "count": 5}, {"startOffset": 1272, "endOffset": 1293, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleKeypress", "ranges": [{"startOffset": 1488, "endOffset": 2205, "count": 7}, {"startOffset": 1540, "endOffset": 1573, "count": 0}, {"startOffset": 1609, "endOffset": 1882, "count": 0}, {"startOffset": 1911, "endOffset": 2003, "count": 0}, {"startOffset": 2047, "endOffset": 2075, "count": 2}, {"startOffset": 2077, "endOffset": 2119, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2325, "endOffset": 2705, "count": 5}, {"startOffset": 2451, "endOffset": 2699, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2768", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/commandUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 5}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 338, "endOffset": 386, "count": 5}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "isAtCommand", "ranges": [{"startOffset": 497, "endOffset": 607, "count": 5}], "isBlockCoverage": true}, {"functionName": "isSlashCommand", "ranges": [{"startOffset": 632, "endOffset": 664, "count": 5}], "isBlockCoverage": true}], "startOffset": 209}]}