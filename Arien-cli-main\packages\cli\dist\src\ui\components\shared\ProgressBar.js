import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
export const ProgressBar = ({ progress, width = 20, showPercentage = true, color = Colors.AccentCyan, backgroundColor = Colors.Gray, character = '█', }) => {
    const clampedProgress = Math.max(0, Math.min(100, progress));
    const filledWidth = Math.floor((clampedProgress / 100) * width);
    const emptyWidth = width - filledWidth;
    return (_jsxs(Box, { children: [_jsx(Text, { color: backgroundColor, children: "\u300E" }), _jsx(Text, { color: color, children: character.repeat(filledWidth) }), _jsx(Text, { color: backgroundColor, dimColor: true, children: '░'.repeat(emptyWidth) }), _jsx(Text, { color: backgroundColor, children: "\u300F" }), showPercentage && (_jsxs(Text, { color: color, children: [" ", clampedProgress, "%"] }))] }));
};
export const IndeterminateProgress = ({ width = 20, color = Colors.AccentPurple, animationFrame = 0, }) => {
    const position = animationFrame % (width + 4);
    const barPosition = Math.max(0, Math.min(position - 2, width - 3));
    const bar = Array(width)
        .fill('░')
        .map((char, i) => {
        if (i >= barPosition && i < barPosition + 3 && position >= 2 && position < width + 2) {
            return '█';
        }
        return char;
    })
        .join('');
    return (_jsxs(Box, { children: [_jsx(Text, { color: Colors.Gray, children: "\u300E" }), _jsx(Text, { color: color, children: bar }), _jsx(Text, { color: Colors.Gray, children: "\u300F" })] }));
};
//# sourceMappingURL=ProgressBar.js.map