{"version": 3, "file": "mcp-server-registry.js", "sourceRoot": "", "sources": ["../../../src/config/mcp-server-registry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAehE;;GAEG;AACH,MAAM,OAAO,iBAAiB;IACpB,MAAM,CAAU,gBAAgB,GAA2C;QACjF,WAAW,EAAE;YACX,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,8BAA8B,CAAC,EACtC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,qDAAqD,CACtD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC;YAC5C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,8CAA8C,CAC/C;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,CAAC;YAC1C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,qBAAqB,EAAE;YACrB,IAAI,EAAE,qBAAqB;YAC3B,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,kDAAkD,CAAC,EAC1D,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,4DAA4D,CAC7D;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC;YAC3C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,gDAAgD,EAAE,IAAI,EAAE,GAAG,CAAC,EACnE,SAAS,EACT,OAAO,CAAC,GAAG,EAAE,EAAE,uCAAuC;YACtD,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,EAAE,+CAA+C;YACrD,SAAS,EACT,2CAA2C,CAC5C;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC;YACnC,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,KAAK,EAAE;YACL,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,QAAQ,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAC,EACnE,SAAS,EACT,SAAS,EAAE,iDAAiD;YAC5D,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK,EAAE,0DAA0D;YACjE,SAAS,EACT,+CAA+C,CAChD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,aAAa,CAAC;YAC/C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;aACzB;SACF;QAED,QAAQ,EAAE;YACR,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,EAAE,qBAAqB;YAC3B,SAAS,EACT,0CAA0C,CAC3C;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;YAC1C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,6FAA6F;QAC7F,gFAAgF;KACjF,CAAC;IAEM,MAAM,CAAU,oBAAoB,GAA2C;QACrF,mFAAmF;QACnF,QAAQ,EAAE;YACR,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,0CAA0C,CAC3C;YACD,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;YAC1C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;KACF,CAAC;IAEM,MAAM,CAAU,oBAAoB,GAA2C;QACrF,yDAAyD;QACzD,cAAc,EAAE;YACd,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,2CAA2C,CAAC,EACnD,EAAE,aAAa,EAAE,yBAAyB,EAAE,EAC5C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,kDAAkD,CACnD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;YAC9B,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;gBACjB,WAAW,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE;aAC3C;SACF;KACF,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9D,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,IAAY;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAC1C,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACvC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC3C,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;SAC5C,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAAc;QACpC,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACvC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC3C,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;SAC5C,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,UAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACjF,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,6BAA6B;QAC7B,IAAI,KAAK,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;YACxC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;gBACzD,IAAI,CAAC;oBACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;oBAC/C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;oBAElC,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,iBAAiB,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,CAAC,mBAAmB,EAAE,WAAW,EAAE,CAAC;YAC3C,KAAK,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC1F,IAAI,WAAW,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvD,OAAO,CAAC,IAAI,CAAC,yBAAyB,MAAM,eAAe,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,iBAAiB,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YACvC,mBAAmB,EAAE,OAAO;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,UAAkB;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,wCAAwC;QACxC,IAAI,KAAK,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;YACxC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;gBACzD,QAAQ,OAAO,EAAE,CAAC;oBAChB,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;wBAC7E,MAAM;oBACR,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;wBAC5G,MAAM;oBACR,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;wBAC3D,MAAM;oBACR;wBACE,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,yCAAyC,CAAC,CAAC;gBACnF,CAAC;YACH,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,KAAK,CAAC,mBAAmB,EAAE,WAAW,EAAE,CAAC;YAC3C,KAAK,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC1F,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;oBAC/B,YAAY,CAAC,IAAI,CAAC,4BAA4B,MAAM,2CAA2C,CAAC,CAAC;gBACnG,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,wBAAwB,CACnC,UAAkB,EAClB,mBAA6B;QAE7B,mFAAmF;QACnF,IAAI,UAAU,KAAK,KAAK,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjF,wCAAwC;YACxC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACvB,yDAAyD;oBACzD,MAAM,gBAAgB,GAAG;wBACvB;4BACE,OAAO,EAAE,2BAA2B;4BACpC,IAAI,EAAE,CAAC,IAAI,EAAE,2BAA2B,EAAE,cAAc,EAAE,GAAG,CAAC;4BAC9D,WAAW,EAAE,oEAAoE;yBAClF;wBACD;4BACE,OAAO,EAAE,kCAAkC;4BAC3C,IAAI,EAAE,CAAC,IAAI,EAAE,kCAAkC,EAAE,cAAc,EAAE,GAAG,CAAC;4BACrE,WAAW,EAAE,mEAAmE;yBACjF;qBACF,CAAC;oBAEF,oCAAoC;oBACpC,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;wBACtC,OAAO,IAAI,eAAe,CACxB,KAAK,EACL,MAAM,CAAC,IAAI,EACX,SAAS,EACT,SAAS,EAAE,iDAAiD;wBAC5D,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK,EAAE,sDAAsD;wBAC7D,SAAS,EACT,MAAM,CAAC,WAAW,CACnB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sDAAsD,KAAK,EAAE,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,+DAA+D;QAC/D,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACvB,sDAAsD;oBACtD,MAAM,gBAAgB,GAA4D;wBAChF,SAAS,EAAE;4BACT,IAAI,EAAE,CAAC,IAAI,EAAE,sCAAsC,CAAC;4BACpD,WAAW,EAAE,mCAAmC;yBACjD;wBACD,YAAY,EAAE;4BACZ,IAAI,EAAE,CAAC,IAAI,EAAE,uCAAuC,CAAC;4BACrD,WAAW,EAAE,sCAAsC;yBACpD;qBACF,CAAC;oBAEF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAC9C,IAAI,QAAQ,EAAE,CAAC;wBACb,OAAO,IAAI,eAAe,CACxB,KAAK,EACL,QAAQ,CAAC,IAAI,EACb,SAAS,EACT,OAAO,CAAC,GAAG,EAAE,EACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK,EACL,SAAS,EACT,QAAQ,CAAC,WAAW,CACrB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,UAAU,cAAc,KAAK,EAAE,CAAC,CAAC;YACzF,CAAC;QACH,CAAC;QAED,+DAA+D;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,OAAe;QAC3D,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9E,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE;aACvB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB;QAC/B,MAAM,iBAAiB,GAAoC,EAAE,CAAC;QAE9D,4DAA4D;QAC5D,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEpF,qEAAqE;QACrE,2DAA2D;QAC3D,MAAM,0BAA0B,GAAG,CAAC,YAAY,CAAC,CAAC;QAElD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClE,IAAI,0BAA0B,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9C,IAAI,WAAW,EAAE,CAAC;oBAChB,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,uDAAuD,CAAC,CAAC;gBAC/F,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,YAAY,CAAC,iBAAiB,EAAE,CAAC;oBACnC,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;oBACvC,IAAI,WAAW,EAAE,CAAC;wBAChB,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,4DAA4D;oBAC5D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;oBACnG,IAAI,cAAc,EAAE,CAAC;wBACnB,iBAAiB,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;wBACzC,IAAI,WAAW,EAAE,CAAC;4BAChB,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,gCAAgC,CAAC,CAAC;wBACvE,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,IAAI,WAAW,EAAE,CAAC;4BAChB,OAAO,CAAC,IAAI,CAAC,iBAAiB,IAAI,2CAA2C,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BAE5H,yDAAyD;4BACzD,MAAM,eAAe,GAAG,YAAY,CAAC,mBAAmB;iCACrD,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iCACtC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iCACvC,MAAM,CAAC,OAAO,CAAa,CAAC;4BAE/B,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAC/B,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;gCAChF,IAAI,CAAC;oCACH,MAAM,cAAc,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;oCACrG,mBAAmB,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;oCAEjE,2CAA2C;oCAC3C,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oCACrE,IAAI,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;wCAC1C,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;wCACvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,yCAAyC,CAAC,CAAC;wCAC5E,SAAS;oCACX,CAAC;gCACH,CAAC;gCAAC,OAAO,YAAY,EAAE,CAAC;oCACtB,OAAO,CAAC,IAAI,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;gCACpE,CAAC;4BACH,CAAC;4BAED,OAAO,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;4BACrF,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;4BAC5D,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,WAAW,EAAE,CAAC,CAAC,CAAC;wBAC3E,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,WAAW,EAAE,CAAC;oBAChB,OAAO,CAAC,IAAI,CAAC,iBAAiB,IAAI,wBAAwB,KAAK,EAAE,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC"}