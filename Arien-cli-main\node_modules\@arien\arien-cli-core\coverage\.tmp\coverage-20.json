{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-tool.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1066, "endOffset": 11142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1425, "endOffset": 1561, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1661, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1716, "endOffset": 3525, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1813, "endOffset": 2581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2675, "endOffset": 3074, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3154, "endOffset": 3519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3576, "endOffset": 6205, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3699, "endOffset": 4961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5063, "endOffset": 5610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5704, "endOffset": 6199, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6269, "endOffset": 11138, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6350, "endOffset": 6756, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6840, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7371, "endOffset": 7892, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8001, "endOffset": 8994, "count": 1}, {"startOffset": 8732, "endOffset": 8988, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9087, "endOffset": 10030, "count": 1}, {"startOffset": 9904, "endOffset": 10024, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10119, "endOffset": 11132, "count": 1}, {"startOffset": 11006, "endOffset": 11126, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 28}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 629, "endOffset": 1194, "count": 12}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1197, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1245, "endOffset": 2342, "count": 6}, {"startOffset": 1439, "endOffset": 1466, "count": 1}, {"startOffset": 1466, "endOffset": 1528, "count": 5}, {"startOffset": 1528, "endOffset": 1580, "count": 4}, {"startOffset": 1582, "endOffset": 1609, "count": 2}, {"startOffset": 1609, "endOffset": 2341, "count": 3}], "isBlockCoverage": true}, {"functionName": "onConfirm", "ranges": [{"startOffset": 1946, "endOffset": 2299, "count": 2}, {"startOffset": 2058, "endOffset": 2291, "count": 1}], "isBlockCoverage": true}, {"functionName": "execute", "ranges": [{"startOffset": 2345, "endOffset": 2669, "count": 3}, {"startOffset": 2545, "endOffset": 2668, "count": 2}], "isBlockCoverage": true}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 2673, "endOffset": 3597, "count": 2}, {"startOffset": 2761, "endOffset": 3400, "count": 1}, {"startOffset": 3401, "endOffset": 3438, "count": 0}, {"startOffset": 3484, "endOffset": 3518, "count": 1}, {"startOffset": 3518, "endOffset": 3596, "count": 0}], "isBlockCoverage": true}, {"functionName": "processFunctionResponse", "ranges": [{"startOffset": 2833, "endOffset": 3315, "count": 1}, {"startOffset": 3209, "endOffset": 3314, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3074, "endOffset": 3098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3175, "endOffset": 3188, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 5}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 12}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 4}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}