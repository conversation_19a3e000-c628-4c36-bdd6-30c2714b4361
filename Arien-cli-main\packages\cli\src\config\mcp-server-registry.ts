/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { MCPServerConfig } from '@arien/arien-cli-core';
import { DependencyInstaller } from './dependency-installer.js';

export interface MCPServerRegistryEntry {
  name: string;
  config: MCPServerConfig;
  verified: boolean;
  lastVerified: Date;
  category: string;
  tags: string[];
  minimumRequirements?: {
    commands?: string[];
    environment?: Record<string, string>;
  };
}

/**
 * Registry of verified MCP servers with automatic updates and validation
 */
export class MCPServerRegistry {
  private static readonly VERIFIED_SERVERS: Record<string, MCPServerRegistryEntry> = {
    'Context 7': {
      name: 'Context 7',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@upstash/context7-mcp@latest'],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'Context7 MCP server for enhanced context management'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'Documentation',
      tags: ['context', 'documentation', 'search'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },

    'Playwright': {
      name: 'Playwright',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@playwright/mcp@latest'],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'Playwright MCP server for browser automation'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'Automation',
      tags: ['browser', 'automation', 'testing'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },

    'Sequential Thinking': {
      name: 'Sequential Thinking',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@modelcontextprotocol/server-sequential-thinking'],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'Sequential Thinking MCP server for dynamic problem-solving'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'AI Tools',
      tags: ['thinking', 'problem-solving', 'ai'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },

    'Filesystem': {
      name: 'Filesystem',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@modelcontextprotocol/server-filesystem@latest', '--', '.'],
        undefined,
        process.cwd(), // Explicitly set the working directory
        undefined,
        undefined,
        undefined,
        5000, // Shorter timeout for faster failure detection
        undefined,
        'Filesystem MCP server for file operations'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'System',
      tags: ['filesystem', 'files', 'io'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },

    'Git': {
      name: 'Git',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@cyanheads/git-mcp-server', '--repository', '.'],
        undefined,
        undefined, // Let npx handle working directory automatically
        undefined,
        undefined,
        undefined,
        15000, // Longer timeout for package installation and startup
        undefined,
        'Git MCP server for version control operations (cyanheads)'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'Development',
      tags: ['git', 'version-control', 'development'],
      minimumRequirements: {
        commands: ['npx', 'git'],
      },
    },

    'Memory': {
      name: 'Memory',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@modelcontextprotocol/server-memory'],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        8000, // Reasonable timeout
        undefined,
        'Memory MCP server for persistent storage'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'Utility',
      tags: ['memory', 'storage', 'persistence'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },

    // Note: Time server removed - package @modelcontextprotocol/server-time doesn't exist in npm
    // Users can implement time functionality using built-in JavaScript Date objects
  };

  private static readonly EXPERIMENTAL_SERVERS: Record<string, MCPServerRegistryEntry> = {
    // Servers that are available but may require additional setup or have known issues
    'Memory': {
      name: 'Memory',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@modelcontextprotocol/server-memory'],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'Memory MCP server for persistent storage'
      ),
      verified: false,
      lastVerified: new Date('2025-01-02'),
      category: 'Storage',
      tags: ['memory', 'storage', 'persistence'],
      minimumRequirements: {
        commands: ['npx'],
      },
    },
  };

  private static readonly CONFIGURABLE_SERVERS: Record<string, MCPServerRegistryEntry> = {
    // Servers that require API keys or special configuration
    'Brave Search': {
      name: 'Brave Search',
      config: new MCPServerConfig(
        'npx',
        ['-y', '@modelcontextprotocol/server-brave-search'],
        { BRAVE_API_KEY: 'your_brave_api_key_here' },
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'Brave Search MCP server (requires BRAVE_API_KEY)'
      ),
      verified: true,
      lastVerified: new Date('2025-01-02'),
      category: 'Search',
      tags: ['search', 'web', 'api'],
      minimumRequirements: {
        commands: ['npx'],
        environment: { BRAVE_API_KEY: 'required' },
      },
    },
  };

  /**
   * Gets all verified MCP servers that should be included by default
   */
  static getVerifiedServers(): Record<string, MCPServerConfig> {
    const servers: Record<string, MCPServerConfig> = {};
    
    Object.entries(this.VERIFIED_SERVERS).forEach(([name, entry]) => {
      servers[name] = entry.config;
    });

    return servers;
  }

  /**
   * Gets experimental servers (not included by default)
   */
  static getExperimentalServers(): Record<string, MCPServerConfig> {
    const servers: Record<string, MCPServerConfig> = {};
    
    Object.entries(this.EXPERIMENTAL_SERVERS).forEach(([name, entry]) => {
      servers[name] = entry.config;
    });

    return servers;
  }

  /**
   * Gets servers that require configuration (API keys, etc.)
   */
  static getConfigurableServers(): Record<string, MCPServerConfig> {
    const servers: Record<string, MCPServerConfig> = {};
    
    Object.entries(this.CONFIGURABLE_SERVERS).forEach(([name, entry]) => {
      servers[name] = entry.config;
    });

    return servers;
  }

  /**
   * Gets a specific server entry with metadata
   */
  static getServerEntry(name: string): MCPServerRegistryEntry | undefined {
    return this.VERIFIED_SERVERS[name] || 
           this.EXPERIMENTAL_SERVERS[name] || 
           this.CONFIGURABLE_SERVERS[name];
  }

  /**
   * Gets all servers by category
   */
  static getServersByCategory(category: string): MCPServerRegistryEntry[] {
    const allServers = [
      ...Object.values(this.VERIFIED_SERVERS),
      ...Object.values(this.EXPERIMENTAL_SERVERS),
      ...Object.values(this.CONFIGURABLE_SERVERS),
    ];

    return allServers.filter(server => server.category === category);
  }

  /**
   * Gets all servers with specific tags
   */
  static getServersByTags(tags: string[]): MCPServerRegistryEntry[] {
    const allServers = [
      ...Object.values(this.VERIFIED_SERVERS),
      ...Object.values(this.EXPERIMENTAL_SERVERS),
      ...Object.values(this.CONFIGURABLE_SERVERS),
    ];

    return allServers.filter(server => 
      tags.some(tag => server.tags.includes(tag))
    );
  }

  /**
   * Checks if a server meets system requirements
   */
  static async checkServerRequirements(
    serverName: string
  ): Promise<{ meetsRequirements: boolean; missingRequirements: string[] }> {
    const entry = this.getServerEntry(serverName);
    if (!entry) {
      return { meetsRequirements: false, missingRequirements: ['Server not found'] };
    }

    const missing: string[] = [];

    // Check command requirements
    if (entry.minimumRequirements?.commands) {
      for (const command of entry.minimumRequirements.commands) {
        try {
          const { exec } = await import('child_process');
          const { promisify } = await import('util');
          const execAsync = promisify(exec);
          
          await execAsync(`${command} --version`, { timeout: 5000 });
        } catch (error) {
          missing.push(`Command '${command}' not available`);
        }
      }
    }

    // Check environment requirements
    if (entry.minimumRequirements?.environment) {
      for (const [envVar, requirement] of Object.entries(entry.minimumRequirements.environment)) {
        if (requirement === 'required' && !process.env[envVar]) {
          missing.push(`Environment variable '${envVar}' is required`);
        }
      }
    }

    return {
      meetsRequirements: missing.length === 0,
      missingRequirements: missing,
    };
  }

  /**
   * Gets installation instructions for a server
   */
  static getInstallationInstructions(serverName: string): string[] {
    const entry = this.getServerEntry(serverName);
    if (!entry) {
      return ['Server not found in registry'];
    }

    const instructions: string[] = [];

    // Add command installation instructions
    if (entry.minimumRequirements?.commands) {
      for (const command of entry.minimumRequirements.commands) {
        switch (command) {
          case 'npx':
            instructions.push('Install Node.js from https://nodejs.org/ (includes npx)');
            break;
          case 'uvx':
            instructions.push('Install uv from https://docs.astral.sh/uv/getting-started/installation/ (includes uvx)');
            break;
          case 'git':
            instructions.push('Install Git from https://git-scm.com/');
            break;
          default:
            instructions.push(`Install ${command} (check documentation for instructions)`);
        }
      }
    }

    // Add environment setup instructions
    if (entry.minimumRequirements?.environment) {
      for (const [envVar, requirement] of Object.entries(entry.minimumRequirements.environment)) {
        if (requirement === 'required') {
          instructions.push(`Set environment variable ${envVar} (check server documentation for details)`);
        }
      }
    }

    return instructions;
  }

  /**
   * Gets a fallback configuration for a server when dependencies are missing
   */
  static async getFallbackConfiguration(
    serverName: string,
    missingRequirements: string[]
  ): Promise<MCPServerConfig | null> {
    // Special handling for Git server - provide alternative configurations when primary fails
    if (serverName === 'Git') {
      // Check if npx is available as fallback
      try {
        const npxCheck = await this.checkCommandAvailability('npx');
        if (npxCheck.available) {
          // Try multiple Git server options in order of preference
          const gitServerOptions = [
            {
              package: '@cyanheads/git-mcp-server',
              args: ['-y', '@cyanheads/git-mcp-server', '--repository', '.'],
              description: 'Git MCP server for version control operations (cyanheads)'
            },
            {
              package: '@modelcontextprotocol/server-git',
              args: ['-y', '@modelcontextprotocol/server-git', '--repository', '.'],
              description: 'Git MCP server for version control operations (official)'
            }
          ];

          // If uvx is missing, try npx-based alternatives
          if (missingRequirements.some(req => req.includes('uvx'))) {
            // Return the first available npx option
            for (const option of gitServerOptions) {
              return new MCPServerConfig(
                'npx',
                option.args,
                undefined,
                undefined, // Let npx handle working directory automatically
                undefined,
                undefined,
                undefined,
                15000, // Longer timeout for package installation and startup
                undefined,
                option.description
              );
            }
          } else {
            // If uvx is available but the primary package failed, try uvx with alternative packages
            try {
              const uvxCheck = await this.checkCommandAvailability('uvx');
              if (uvxCheck.available) {
                return new MCPServerConfig(
                  'uvx',
                  ['mcp-server-git', '--repository', '.'],
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  15000,
                  undefined,
                  'Git MCP server for version control operations (official uvx)'
                );
              }
            } catch (uvxError) {
              console.debug(`uvx check failed, falling back to npx: ${uvxError}`);
            }

            // Fallback to npx if uvx fails
            return new MCPServerConfig(
              'npx',
              gitServerOptions[0].args,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              15000,
              undefined,
              gitServerOptions[0].description
            );
          }
        }
      } catch (error) {
        console.debug(`Failed to check npx availability for Git fallback: ${error}`);
      }
    }

    // Fallback for servers that require uvx but it's not available
    if (missingRequirements.some(req => req.includes('uvx'))) {
      try {
        const npxCheck = await this.checkCommandAvailability('npx');
        if (npxCheck.available) {
          // Provide generic npx-based fallbacks for uvx servers
          const fallbackMappings: Record<string, { args: string[]; description: string }> = {
            'MongoDB': {
              args: ['-y', '@modelcontextprotocol/server-mongodb'],
              description: 'MongoDB MCP server (npx fallback)'
            },
            'PostgreSQL': {
              args: ['-y', '@modelcontextprotocol/server-postgres'],
              description: 'PostgreSQL MCP server (npx fallback)'
            }
          };

          const fallback = fallbackMappings[serverName];
          if (fallback) {
            return new MCPServerConfig(
              'npx',
              fallback.args,
              undefined,
              process.cwd(),
              undefined,
              undefined,
              undefined,
              10000,
              undefined,
              fallback.description
            );
          }
        }
      } catch (error) {
        console.debug(`Failed to check npx availability for ${serverName} fallback: ${error}`);
      }
    }

    // Add more fallback configurations for other servers as needed
    return null;
  }

  /**
   * Checks if a command is available in the system
   */
  private static async checkCommandAvailability(command: string): Promise<{ available: boolean; version?: string }> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
      return {
        available: true,
        version: stdout.trim(),
      };
    } catch (error) {
      return {
        available: false,
      };
    }
  }

  /**
   * Validates and filters servers based on system capabilities
   */
  static async getCompatibleServers(): Promise<Record<string, MCPServerConfig>> {
    const compatibleServers: Record<string, MCPServerConfig> = {};

    // Check if debug mode is enabled via command line arguments
    const isDebugMode = process.argv.includes('--debug') || process.argv.includes('-d');

    // Temporarily disable the Filesystem server due to connection issues
    // The built-in file tools provide equivalent functionality
    const temporarilyDisabledServers = ['Filesystem'];

    for (const [name, entry] of Object.entries(this.VERIFIED_SERVERS)) {
      if (temporarilyDisabledServers.includes(name)) {
        if (isDebugMode) {
          console.debug(`⚠️ MCP server '${name}' temporarily disabled due to known connection issues`);
        }
        continue;
      }

      try {
        const requirements = await this.checkServerRequirements(name);
        if (requirements.meetsRequirements) {
          compatibleServers[name] = entry.config;
          if (isDebugMode) {
            console.debug(`✅ MCP server '${name}' is compatible`);
          }
        } else {
          // Check if we have a fallback configuration for this server
          const fallbackConfig = await this.getFallbackConfiguration(name, requirements.missingRequirements);
          if (fallbackConfig) {
            compatibleServers[name] = fallbackConfig;
            if (isDebugMode) {
              console.debug(`✅ MCP server '${name}' using fallback configuration`);
            }
          } else {
            if (isDebugMode) {
              console.warn(`❌ MCP server '${name}' disabled due to missing requirements: ${requirements.missingRequirements.join(', ')}`);

              // Attempt automatic installation of missing dependencies
              const missingCommands = requirements.missingRequirements
                .filter(req => req.includes('Command'))
                .map(req => req.match(/'([^']+)'/)?.[1])
                .filter(Boolean) as string[];

              if (missingCommands.length > 0) {
                console.warn(`   Attempting automatic installation of missing dependencies...`);
                try {
                  const installResults = await DependencyInstaller.installMultipleDependencies(missingCommands, false);
                  DependencyInstaller.summarizeInstallationResults(installResults);

                  // Re-check requirements after installation
                  const recheckRequirements = await this.checkServerRequirements(name);
                  if (recheckRequirements.meetsRequirements) {
                    compatibleServers[name] = entry.config;
                    console.log(`✅ MCP server '${name}' enabled after dependency installation`);
                    continue;
                  }
                } catch (installError) {
                  console.warn(`   Automatic installation failed: ${installError}`);
                }
              }

              console.warn(`   To enable this server manually, install the missing dependencies:`);
              const instructions = this.getInstallationInstructions(name);
              instructions.forEach(instruction => console.warn(`   - ${instruction}`));
            }
          }
        }
      } catch (error) {
        if (isDebugMode) {
          console.warn(`❌ MCP server '${name}' validation failed: ${error}`);
        }
      }
    }

    return compatibleServers;
  }
}
