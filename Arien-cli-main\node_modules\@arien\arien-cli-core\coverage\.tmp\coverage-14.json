{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-client.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70942, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 70942, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 900, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1017, "endOffset": 1359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1105, "endOffset": 1298, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1435, "endOffset": 1685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1521, "endOffset": 1628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1740, "endOffset": 1827, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3204, "endOffset": 20478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3293, "endOffset": 5190, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4049, "endOffset": 4071, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4648, "endOffset": 4851, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5071, "endOffset": 5184, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5231, "endOffset": 5290, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5389, "endOffset": 5921, "count": 1}, {"startOffset": 5480, "endOffset": 5485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6001, "endOffset": 7788, "count": 1}, {"startOffset": 6622, "endOffset": 6627, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7877, "endOffset": 9221, "count": 1}, {"startOffset": 8550, "endOffset": 8555, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9308, "endOffset": 10477, "count": 1}, {"startOffset": 9953, "endOffset": 9958, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10578, "endOffset": 13829, "count": 1}, {"startOffset": 12539, "endOffset": 12544, "count": 0}, {"startOffset": 13478, "endOffset": 13484, "count": 0}, {"startOffset": 13527, "endOffset": 13533, "count": 0}, {"startOffset": 13547, "endOffset": 13825, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11563, "endOffset": 11617, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11678, "endOffset": 11986, "count": 3}, {"startOffset": 11825, "endOffset": 11869, "count": 1}, {"startOffset": 11870, "endOffset": 11904, "count": 1}, {"startOffset": 11906, "endOffset": 11980, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12053, "endOffset": 12454, "count": 2}, {"startOffset": 12119, "endOffset": 12427, "count": 1}, {"startOffset": 12427, "endOffset": 12453, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12789, "endOffset": 12806, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12872, "endOffset": 12937, "count": 3}, {"startOffset": 12908, "endOffset": 12937, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13003, "endOffset": 13068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13134, "endOffset": 13199, "count": 3}, {"startOffset": 13170, "endOffset": 13199, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13929, "endOffset": 15926, "count": 1}, {"startOffset": 14940, "endOffset": 14945, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16017, "endOffset": 16797, "count": 1}, {"startOffset": 16503, "endOffset": 16508, "count": 0}, {"startOffset": 16625, "endOffset": 16796, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16239, "endOffset": 16293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16368, "endOffset": 16381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16914, "endOffset": 17528, "count": 1}, {"startOffset": 17161, "endOffset": 17166, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17068, "endOffset": 17081, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17625, "endOffset": 18595, "count": 1}, {"startOffset": 18103, "endOffset": 18108, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18010, "endOffset": 18023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18694, "endOffset": 19556, "count": 1}, {"startOffset": 19165, "endOffset": 19170, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19072, "endOffset": 19085, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19633, "endOffset": 20474, "count": 1}, {"startOffset": 20013, "endOffset": 20018, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20538, "endOffset": 23092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20622, "endOffset": 20708, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20789, "endOffset": 21062, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21142, "endOffset": 21483, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21563, "endOffset": 21830, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21915, "endOffset": 22229, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22303, "endOffset": 23088, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 11}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1216, "endOffset": 1268, "count": 6}, {"startOffset": 1258, "endOffset": 1266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2369, "endOffset": 2570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2637, "endOffset": 2848, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 3028, "endOffset": 3117, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 3119, "endOffset": 3298, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 3300, "endOffset": 3495, "count": 23}, {"startOffset": 3454, "endOffset": 3493, "count": 0}], "isBlockCoverage": true}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 3496, "endOffset": 3628, "count": 1}, {"startOffset": 3589, "endOffset": 3606, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 3630, "endOffset": 3713, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 3715, "endOffset": 3778, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 3780, "endOffset": 5751, "count": 11}, {"startOffset": 3947, "endOffset": 4286, "count": 2}, {"startOffset": 4110, "endOffset": 4188, "count": 0}, {"startOffset": 4188, "endOffset": 4286, "count": 1}, {"startOffset": 4286, "endOffset": 5067, "count": 10}, {"startOffset": 5067, "endOffset": 5373, "count": 9}, {"startOffset": 5206, "endOffset": 5367, "count": 0}, {"startOffset": 5373, "endOffset": 5541, "count": 1}, {"startOffset": 5401, "endOffset": 5541, "count": 0}, {"startOffset": 5541, "endOffset": 5600, "count": 10}, {"startOffset": 5600, "endOffset": 5749, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4075, "endOffset": 4107, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4356, "endOffset": 4458, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4633, "endOffset": 5038, "count": 10}, {"startOffset": 4863, "endOffset": 5032, "count": 0}], "isBlockCoverage": true}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 5753, "endOffset": 14983, "count": 10}, {"startOffset": 5954, "endOffset": 6078, "count": 0}, {"startOffset": 6109, "endOffset": 6206, "count": 2}, {"startOffset": 6206, "endOffset": 6815, "count": 8}, {"startOffset": 6241, "endOffset": 6535, "count": 7}, {"startOffset": 6381, "endOffset": 6386, "count": 5}, {"startOffset": 6535, "endOffset": 6815, "count": 1}, {"startOffset": 6815, "endOffset": 6962, "count": 9}, {"startOffset": 6962, "endOffset": 7251, "count": 0}, {"startOffset": 7251, "endOffset": 7284, "count": 9}, {"startOffset": 7284, "endOffset": 7351, "count": 7}, {"startOffset": 7351, "endOffset": 7468, "count": 9}, {"startOffset": 7476, "endOffset": 7552, "count": 8}, {"startOffset": 7552, "endOffset": 11476, "count": 1}, {"startOffset": 7648, "endOffset": 8792, "count": 0}, {"startOffset": 9470, "endOffset": 10093, "count": 0}, {"startOffset": 10188, "endOffset": 10556, "count": 0}, {"startOffset": 10600, "endOffset": 10783, "count": 0}, {"startOffset": 10986, "endOffset": 11093, "count": 0}, {"startOffset": 11476, "endOffset": 11735, "count": 8}, {"startOffset": 11735, "endOffset": 11754, "count": 6}, {"startOffset": 11756, "endOffset": 11973, "count": 6}, {"startOffset": 11973, "endOffset": 12124, "count": 8}, {"startOffset": 12124, "endOffset": 12221, "count": 7}, {"startOffset": 12223, "endOffset": 12712, "count": 0}, {"startOffset": 12712, "endOffset": 14003, "count": 7}, {"startOffset": 12815, "endOffset": 12982, "count": 0}, {"startOffset": 13195, "endOffset": 13272, "count": 1}, {"startOffset": 13314, "endOffset": 13419, "count": 0}, {"startOffset": 13590, "endOffset": 13626, "count": 0}, {"startOffset": 13825, "endOffset": 13830, "count": 0}, {"startOffset": 14003, "endOffset": 14008, "count": 7}, {"startOffset": 14008, "endOffset": 14469, "count": 1}, {"startOffset": 14207, "endOffset": 14271, "count": 0}, {"startOffset": 14272, "endOffset": 14347, "count": 0}, {"startOffset": 14469, "endOffset": 14536, "count": 8}, {"startOffset": 14536, "endOffset": 14981, "count": 2}, {"startOffset": 14718, "endOffset": 14782, "count": 0}, {"startOffset": 14783, "endOffset": 14858, "count": 0}], "isBlockCoverage": true}, {"functionName": "mcpClient.callTool", "ranges": [{"startOffset": 7050, "endOffset": 7246, "count": 0}], "isBlockCoverage": false}, {"functionName": "mcpClient.connect.timeout", "ranges": [{"startOffset": 7308, "endOffset": 7345, "count": 7}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_1__.StdioClientTransport.command", "ranges": [{"startOffset": 7781, "endOffset": 7818, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11268, "endOffset": 11328, "count": 2}], "isBlockCoverage": true}, {"functionName": "mcpClient.onerror", "ranges": [{"startOffset": 11500, "endOffset": 11663, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11790, "endOffset": 11967, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 14984, "endOffset": 15383, "count": 32}, {"startOffset": 15037, "endOffset": 15054, "count": 1}, {"startOffset": 15054, "endOffset": 15076, "count": 31}, {"startOffset": 15076, "endOffset": 15187, "count": 8}, {"startOffset": 15144, "endOffset": 15183, "count": 10}, {"startOffset": 15187, "endOffset": 15209, "count": 31}, {"startOffset": 15209, "endOffset": 15252, "count": 2}, {"startOffset": 15252, "endOffset": 15279, "count": 31}, {"startOffset": 15279, "endOffset": 15381, "count": 11}, {"startOffset": 15338, "endOffset": 15377, "count": 7}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "344", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/mcp-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 14}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "DiscoveredMCPTool", "ranges": [{"startOffset": 629, "endOffset": 1194, "count": 7}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1197, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 1245, "endOffset": 2342, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 2345, "endOffset": 2669, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 2673, "endOffset": 3597, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "345", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 7}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}