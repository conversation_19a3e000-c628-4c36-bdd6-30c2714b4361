{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 893, "endOffset": 7755, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 997, "endOffset": 1246, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1287, "endOffset": 1346, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1404, "endOffset": 4097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1507, "endOffset": 1610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1554, "endOffset": 1588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1698, "endOffset": 2443, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2513, "endOffset": 3116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2608, "endOffset": 2803, "count": 2}, {"startOffset": 2715, "endOffset": 2759, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3195, "endOffset": 3881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3385, "endOffset": 3569, "count": 1}, {"startOffset": 3481, "endOffset": 3525, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3981, "endOffset": 4091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4028, "endOffset": 4069, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4150, "endOffset": 7283, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4199, "endOffset": 4452, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4527, "endOffset": 4841, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4918, "endOffset": 5447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5534, "endOffset": 5720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5791, "endOffset": 5895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5985, "endOffset": 6167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6245, "endOffset": 6430, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6531, "endOffset": 6716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6794, "endOffset": 7001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7084, "endOffset": 7277, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7345, "endOffset": 7751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7426, "endOffset": 7745, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 15}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 15}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 15}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 13}, {"startOffset": 1040, "endOffset": 1047, "count": 0}, {"startOffset": 1211, "endOffset": 1247, "count": 26}], "isBlockCoverage": true}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 28}, {"startOffset": 1492, "endOffset": 1528, "count": 3}, {"startOffset": 1528, "endOffset": 1560, "count": 25}, {"startOffset": 1560, "endOffset": 1565, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 95}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 95}, {"startOffset": 1623, "endOffset": 1644, "count": 61}], "isBlockCoverage": true}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 38}], "isBlockCoverage": true}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 24}, {"startOffset": 1869, "endOffset": 1929, "count": 3}, {"startOffset": 1930, "endOffset": 1940, "count": 21}, {"startOffset": 2004, "endOffset": 2031, "count": 2}, {"startOffset": 2031, "endOffset": 2133, "count": 22}, {"startOffset": 2133, "endOffset": 2192, "count": 0}, {"startOffset": 2192, "endOffset": 2240, "count": 22}], "isBlockCoverage": true}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 4}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}