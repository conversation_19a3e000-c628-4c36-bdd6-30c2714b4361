{"version": "3.2.4", "results": [[":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 481.89599999999973, "failed": true}], [":src/ui/components/shared/text-buffer.test.ts", {"duration": 759.4279000000006, "failed": false}], [":src/ui/hooks/useArienStream.test.tsx", {"duration": 572.2384000000002, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 303.9946, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 90.83819999999923, "failed": true}], [":src/config/settings.test.ts", {"duration": 80.14529999999922, "failed": false}], [":src/config/config.test.ts", {"duration": 47734.9041, "failed": true}], [":src/ui/App.test.tsx", {"duration": 1899.4718000000012, "failed": true}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1700.2229000000007, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 539.4844000000003, "failed": false}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 153.52369999999974, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 240.3732, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 32.71510000000035, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 223.03179999999975, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 97.80009999999993, "failed": true}], [":src/config/mcp-integration.test.ts", {"duration": 18326.538099999998, "failed": true}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 104.32739999999922, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 145.59299999999985, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 155.1230999999998, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 115.1590000000001, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 63.94260000000031, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 931.3626999999997, "failed": false}], [":src/config/built-in-mcp-servers.test.ts", {"duration": 55.85080000000016, "failed": true}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 140.77059999999983, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 955.9393, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 127.92050000000017, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 92.07830000000013, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 97.97340000000031, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 5.8870999999999185, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 99.48279999999977, "failed": false}], [":src/arien.test.tsx", {"duration": 7.484299999999166, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 92.63230000000021, "failed": false}], [":src/config/extension.test.ts", {"duration": 39.06179999999995, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 424.4892, "failed": false}], [":src/ui/components/messages/ArienMessage.test.tsx", {"duration": 85.20619999999963, "failed": true}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 264.7957999999999, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/components/Stats.test.tsx", {"duration": 159.3687, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 12.15560000000005, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 9.443099999999959, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 151.89570000000003, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 93.88760000000002, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 154.02189999999973, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 8.654199999999946, "failed": false}]]}