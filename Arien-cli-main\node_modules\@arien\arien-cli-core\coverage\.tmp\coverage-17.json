{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 14}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 14}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 14}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/retry.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41768, "count": 1}], "isBlockCoverage": true}, {"functionName": "createFailingFunction", "ranges": [{"startOffset": 715, "endOffset": 1030, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 814, "endOffset": 1026, "count": 15}, {"startOffset": 874, "endOffset": 996, "count": 12}, {"startOffset": 996, "endOffset": 1025, "count": 3}], "isBlockCoverage": true}, {"functionName": "NonRetryableError", "ranges": [{"startOffset": 1074, "endOffset": 1157, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1215, "endOffset": 13050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1262, "endOffset": 1422, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1463, "endOffset": 1568, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1665, "endOffset": 1937, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2034, "endOffset": 2447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2527, "endOffset": 3001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3084, "endOffset": 3590, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3145, "endOffset": 3222, "count": 1}], "isBlockCoverage": true}, {"functionName": "shouldRetry", "ranges": [{"startOffset": 3249, "endOffset": 3297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3691, "endOffset": 4266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3752, "endOffset": 3868, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4371, "endOffset": 4831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4432, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4894, "endOffset": 5941, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5368, "endOffset": 5385, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6033, "endOffset": 7491, "count": 1}, {"startOffset": 7188, "endOffset": 7268, "count": 0}], "isBlockCoverage": true}, {"functionName": "runRetry", "ranges": [{"startOffset": 6191, "endOffset": 6365, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6639, "endOffset": 6656, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7007, "endOffset": 7024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7320, "endOffset": 7485, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7571, "endOffset": 13046, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7692, "endOffset": 8747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7912, "endOffset": 8114, "count": 3}, {"startOffset": 7957, "endOffset": 8079, "count": 2}, {"startOffset": 8079, "endOffset": 8113, "count": 1}], "isBlockCoverage": true}, {"functionName": "onPersistent429", "ranges": [{"startOffset": 8270, "endOffset": 8387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8832, "endOffset": 9703, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8957, "endOffset": 9083, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9344, "endOffset": 9360, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9810, "endOffset": 10741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9938, "endOffset": 10025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10098, "endOffset": 10298, "count": 3}, {"startOffset": 10141, "endOffset": 10263, "count": 2}, {"startOffset": 10263, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10840, "endOffset": 11751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10989, "endOffset": 11115, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11376, "endOffset": 11392, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11852, "endOffset": 13040, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12096, "endOffset": 12480, "count": 4}, {"startOffset": 12160, "endOffset": 12199, "count": 1}, {"startOffset": 12199, "endOffset": 12229, "count": 3}, {"startOffset": 12229, "endOffset": 12344, "count": 1}, {"startOffset": 12344, "endOffset": 12472, "count": 2}], "isBlockCoverage": true}, {"functionName": "onPersistent429", "ranges": [{"startOffset": 12636, "endOffset": 12753, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/retry.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 14}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 662, "endOffset": 1047, "count": 19}, {"startOffset": 806, "endOffset": 838, "count": 11}, {"startOffset": 823, "endOffset": 838, "count": 10}, {"startOffset": 840, "endOffset": 866, "count": 18}, {"startOffset": 870, "endOffset": 917, "count": 1}, {"startOffset": 919, "endOffset": 1028, "count": 1}, {"startOffset": 960, "endOffset": 972, "count": 0}, {"startOffset": 1012, "endOffset": 1024, "count": 0}, {"startOffset": 1028, "endOffset": 1046, "count": 1}], "isBlockCoverage": true}, {"functionName": "delay", "ranges": [{"startOffset": 1048, "endOffset": 1130, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1090, "endOffset": 1126, "count": 18}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1131, "endOffset": 3083, "count": 14}, {"startOffset": 1469, "endOffset": 3033, "count": 35}, {"startOffset": 1527, "endOffset": 3029, "count": 29}, {"startOffset": 1623, "endOffset": 1663, "count": 14}, {"startOffset": 1663, "endOffset": 1711, "count": 15}, {"startOffset": 1748, "endOffset": 1766, "count": 8}, {"startOffset": 1767, "endOffset": 1840, "count": 7}, {"startOffset": 1842, "endOffset": 2218, "count": 5}, {"startOffset": 1952, "endOffset": 2092, "count": 3}, {"startOffset": 2103, "endOffset": 2210, "count": 0}, {"startOffset": 2218, "endOffset": 2253, "count": 26}, {"startOffset": 2253, "endOffset": 2275, "count": 20}, {"startOffset": 2277, "endOffset": 2307, "count": 8}, {"startOffset": 2307, "endOffset": 2439, "count": 18}, {"startOffset": 2439, "endOffset": 2716, "count": 0}, {"startOffset": 2716, "endOffset": 3023, "count": 18}, {"startOffset": 3033, "endOffset": 3082, "count": 0}], "isBlockCoverage": true}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 3085, "endOffset": 3547, "count": 47}, {"startOffset": 3197, "endOffset": 3232, "count": 46}, {"startOffset": 3234, "endOffset": 3268, "count": 46}, {"startOffset": 3268, "endOffset": 3298, "count": 1}, {"startOffset": 3298, "endOffset": 3335, "count": 0}, {"startOffset": 3336, "endOffset": 3362, "count": 0}, {"startOffset": 3364, "endOffset": 3523, "count": 0}, {"startOffset": 3527, "endOffset": 3546, "count": 1}], "isBlockCoverage": true}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 3548, "endOffset": 4434, "count": 8}, {"startOffset": 3668, "endOffset": 3705, "count": 0}, {"startOffset": 3706, "endOffset": 3732, "count": 0}, {"startOffset": 3734, "endOffset": 4415, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 4435, "endOffset": 4680, "count": 18}, {"startOffset": 4578, "endOffset": 4634, "count": 8}], "isBlockCoverage": true}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 4681, "endOffset": 5599, "count": 18}, {"startOffset": 4957, "endOffset": 4996, "count": 8}, {"startOffset": 4996, "endOffset": 5597, "count": 10}, {"startOffset": 5102, "endOffset": 5597, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/contentGenerator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 7}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1150, "endOffset": 1338, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1357, "endOffset": 2722, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 2724, "endOffset": 3591, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "514", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/codeAssist.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 923, "endOffset": 1379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "515", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/oauth2.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 2061, "endOffset": 2667, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 2669, "endOffset": 4557, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 4558, "endOffset": 5065, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 5067, "endOffset": 5536, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 5537, "endOffset": 5878, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 5879, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 6028, "endOffset": 6174, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "530", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 785, "endOffset": 954, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 958, "endOffset": 2044, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2046, "endOffset": 2384, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "531", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 650, "endOffset": 1264, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1333, "endOffset": 1498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1564, "endOffset": 1818, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "532", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1073, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1218, "endOffset": 1612, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1615, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 1905, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2010, "endOffset": 2118, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2121, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2249, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2392, "endOffset": 2614, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2617, "endOffset": 2666, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2669, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3065, "endOffset": 3418, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3421, "endOffset": 4521, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "537", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 863, "endOffset": 1014, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1016, "endOffset": 1105, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1107, "endOffset": 1258, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1260, "endOffset": 1620, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1622, "endOffset": 2051, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2052, "endOffset": 2191, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2192, "endOffset": 2299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2300, "endOffset": 2661, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 2662, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 2718, "endOffset": 2824, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 2825, "endOffset": 3738, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "538", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/config/models.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "539", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/modelCheck.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 550, "endOffset": 1915, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}