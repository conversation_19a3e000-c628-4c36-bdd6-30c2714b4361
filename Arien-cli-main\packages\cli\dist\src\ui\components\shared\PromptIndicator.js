import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { useEffect, useState } from 'react';
import { Text } from 'ink';
import { Colors } from '../../colors.js';
const indicators = {
    default: ['>', '→', '⟶', '→', '>'],
    shell: ['!', '‼', '⁉', '‼', '!'],
    completion: ['.', '..', '...', '..', '.'],
    waiting: ['⊙', '◉', '◎', '◉', '⊙'],
};
export const PromptIndicator = ({ type = 'default', animated = true, color, }) => {
    const [frame, setFrame] = useState(0);
    const sequence = indicators[type];
    useEffect(() => {
        if (!animated)
            return;
        const interval = setInterval(() => {
            setFrame((prev) => (prev + 1) % sequence.length);
        }, 300);
        return () => clearInterval(interval);
    }, [animated, sequence.length]);
    const indicator = animated ? sequence[frame] : sequence[0];
    const displayColor = color || (type === 'shell' ? Colors.AccentYellow : Colors.AccentPurple);
    return _jsxs(Text, { color: displayColor, bold: true, children: [indicator, " "] });
};
export const TypewriterText = ({ text, speed = 50, color = Colors.Foreground, onComplete, }) => {
    const [displayedText, setDisplayedText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    useEffect(() => {
        if (currentIndex >= text.length) {
            onComplete?.();
            return;
        }
        const timeout = setTimeout(() => {
            setDisplayedText(text.slice(0, currentIndex + 1));
            setCurrentIndex(currentIndex + 1);
        }, speed);
        return () => clearTimeout(timeout);
    }, [currentIndex, text, speed, onComplete]);
    return _jsx(Text, { color: color, children: displayedText });
};
//# sourceMappingURL=PromptIndicator.js.map