{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ToolMessage.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 455, "endOffset": 1397, "count": 1}], "isBlockCoverage": true}, {"functionName": "ArienRespondingSpinner", "ranges": [{"startOffset": 490, "endOffset": 1394, "count": 3}, {"startOffset": 695, "endOffset": 1037, "count": 1}, {"startOffset": 1037, "endOffset": 1382, "count": 2}, {"startOffset": 1383, "endOffset": 1389, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1451, "endOffset": 1878, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1476, "endOffset": 1875, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1945, "endOffset": 2362, "count": 1}], "isBlockCoverage": true}, {"functionName": "MockMarkdownDisplay", "ranges": [{"startOffset": 1973, "endOffset": 2359, "count": 14}], "isBlockCoverage": true}, {"functionName": "renderWithContext", "ranges": [{"startOffset": 3417, "endOffset": 3858, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3888, "endOffset": 15825, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4213, "endOffset": 4851, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4898, "endOffset": 10266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4943, "endOffset": 5469, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5509, "endOffset": 6035, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6078, "endOffset": 6667, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6708, "endOffset": 7287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7325, "endOffset": 7899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7984, "endOffset": 8670, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8773, "endOffset": 9477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9573, "endOffset": 10260, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10315, "endOffset": 10957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10995, "endOffset": 11993, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12046, "endOffset": 12762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12804, "endOffset": 13423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13478, "endOffset": 14185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14225, "endOffset": 14788, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14833, "endOffset": 15821, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ToolMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 18}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolMessage", "ranges": [{"startOffset": 3148, "endOffset": 10145, "count": 18}, {"startOffset": 8647, "endOffset": 8938, "count": 1}, {"startOffset": 9183, "endOffset": 9936, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3373, "endOffset": 3561, "count": 18}, {"startOffset": 3403, "endOffset": 3552, "count": 1}, {"startOffset": 3553, "endOffset": 3561, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3670, "endOffset": 3724, "count": 18}, {"startOffset": 3692, "endOffset": 3699, "count": 1}, {"startOffset": 3700, "endOffset": 3724, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3837, "endOffset": 3904, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3998, "endOffset": 4268, "count": 18}, {"startOffset": 4049, "endOffset": 4084, "count": 2}, {"startOffset": 4084, "endOffset": 4152, "count": 16}, {"startOffset": 4152, "endOffset": 4237, "count": 0}, {"startOffset": 4237, "endOffset": 4267, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4356, "endOffset": 7145, "count": 16}, {"startOffset": 4397, "endOffset": 4409, "count": 0}, {"startOffset": 4462, "endOffset": 6535, "count": 15}, {"startOffset": 4498, "endOffset": 5442, "count": 14}, {"startOffset": 5442, "endOffset": 6529, "count": 1}, {"startOffset": 6535, "endOffset": 7141, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10210, "endOffset": 11883, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10295, "endOffset": 11481, "count": 18}, {"startOffset": 10454, "endOffset": 10939, "count": 3}, {"startOffset": 10939, "endOffset": 11016, "count": 15}, {"startOffset": 11016, "endOffset": 11072, "count": 4}, {"startOffset": 11073, "endOffset": 11132, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11995, "endOffset": 14732, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12104, "endOffset": 12466, "count": 18}, {"startOffset": 12142, "endOffset": 12212, "count": 1}, {"startOffset": 12219, "endOffset": 12289, "count": 16}, {"startOffset": 12296, "endOffset": 12357, "count": 1}, {"startOffset": 12364, "endOffset": 12456, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12545, "endOffset": 12970, "count": 18}, {"startOffset": 12581, "endOffset": 12656, "count": 3}, {"startOffset": 12663, "endOffset": 12739, "count": 1}, {"startOffset": 12746, "endOffset": 12827, "count": 1}, {"startOffset": 12834, "endOffset": 12926, "count": 1}, {"startOffset": 12933, "endOffset": 12960, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14831, "endOffset": 15496, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 21}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 139}, {"startOffset": 475, "endOffset": 483, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 43}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 17}], "isBlockCoverage": true}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 2}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 2}], "isBlockCoverage": true}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 19}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 43}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 43}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 1}, {"startOffset": 495, "endOffset": 503, "count": 0}], "isBlockCoverage": true}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1813, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1884, "endOffset": 2611, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2632, "endOffset": 7251, "count": 1}, {"startOffset": 2989, "endOffset": 3015, "count": 0}, {"startOffset": 3073, "endOffset": 3150, "count": 0}, {"startOffset": 3931, "endOffset": 3960, "count": 0}, {"startOffset": 3961, "endOffset": 3982, "count": 0}, {"startOffset": 4124, "endOffset": 4127, "count": 0}, {"startOffset": 4541, "endOffset": 4691, "count": 0}, {"startOffset": 5933, "endOffset": 5963, "count": 0}, {"startOffset": 5964, "endOffset": 6465, "count": 0}, {"startOffset": 6510, "endOffset": 6543, "count": 0}, {"startOffset": 6544, "endOffset": 7044, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRows", "ranges": [{"startOffset": 3154, "endOffset": 3670, "count": 1}, {"startOffset": 3248, "endOffset": 3269, "count": 0}, {"startOffset": 3328, "endOffset": 3438, "count": 0}, {"startOffset": 3587, "endOffset": 3669, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4241, "endOffset": 4426, "count": 1}, {"startOffset": 4279, "endOffset": 4316, "count": 0}, {"startOffset": 4349, "endOffset": 4355, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4375, "endOffset": 4421, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4758, "endOffset": 5745, "count": 1}, {"startOffset": 5246, "endOffset": 5545, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4894, "endOffset": 5244, "count": 1}], "isBlockCoverage": true}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 7253, "endOffset": 10048, "count": 1}, {"startOffset": 7393, "endOffset": 7596, "count": 0}, {"startOffset": 7620, "endOffset": 8168, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRowChild", "ranges": [{"startOffset": 8264, "endOffset": 9905, "count": 2}, {"startOffset": 8339, "endOffset": 8360, "count": 0}, {"startOffset": 8399, "endOffset": 8430, "count": 1}, {"startOffset": 8432, "endOffset": 9058, "count": 1}, {"startOffset": 8488, "endOffset": 8513, "count": 0}, {"startOffset": 8564, "endOffset": 8569, "count": 0}, {"startOffset": 8714, "endOffset": 9037, "count": 0}, {"startOffset": 9058, "endOffset": 9125, "count": 1}, {"startOffset": 9125, "endOffset": 9200, "count": 0}, {"startOffset": 9200, "endOffset": 9260, "count": 1}, {"startOffset": 9260, "endOffset": 9430, "count": 0}, {"startOffset": 9430, "endOffset": 9486, "count": 1}, {"startOffset": 9486, "endOffset": 9615, "count": 0}, {"startOffset": 9615, "endOffset": 9736, "count": 1}, {"startOffset": 9737, "endOffset": 9774, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9357, "endOffset": 9401, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9850, "endOffset": 9894, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9988, "endOffset": 10027, "count": 1}], "isBlockCoverage": true}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 10049, "endOffset": 14127, "count": 1}, {"startOffset": 10184, "endOffset": 10218, "count": 0}, {"startOffset": 10220, "endOffset": 10258, "count": 0}, {"startOffset": 10533, "endOffset": 11190, "count": 0}, {"startOffset": 11271, "endOffset": 11325, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10369, "endOffset": 10497, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10615, "endOffset": 10928, "count": 0}], "isBlockCoverage": false}, {"functionName": "addWrappingPartToLines", "ranges": [{"startOffset": 11383, "endOffset": 11796, "count": 1}, {"startOffset": 11514, "endOffset": 11741, "count": 0}], "isBlockCoverage": true}, {"functionName": "addToWrappingPart", "ranges": [{"startOffset": 11799, "endOffset": 12055, "count": 3}, {"startOffset": 11873, "endOffset": 11929, "count": 2}, {"startOffset": 11931, "endOffset": 11996, "count": 2}, {"startOffset": 11996, "endOffset": 12051, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12079, "endOffset": 13998, "count": 1}, {"startOffset": 13955, "endOffset": 13994, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12178, "endOffset": 13915, "count": 1}, {"startOffset": 12230, "endOffset": 12273, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12340, "endOffset": 13907, "count": 3}, {"startOffset": 12371, "endOffset": 12378, "count": 0}, {"startOffset": 12505, "endOffset": 12529, "count": 0}, {"startOffset": 12531, "endOffset": 12646, "count": 0}, {"startOffset": 12688, "endOffset": 13790, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1578", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1579", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1212, "endOffset": 1276, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1305, "endOffset": 1371, "count": 1}], "isBlockCoverage": true}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1398, "endOffset": 3012, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1580", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/StreamingContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 21}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "useStreamingContext", "ranges": [{"startOffset": 802, "endOffset": 1043, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}