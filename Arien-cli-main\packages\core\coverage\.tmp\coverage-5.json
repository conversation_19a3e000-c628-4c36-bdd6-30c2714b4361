{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/bfsFileSearch.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "createMockDirent", "ranges": [{"startOffset": 1004, "endOffset": 1181, "count": 10}], "isBlockCoverage": true}, {"functionName": "dirent.isFile", "ranges": [{"startOffset": 1111, "endOffset": 1123, "count": 3}], "isBlockCoverage": true}, {"functionName": "dirent.isDirectory", "ranges": [{"startOffset": 1148, "endOffset": 1161, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1235, "endOffset": 5507, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1282, "endOffset": 1339, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1417, "endOffset": 1872, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1946, "endOffset": 2545, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2137, "endOffset": 2365, "count": 2}, {"startOffset": 2181, "endOffset": 2278, "count": 1}, {"startOffset": 2278, "endOffset": 2341, "count": 0}, {"startOffset": 2341, "endOffset": 2364, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2618, "endOffset": 3427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2809, "endOffset": 3205, "count": 2}, {"startOffset": 2853, "endOffset": 3018, "count": 1}, {"startOffset": 3018, "endOffset": 3081, "count": 0}, {"startOffset": 3081, "endOffset": 3118, "count": 1}, {"startOffset": 3118, "endOffset": 3181, "count": 0}, {"startOffset": 3181, "endOffset": 3204, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3493, "endOffset": 4289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3684, "endOffset": 4080, "count": 2}, {"startOffset": 3728, "endOffset": 3893, "count": 1}, {"startOffset": 3893, "endOffset": 3956, "count": 0}, {"startOffset": 3956, "endOffset": 3993, "count": 1}, {"startOffset": 3993, "endOffset": 4056, "count": 0}, {"startOffset": 4056, "endOffset": 4079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4358, "endOffset": 5503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4680, "endOffset": 5124, "count": 2}, {"startOffset": 4724, "endOffset": 4937, "count": 1}, {"startOffset": 4937, "endOffset": 5000, "count": 0}, {"startOffset": 5000, "endOffset": 5037, "count": 1}, {"startOffset": 5037, "endOffset": 5100, "count": 0}, {"startOffset": 5100, "endOffset": 5123, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/bfsFileSearch.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 5}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 618, "endOffset": 680, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 684, "endOffset": 1893, "count": 5}, {"startOffset": 1002, "endOffset": 1030, "count": 10}, {"startOffset": 1032, "endOffset": 1869, "count": 9}, {"startOffset": 1105, "endOffset": 1128, "count": 0}, {"startOffset": 1197, "endOffset": 1283, "count": 0}, {"startOffset": 1408, "endOffset": 1437, "count": 0}, {"startOffset": 1472, "endOffset": 1865, "count": 10}, {"startOffset": 1570, "endOffset": 1591, "count": 3}, {"startOffset": 1603, "endOffset": 1630, "count": 1}, {"startOffset": 1630, "endOffset": 1663, "count": 9}, {"startOffset": 1663, "endOffset": 1762, "count": 6}, {"startOffset": 1711, "endOffset": 1754, "count": 5}, {"startOffset": 1762, "endOffset": 1859, "count": 3}, {"startOffset": 1815, "endOffset": 1859, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "322", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 1}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 1}, {"startOffset": 1173, "endOffset": 1197, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 3}, {"startOffset": 2113, "endOffset": 2135, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "323", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 2}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 2}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 2}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 1}, {"startOffset": 1040, "endOffset": 1047, "count": 0}, {"startOffset": 1211, "endOffset": 1247, "count": 2}], "isBlockCoverage": true}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 3}, {"startOffset": 1492, "endOffset": 1528, "count": 0}, {"startOffset": 1560, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 3}], "isBlockCoverage": true}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 4}], "isBlockCoverage": true}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 3}, {"startOffset": 1930, "endOffset": 1940, "count": 0}, {"startOffset": 2004, "endOffset": 2031, "count": 0}, {"startOffset": 2133, "endOffset": 2192, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}