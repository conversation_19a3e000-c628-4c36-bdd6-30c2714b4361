{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fileUtils.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 396, "endOffset": 501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1152, "endOffset": 18799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 2214, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1648, "endOffset": 1665, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2255, "endOffset": 2487, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2543, "endOffset": 5519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2708, "endOffset": 3017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3100, "endOffset": 3206, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3292, "endOffset": 3635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3746, "endOffset": 3966, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4057, "endOffset": 4374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4472, "endOffset": 4860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4975, "endOffset": 5513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5575, "endOffset": 7824, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5655, "endOffset": 5760, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5803, "endOffset": 5946, "count": 5}, {"startOffset": 5872, "endOffset": 5940, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6023, "endOffset": 6205, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6288, "endOffset": 6554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6644, "endOffset": 7004, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7117, "endOffset": 7474, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7569, "endOffset": 7818, "count": 1}, {"startOffset": 7638, "endOffset": 7706, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7882, "endOffset": 10489, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7962, "endOffset": 8148, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8191, "endOffset": 8385, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8468, "endOffset": 8634, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8718, "endOffset": 8885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8960, "endOffset": 9130, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9229, "endOffset": 9405, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9504, "endOffset": 9685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9792, "endOffset": 10201, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10313, "endOffset": 10483, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10557, "endOffset": 18795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10606, "endOffset": 11063, "count": 11}, {"startOffset": 10678, "endOffset": 10723, "count": 0}, {"startOffset": 10789, "endOffset": 10835, "count": 0}, {"startOffset": 10899, "endOffset": 10943, "count": 0}, {"startOffset": 11010, "endOffset": 11057, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11139, "endOffset": 11610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11678, "endOffset": 11996, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12076, "endOffset": 12636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12721, "endOffset": 13352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13420, "endOffset": 14183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14248, "endOffset": 15020, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15084, "endOffset": 15634, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15710, "endOffset": 16008, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16103, "endOffset": 17061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16164, "endOffset": 16189, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17142, "endOffset": 17844, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17924, "endOffset": 18789, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fileUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 12}, {"startOffset": 485, "endOffset": 493, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 5}, {"startOffset": 574, "endOffset": 582, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 7}, {"startOffset": 667, "endOffset": 675, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 11}, {"startOffset": 780, "endOffset": 788, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1290, "endOffset": 1470, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 1472, "endOffset": 2066, "count": 12}, {"startOffset": 1861, "endOffset": 1886, "count": 0}, {"startOffset": 2009, "endOffset": 2063, "count": 11}], "isBlockCoverage": true}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 2068, "endOffset": 2935, "count": 12}, {"startOffset": 2275, "endOffset": 2353, "count": 1}, {"startOffset": 2353, "endOffset": 2618, "count": 10}, {"startOffset": 2618, "endOffset": 2631, "count": 0}, {"startOffset": 2631, "endOffset": 2703, "count": 10}, {"startOffset": 2703, "endOffset": 2850, "count": 2822}, {"startOffset": 2732, "endOffset": 2744, "count": 1}, {"startOffset": 2744, "endOffset": 2769, "count": 2821}, {"startOffset": 2769, "endOffset": 2804, "count": 2807}, {"startOffset": 2787, "endOffset": 2804, "count": 2782}, {"startOffset": 2806, "endOffset": 2844, "count": 14}, {"startOffset": 2850, "endOffset": 2904, "count": 9}, {"startOffset": 2904, "endOffset": 2933, "count": 1}], "isBlockCoverage": true}, {"functionName": "detectFileType", "ranges": [{"startOffset": 2937, "endOffset": 3773, "count": 16}, {"startOffset": 3148, "endOffset": 3188, "count": 9}, {"startOffset": 3190, "endOffset": 3215, "count": 4}, {"startOffset": 3215, "endOffset": 3240, "count": 12}, {"startOffset": 3240, "endOffset": 3281, "count": 5}, {"startOffset": 3283, "endOffset": 3306, "count": 2}, {"startOffset": 3306, "endOffset": 3669, "count": 10}, {"startOffset": 3669, "endOffset": 3695, "count": 3}, {"startOffset": 3695, "endOffset": 3727, "count": 7}, {"startOffset": 3727, "endOffset": 3753, "count": 1}, {"startOffset": 3753, "endOffset": 3772, "count": 6}], "isBlockCoverage": true}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 3775, "endOffset": 7818, "count": 11}, {"startOffset": 3926, "endOffset": 4068, "count": 1}, {"startOffset": 4068, "endOffset": 4167, "count": 10}, {"startOffset": 4167, "endOffset": 4331, "count": 1}, {"startOffset": 4331, "endOffset": 4530, "count": 9}, {"startOffset": 4530, "endOffset": 4746, "count": 1}, {"startOffset": 4753, "endOffset": 6550, "count": 5}, {"startOffset": 4864, "endOffset": 4989, "count": 4}, {"startOffset": 4989, "endOffset": 4993, "count": 3}, {"startOffset": 5043, "endOffset": 5072, "count": 2}, {"startOffset": 5073, "endOffset": 5080, "count": 2}, {"startOffset": 5767, "endOffset": 5796, "count": 3}, {"startOffset": 5866, "endOffset": 6062, "count": 1}, {"startOffset": 6062, "endOffset": 6258, "count": 3}, {"startOffset": 6100, "endOffset": 6258, "count": 1}, {"startOffset": 6258, "endOffset": 6405, "count": 4}, {"startOffset": 6405, "endOffset": 6420, "count": 2}, {"startOffset": 6421, "endOffset": 6425, "count": 2}, {"startOffset": 6557, "endOffset": 6570, "count": 2}, {"startOffset": 6577, "endOffset": 7068, "count": 3}, {"startOffset": 6685, "endOffset": 6917, "count": 2}, {"startOffset": 6917, "endOffset": 6946, "count": 0}, {"startOffset": 7075, "endOffset": 7366, "count": 0}, {"startOffset": 7377, "endOffset": 7816, "count": 2}, {"startOffset": 7457, "endOffset": 7472, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5401, "endOffset": 5647, "count": 11}, {"startOffset": 5468, "endOffset": 5613, "count": 1}, {"startOffset": 5613, "endOffset": 5646, "count": 10}], "isBlockCoverage": true}], "startOffset": 209}]}