{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/contentGenerator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6033, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6033, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 846, "endOffset": 2255, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 1458, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1541, "endOffset": 2251, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1691, "endOffset": 1710, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/contentGenerator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 2}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1150, "endOffset": 1338, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1357, "endOffset": 2722, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 2724, "endOffset": 3591, "count": 2}, {"startOffset": 3042, "endOffset": 3208, "count": 1}, {"startOffset": 3208, "endOffset": 3242, "count": 0}, {"startOffset": 3264, "endOffset": 3484, "count": 1}, {"startOffset": 3365, "endOffset": 3373, "count": 0}, {"startOffset": 3484, "endOffset": 3590, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "513", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/codeAssist.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 923, "endOffset": 1379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "514", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/oauth2.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 2061, "endOffset": 2667, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 2669, "endOffset": 4557, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 4558, "endOffset": 5065, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 5067, "endOffset": 5536, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 5537, "endOffset": 5878, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 5879, "endOffset": 6027, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 6028, "endOffset": 6174, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "529", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 785, "endOffset": 954, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 958, "endOffset": 2044, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2046, "endOffset": 2384, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "530", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 650, "endOffset": 1264, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1333, "endOffset": 1498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1564, "endOffset": 1818, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "531", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1073, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1218, "endOffset": 1612, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateContent", "ranges": [{"startOffset": 1615, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "onboardUser", "ranges": [{"startOffset": 1905, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2010, "endOffset": 2118, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2121, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2249, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2392, "endOffset": 2614, "count": 0}], "isBlockCoverage": false}, {"functionName": "embedContent", "ranges": [{"startOffset": 2617, "endOffset": 2666, "count": 0}], "isBlockCoverage": false}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2669, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3065, "endOffset": 3418, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3421, "endOffset": 4521, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "536", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 863, "endOffset": 1014, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1016, "endOffset": 1105, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1107, "endOffset": 1258, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1260, "endOffset": 1620, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1622, "endOffset": 2051, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2052, "endOffset": 2191, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2192, "endOffset": 2299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2300, "endOffset": 2661, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 2662, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 2718, "endOffset": 2824, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 2825, "endOffset": 3738, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "537", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/config/models.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 484, "endOffset": 547, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "538", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/modelCheck.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 550, "endOffset": 1915, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}