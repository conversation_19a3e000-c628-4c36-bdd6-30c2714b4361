{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/getFolderStructure.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35892, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35892, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 390, "endOffset": 645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 530, "endOffset": 542, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDirent", "ranges": [{"startOffset": 1381, "endOffset": 1640, "count": 42}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 1418, "endOffset": 1439, "count": 8}], "isBlockCoverage": true}, {"functionName": "isDirectory", "ranges": [{"startOffset": 1456, "endOffset": 1476, "count": 8}], "isBlockCoverage": true}, {"functionName": "isBlockDevice", "ranges": [{"startOffset": 1495, "endOffset": 1506, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCharacterDevice", "ranges": [{"startOffset": 1529, "endOffset": 1540, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSymbolicLink", "ranges": [{"startOffset": 1560, "endOffset": 1571, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFIFO", "ranges": [{"startOffset": 1583, "endOffset": 1594, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSocket", "ranges": [{"startOffset": 1608, "endOffset": 1619, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1699, "endOffset": 9378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1746, "endOffset": 2329, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1847, "endOffset": 1859, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1924, "endOffset": 2318, "count": 10}, {"startOffset": 2068, "endOffset": 2129, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2370, "endOffset": 2429, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3104, "endOffset": 3151, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3227, "endOffset": 3271, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3428, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3437, "endOffset": 3471, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3858, "endOffset": 4195, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4262, "endOffset": 4546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4643, "endOffset": 5184, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5278, "endOffset": 5831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5910, "endOffset": 6258, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6355, "endOffset": 6699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6785, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7389, "endOffset": 7865, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7939, "endOffset": 8464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8076, "endOffset": 8252, "count": 1}, {"startOffset": 8208, "endOffset": 8251, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8551, "endOffset": 8928, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9024, "endOffset": 9374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9448, "endOffset": 11654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9495, "endOffset": 10634, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9596, "endOffset": 9608, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9666, "endOffset": 10281, "count": 4}, {"startOffset": 9752, "endOffset": 10020, "count": 2}, {"startOffset": 10020, "endOffset": 10083, "count": 0}, {"startOffset": 10083, "endOffset": 10129, "count": 2}, {"startOffset": 10129, "endOffset": 10257, "count": 0}, {"startOffset": 10257, "endOffset": 10280, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10336, "endOffset": 10536, "count": 6}, {"startOffset": 10427, "endOffset": 10512, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10727, "endOffset": 11168, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11258, "endOffset": 11650, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/getFolderStructure.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 13}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 822, "endOffset": 4467, "count": 13}, {"startOffset": 1260, "endOffset": 4445, "count": 15}, {"startOffset": 1358, "endOffset": 1381, "count": 0}, {"startOffset": 1466, "endOffset": 1489, "count": 0}, {"startOffset": 1617, "endOffset": 1697, "count": 4}, {"startOffset": 1697, "endOffset": 2091, "count": 11}, {"startOffset": 2038, "endOffset": 2091, "count": 0}, {"startOffset": 2091, "endOffset": 2199, "count": 4}, {"startOffset": 2199, "endOffset": 2897, "count": 8}, {"startOffset": 2227, "endOffset": 2891, "count": 4}, {"startOffset": 2279, "endOffset": 2349, "count": 0}, {"startOffset": 2501, "endOffset": 2523, "count": 2}, {"startOffset": 2525, "endOffset": 2637, "count": 2}, {"startOffset": 2592, "endOffset": 2627, "count": 0}, {"startOffset": 2679, "endOffset": 2723, "count": 0}, {"startOffset": 2897, "endOffset": 2974, "count": 4}, {"startOffset": 2974, "endOffset": 4388, "count": 8}, {"startOffset": 3007, "endOffset": 4382, "count": 4}, {"startOffset": 3059, "endOffset": 3134, "count": 0}, {"startOffset": 3337, "endOffset": 3359, "count": 2}, {"startOffset": 3361, "endOffset": 3491, "count": 2}, {"startOffset": 3433, "endOffset": 3481, "count": 0}, {"startOffset": 3547, "endOffset": 3564, "count": 2}, {"startOffset": 3566, "endOffset": 4382, "count": 2}, {"startOffset": 4388, "endOffset": 4445, "count": 4}, {"startOffset": 4445, "endOffset": 4466, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1650, "endOffset": 1688, "count": 12}], "isBlockCoverage": true}, {"functionName": "formatStructure", "ranges": [{"startOffset": 4468, "endOffset": 5983, "count": 6}, {"startOffset": 4608, "endOffset": 4616, "count": 4}, {"startOffset": 4617, "endOffset": 4625, "count": 2}, {"startOffset": 4655, "endOffset": 4672, "count": 2}, {"startOffset": 4674, "endOffset": 4799, "count": 4}, {"startOffset": 4759, "endOffset": 4781, "count": 2}, {"startOffset": 4782, "endOffset": 4786, "count": 2}, {"startOffset": 4850, "endOffset": 4854, "count": 2}, {"startOffset": 4855, "endOffset": 4912, "count": 4}, {"startOffset": 4894, "endOffset": 4902, "count": 2}, {"startOffset": 4903, "endOffset": 4911, "count": 2}, {"startOffset": 4991, "endOffset": 5255, "count": 4}, {"startOffset": 5049, "endOffset": 5080, "count": 2}, {"startOffset": 5081, "endOffset": 5107, "count": 0}, {"startOffset": 5159, "endOffset": 5167, "count": 0}, {"startOffset": 5282, "endOffset": 5540, "count": 0}, {"startOffset": 5634, "endOffset": 5875, "count": 4}, {"startOffset": 5702, "endOffset": 5728, "count": 2}, {"startOffset": 5907, "endOffset": 5981, "count": 0}], "isBlockCoverage": true}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 5984, "endOffset": 7871, "count": 13}, {"startOffset": 6152, "endOffset": 6162, "count": 9}, {"startOffset": 6163, "endOffset": 6175, "count": 8}, {"startOffset": 6204, "endOffset": 6220, "count": 9}, {"startOffset": 6221, "endOffset": 6247, "count": 12}, {"startOffset": 6280, "endOffset": 6300, "count": 9}, {"startOffset": 6326, "endOffset": 6339, "count": 9}, {"startOffset": 6370, "endOffset": 6388, "count": 9}, {"startOffset": 6389, "endOffset": 6396, "count": 12}, {"startOffset": 6858, "endOffset": 6960, "count": 11}, {"startOffset": 6960, "endOffset": 7657, "count": 2}, {"startOffset": 7657, "endOffset": 7869, "count": 0}], "isBlockCoverage": true}, {"functionName": "checkForTruncation", "ranges": [{"startOffset": 6439, "endOffset": 6752, "count": 6}, {"startOffset": 6529, "endOffset": 6573, "count": 2}, {"startOffset": 6606, "endOffset": 6746, "count": 4}, {"startOffset": 6722, "endOffset": 6728, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 11}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 11}], "isBlockCoverage": true}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "361", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "362", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 2}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 2}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 2}, {"startOffset": 1173, "endOffset": 1197, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 4}, {"startOffset": 2113, "endOffset": 2135, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "363", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 4}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 4}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 4}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 2}, {"startOffset": 1040, "endOffset": 1047, "count": 0}, {"startOffset": 1211, "endOffset": 1247, "count": 4}], "isBlockCoverage": true}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 6}, {"startOffset": 1492, "endOffset": 1528, "count": 0}, {"startOffset": 1560, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 6}, {"startOffset": 1623, "endOffset": 1644, "count": 0}], "isBlockCoverage": true}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 8}], "isBlockCoverage": true}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 4}, {"startOffset": 1930, "endOffset": 1940, "count": 0}, {"startOffset": 2004, "endOffset": 2031, "count": 0}, {"startOffset": 2133, "endOffset": 2192, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}