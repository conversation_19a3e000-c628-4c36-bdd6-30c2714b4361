{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/server.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16908, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16908, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 698, "endOffset": 5774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 1011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1089, "endOffset": 2088, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2172, "endOffset": 3294, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2343, "endOffset": 2697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3368, "endOffset": 4070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4147, "endOffset": 4758, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4827, "endOffset": 5343, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5419, "endOffset": 5770, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 8}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "CodeAssistServer", "ranges": [{"startOffset": 1073, "endOffset": 1215, "count": 7}], "isBlockCoverage": true}, {"functionName": "generateContentStream", "ranges": [{"startOffset": 1218, "endOffset": 1612, "count": 1}, {"startOffset": 1424, "endOffset": 1437, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1456, "endOffset": 1605, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateContent", "ranges": [{"startOffset": 1615, "endOffset": 1902, "count": 1}, {"startOffset": 1806, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "onboardUser", "ranges": [{"startOffset": 1905, "endOffset": 2007, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadCodeAssist", "ranges": [{"startOffset": 2010, "endOffset": 2118, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2121, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCodeAssistGlobalUserSetting", "ranges": [{"startOffset": 2249, "endOffset": 2389, "count": 0}], "isBlockCoverage": false}, {"functionName": "countTokens", "ranges": [{"startOffset": 2392, "endOffset": 2614, "count": 1}], "isBlockCoverage": true}, {"functionName": "embedContent", "ranges": [{"startOffset": 2617, "endOffset": 2666, "count": 1}], "isBlockCoverage": true}, {"functionName": "callEndpoint", "ranges": [{"startOffset": 2669, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEndpoint", "ranges": [{"startOffset": 3065, "endOffset": 3418, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamEndpoint", "ranges": [{"startOffset": 3421, "endOffset": 4521, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/code_assist/converter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 1}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 2}, {"startOffset": 521, "endOffset": 529, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 2}, {"startOffset": 640, "endOffset": 648, "count": 0}], "isBlockCoverage": true}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 863, "endOffset": 1014, "count": 1}], "isBlockCoverage": true}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1016, "endOffset": 1105, "count": 1}], "isBlockCoverage": true}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1107, "endOffset": 1258, "count": 2}], "isBlockCoverage": true}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1260, "endOffset": 1620, "count": 2}], "isBlockCoverage": true}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1622, "endOffset": 2051, "count": 2}, {"startOffset": 1768, "endOffset": 1787, "count": 0}, {"startOffset": 1819, "endOffset": 1834, "count": 0}, {"startOffset": 1857, "endOffset": 1864, "count": 0}, {"startOffset": 1892, "endOffset": 1904, "count": 0}, {"startOffset": 1928, "endOffset": 1936, "count": 0}, {"startOffset": 1968, "endOffset": 1984, "count": 0}], "isBlockCoverage": true}, {"functionName": "toContents", "ranges": [{"startOffset": 2052, "endOffset": 2191, "count": 3}, {"startOffset": 2156, "endOffset": 2190, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2192, "endOffset": 2299, "count": 2}, {"startOffset": 2267, "endOffset": 2298, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2300, "endOffset": 2661, "count": 3}, {"startOffset": 2360, "endOffset": 2435, "count": 0}, {"startOffset": 2472, "endOffset": 2550, "count": 0}, {"startOffset": 2603, "endOffset": 2660, "count": 0}], "isBlockCoverage": true}, {"functionName": "toParts", "ranges": [{"startOffset": 2662, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 2718, "endOffset": 2824, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 2825, "endOffset": 3738, "count": 2}, {"startOffset": 2908, "endOffset": 3737, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}