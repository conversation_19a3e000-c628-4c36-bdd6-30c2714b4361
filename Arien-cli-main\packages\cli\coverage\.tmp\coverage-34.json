{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4972, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4972, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 439, "endOffset": 1662, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 1658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 543, "endOffset": 765, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 832, "endOffset": 975, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1028, "endOffset": 1149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1213, "endOffset": 1364, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1411, "endOffset": 1652, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1027", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 6}, {"startOffset": 374, "endOffset": 382, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 6}, {"startOffset": 951, "endOffset": 974, "count": 2}, {"startOffset": 974, "endOffset": 1018, "count": 4}, {"startOffset": 1018, "endOffset": 1048, "count": 1}, {"startOffset": 1049, "endOffset": 1055, "count": 3}, {"startOffset": 1086, "endOffset": 1138, "count": 542}, {"startOffset": 1108, "endOffset": 1134, "count": 1}, {"startOffset": 1138, "endOffset": 1156, "count": 3}], "isBlockCoverage": true}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}