{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/shellCommandProcessor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17022, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17022, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 513, "endOffset": 646, "count": 1}], "isBlockCoverage": true}, {"functionName": "platform", "ranges": [{"startOffset": 549, "endOffset": 562, "count": 6}], "isBlockCoverage": true}, {"functionName": "tmpdir", "ranges": [{"startOffset": 576, "endOffset": 588, "count": 3}], "isBlockCoverage": true}, {"functionName": "platform", "ranges": [{"startOffset": 606, "endOffset": 619, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir", "ranges": [{"startOffset": 631, "endOffset": 643, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 760, "endOffset": 813, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1414, "endOffset": 5688, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1601, "endOffset": 2564, "count": 3}], "isBlockCoverage": true}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 2457, "endOffset": 2474, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2579, "endOffset": 2638, "count": 3}], "isBlockCoverage": true}, {"functionName": "renderProcessorHook", "ranges": [{"startOffset": 2671, "endOffset": 2911, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2709, "endOffset": 2907, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2976, "endOffset": 3829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3111, "endOffset": 3198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3327, "endOffset": 3417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3444, "endOffset": 3499, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3532, "endOffset": 3576, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3868, "endOffset": 4844, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4129, "endOffset": 4250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4379, "endOffset": 4464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4491, "endOffset": 4546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4579, "endOffset": 4623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4885, "endOffset": 5684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5020, "endOffset": 5139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5219, "endOffset": 5306, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5333, "endOffset": 5390, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5423, "endOffset": 5467, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/shellCommandProcessor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 3}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeShellCommand", "ranges": [{"startOffset": 2002, "endOffset": 5369, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2121, "endOffset": 5365, "count": 3}, {"startOffset": 2240, "endOffset": 2251, "count": 0}, {"startOffset": 2294, "endOffset": 2320, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleOutput", "ranges": [{"startOffset": 2876, "endOffset": 4036, "count": 3}, {"startOffset": 3158, "endOffset": 3271, "count": 1}, {"startOffset": 3328, "endOffset": 3355, "count": 2}, {"startOffset": 3356, "endOffset": 3383, "count": 1}, {"startOffset": 3416, "endOffset": 3492, "count": 2}, {"startOffset": 3492, "endOffset": 3574, "count": 1}, {"startOffset": 3609, "endOffset": 3727, "count": 2}, {"startOffset": 3659, "endOffset": 3673, "count": 1}, {"startOffset": 3674, "endOffset": 3678, "count": 1}, {"startOffset": 3727, "endOffset": 4030, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3821, "endOffset": 3855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4066, "endOffset": 4104, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4135, "endOffset": 4173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4198, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "abor<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4261, "endOffset": 4821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4917, "endOffset": 5359, "count": 3}, {"startOffset": 5230, "endOffset": 5244, "count": 1}, {"startOffset": 5245, "endOffset": 5249, "count": 2}], "isBlockCoverage": true}, {"functionName": "addShellCommandToArienHistory", "ranges": [{"startOffset": 5370, "endOffset": 5821, "count": 3}, {"startOffset": 5507, "endOffset": 5573, "count": 0}], "isBlockCoverage": true}, {"functionName": "useShellCommandProcessor", "ranges": [{"startOffset": 5855, "endOffset": 10026, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6019, "endOffset": 9852, "count": 3}, {"startOffset": 6114, "endOffset": 6145, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7016, "endOffset": 9798, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7248, "endOffset": 7490, "count": 4}, {"startOffset": 7343, "endOffset": 7478, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7532, "endOffset": 9226, "count": 3}, {"startOffset": 7720, "endOffset": 7816, "count": 1}, {"startOffset": 7816, "endOffset": 7917, "count": 2}, {"startOffset": 7871, "endOffset": 7904, "count": 0}, {"startOffset": 7988, "endOffset": 8108, "count": 0}, {"startOffset": 8134, "endOffset": 8214, "count": 0}, {"startOffset": 8239, "endOffset": 8383, "count": 0}, {"startOffset": 8416, "endOffset": 8557, "count": 1}, {"startOffset": 8643, "endOffset": 9006, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9234, "endOffset": 9574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9584, "endOffset": 9788, "count": 3}, {"startOffset": 9676, "endOffset": 9756, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1328", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 1}, {"startOffset": 649, "endOffset": 779, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}