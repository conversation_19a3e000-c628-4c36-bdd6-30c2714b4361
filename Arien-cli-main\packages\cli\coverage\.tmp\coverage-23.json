{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useGitBranchName.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 688, "endOffset": 801, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1819, "endOffset": 8229, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1866, "endOffset": 2045, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2086, "endOffset": 2192, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2255, "endOffset": 2755, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2320, "endOffset": 2454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2522, "endOffset": 2565, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2598, "endOffset": 2682, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2837, "endOffset": 3434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2902, "endOffset": 3060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3128, "endOffset": 3171, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3274, "endOffset": 3358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3538, "endOffset": 4222, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3603, "endOffset": 3918, "count": 2}, {"startOffset": 3697, "endOffset": 3858, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3986, "endOffset": 4029, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4062, "endOffset": 4146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4331, "endOffset": 5036, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4396, "endOffset": 4732, "count": 2}, {"startOffset": 4490, "endOffset": 4672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4800, "endOffset": 4843, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4876, "endOffset": 4960, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5122, "endOffset": 6115, "count": 1}, {"startOffset": 5575, "endOffset": 6114, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5211, "endOffset": 5345, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5413, "endOffset": 5456, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5489, "endOffset": 5573, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5698, "endOffset": 5835, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5873, "endOffset": 6039, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6195, "endOffset": 7304, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6311, "endOffset": 6445, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6513, "endOffset": 6556, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6589, "endOffset": 6673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6798, "endOffset": 6935, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7065, "endOffset": 7231, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7375, "endOffset": 8225, "count": 1}, {"startOffset": 8009, "endOffset": 8224, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7644, "endOffset": 7778, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7847, "endOffset": 7890, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7923, "endOffset": 8007, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1327", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useGitBranchName.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 14}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "useGitBranchName", "ranges": [{"startOffset": 1189, "endOffset": 2793, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1373, "endOffset": 2040, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1447, "endOffset": 2034, "count": 5}, {"startOffset": 1496, "endOffset": 1558, "count": 1}, {"startOffset": 1558, "endOffset": 1648, "count": 4}, {"startOffset": 1650, "endOffset": 2026, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1793, "endOffset": 2003, "count": 2}, {"startOffset": 1852, "endOffset": 2002, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2110, "endOffset": 2744, "count": 5}], "isBlockCoverage": true}, {"functionName": "setupWatcher", "ranges": [{"startOffset": 2276, "endOffset": 2669, "count": 5}, {"startOffset": 2416, "endOffset": 2634, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2488, "endOffset": 2623, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2702, "endOffset": 2739, "count": 5}, {"startOffset": 2723, "endOffset": 2730, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}