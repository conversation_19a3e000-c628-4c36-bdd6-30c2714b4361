{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 399, "endOffset": 492, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 839, "endOffset": 14089, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1084, "count": 73}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1125, "endOffset": 1325, "count": 73}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1387, "endOffset": 3985, "count": 1}, {"startOffset": 1908, "endOffset": 3981, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1964, "endOffset": 3973, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2077, "endOffset": 2538, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2655, "endOffset": 2953, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2791, "endOffset": 2841, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3062, "endOffset": 3547, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3665, "endOffset": 3963, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3801, "endOffset": 3851, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4043, "endOffset": 6729, "count": 1}, {"startOffset": 4504, "endOffset": 5391, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4607, "endOffset": 4938, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5038, "endOffset": 5383, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5471, "endOffset": 6480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6564, "endOffset": 6723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6781, "endOffset": 11064, "count": 1}, {"startOffset": 6942, "endOffset": 9086, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7014, "endOffset": 7990, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7098, "endOffset": 7198, "count": 10}, {"startOffset": 7150, "endOffset": 7186, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8074, "endOffset": 8551, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8210, "endOffset": 8318, "count": 10}, {"startOffset": 8262, "endOffset": 8306, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8644, "endOffset": 9078, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8728, "endOffset": 8828, "count": 10}, {"startOffset": 8780, "endOffset": 8816, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9257, "endOffset": 9890, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9675, "endOffset": 9694, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9977, "endOffset": 10605, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10704, "endOffset": 11058, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10816, "endOffset": 10831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11132, "endOffset": 12169, "count": 1}, {"startOffset": 11707, "endOffset": 12165, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11209, "endOffset": 11361, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11442, "endOffset": 11555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11790, "endOffset": 11950, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12039, "endOffset": 12157, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12230, "endOffset": 14085, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12315, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12506, "endOffset": 12610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12693, "endOffset": 12811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12919, "endOffset": 13106, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13219, "endOffset": 13424, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13277, "endOffset": 13319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13529, "endOffset": 13756, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13857, "endOffset": 14079, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/editor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 24}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 12}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 7}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 19}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 18}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 899, "endOffset": 1031, "count": 42}], "isBlockCoverage": true}, {"functionName": "commandExists", "ranges": [{"startOffset": 1032, "endOffset": 1274, "count": 28}, {"startOffset": 1145, "endOffset": 1165, "count": 16}, {"startOffset": 1166, "endOffset": 1187, "count": 12}, {"startOffset": 1243, "endOffset": 1272, "count": 13}], "isBlockCoverage": true}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1596, "endOffset": 1810, "count": 28}, {"startOffset": 1729, "endOffset": 1750, "count": 16}, {"startOffset": 1751, "endOffset": 1774, "count": 12}], "isBlockCoverage": true}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 1812, "endOffset": 2031, "count": 15}, {"startOffset": 1980, "endOffset": 2013, "count": 12}, {"startOffset": 2013, "endOffset": 2030, "count": 3}], "isBlockCoverage": true}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2033, "endOffset": 2212, "count": 7}, {"startOffset": 2083, "endOffset": 2111, "count": 5}, {"startOffset": 2113, "endOffset": 2193, "count": 4}, {"startOffset": 2153, "endOffset": 2188, "count": 3}, {"startOffset": 2193, "endOffset": 2211, "count": 3}], "isBlockCoverage": true}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2214, "endOffset": 3825, "count": 37}, {"startOffset": 2300, "endOffset": 2322, "count": 2}, {"startOffset": 2322, "endOffset": 2419, "count": 35}, {"startOffset": 2419, "endOffset": 2440, "count": 28}, {"startOffset": 2441, "endOffset": 2464, "count": 7}, {"startOffset": 2490, "endOffset": 2504, "count": 6}, {"startOffset": 2509, "endOffset": 2525, "count": 12}, {"startOffset": 2530, "endOffset": 2546, "count": 18}, {"startOffset": 2551, "endOffset": 2565, "count": 24}, {"startOffset": 2570, "endOffset": 2653, "count": 30}, {"startOffset": 2658, "endOffset": 3787, "count": 5}, {"startOffset": 3792, "endOffset": 3819, "count": 0}], "isBlockCoverage": true}, {"functionName": "openDiff", "ranges": [{"startOffset": 3827, "endOffset": 5224, "count": 18}, {"startOffset": 3963, "endOffset": 4054, "count": 1}, {"startOffset": 4054, "endOffset": 4092, "count": 17}, {"startOffset": 4092, "endOffset": 4106, "count": 3}, {"startOffset": 4113, "endOffset": 4129, "count": 6}, {"startOffset": 4136, "endOffset": 4152, "count": 9}, {"startOffset": 4159, "endOffset": 4173, "count": 12}, {"startOffset": 4180, "endOffset": 4736, "count": 15}, {"startOffset": 4743, "endOffset": 5093, "count": 2}, {"startOffset": 4810, "endOffset": 4866, "count": 1}, {"startOffset": 4867, "endOffset": 4948, "count": 1}, {"startOffset": 5100, "endOffset": 5166, "count": 0}, {"startOffset": 5177, "endOffset": 5222, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4219, "endOffset": 4734, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4449, "endOffset": 4633, "count": 10}, {"startOffset": 4489, "endOffset": 4621, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4671, "endOffset": 4722, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4916, "endOffset": 4935, "count": 17}], "isBlockCoverage": true}], "startOffset": 209}]}