{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-many-files.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1185, "endOffset": 16937, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1311, "endOffset": 3415, "count": 22}], "isBlockCoverage": true}, {"functionName": "getFileService", "ranges": [{"startOffset": 1939, "endOffset": 1956, "count": 35}], "isBlockCoverage": true}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 1998, "endOffset": 2008, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2243, "endOffset": 3404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3456, "endOffset": 3806, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3864, "endOffset": 6651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3964, "endOffset": 4124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4218, "endOffset": 4372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4502, "endOffset": 4647, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4748, "endOffset": 4947, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5030, "endOffset": 5262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5359, "endOffset": 5587, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5686, "endOffset": 5905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6010, "endOffset": 6275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6380, "endOffset": 6645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6702, "endOffset": 16933, "count": 1}], "isBlockCoverage": true}, {"functionName": "createFile", "ranges": [{"startOffset": 6733, "endOffset": 7037, "count": 16}], "isBlockCoverage": true}, {"functionName": "createBinaryFile", "ranges": [{"startOffset": 7068, "endOffset": 7361, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7435, "endOffset": 7914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7990, "endOffset": 8740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8343, "endOffset": 8397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8491, "endOffset": 8551, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8807, "endOffset": 9681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9178, "endOffset": 9232, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9326, "endOffset": 9386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9469, "endOffset": 9503, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9752, "endOffset": 10450, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10228, "endOffset": 10265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10542, "endOffset": 10999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11066, "endOffset": 11747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11511, "endOffset": 11562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11849, "endOffset": 12637, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12235, "endOffset": 12310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12402, "endOffset": 12457, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12755, "endOffset": 13537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13650, "endOffset": 14297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14407, "endOffset": 15153, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14765, "endOffset": 14828, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15274, "endOffset": 15723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15839, "endOffset": 16302, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16406, "endOffset": 16927, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/__mocks__/fs/promises.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 22}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 330, "endOffset": 370, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 411, "endOffset": 455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 491, "endOffset": 530, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 566, "endOffset": 605, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 644, "endOffset": 686, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 719, "endOffset": 755, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 832, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 909, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 982, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1018, "endOffset": 1057, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1132, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1167, "endOffset": 1205, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1243, "endOffset": 1284, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1322, "endOffset": 1363, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1402, "endOffset": 1444, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1483, "endOffset": 1525, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1562, "endOffset": 1602, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1638, "endOffset": 1677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1710, "endOffset": 1746, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1781, "endOffset": 1819, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1857, "endOffset": 1898, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1937, "endOffset": 1979, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2016, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2093, "endOffset": 2133, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2169, "endOffset": 2208, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2248, "endOffset": 2291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2330, "endOffset": 2372, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/read-many-files.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 22}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "ReadManyFilesTool", "ranges": [{"startOffset": 2422, "endOffset": 6250, "count": 22}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 6253, "endOffset": 6284, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6288, "endOffset": 6312, "count": 22}], "isBlockCoverage": true}, {"functionName": "validateParams", "ranges": [{"startOffset": 6316, "endOffset": 7654, "count": 22}, {"startOffset": 6425, "endOffset": 6538, "count": 1}, {"startOffset": 6538, "endOffset": 6671, "count": 21}, {"startOffset": 6673, "endOffset": 7023, "count": 0}, {"startOffset": 7023, "endOffset": 7059, "count": 21}, {"startOffset": 7059, "endOffset": 7208, "count": 27}, {"startOffset": 7113, "endOffset": 7202, "count": 1}, {"startOffset": 7208, "endOffset": 7233, "count": 20}, {"startOffset": 7233, "endOffset": 7329, "count": 2}, {"startOffset": 7331, "endOffset": 7420, "count": 1}, {"startOffset": 7420, "endOffset": 7445, "count": 19}, {"startOffset": 7445, "endOffset": 7541, "count": 3}, {"startOffset": 7543, "endOffset": 7632, "count": 1}, {"startOffset": 7632, "endOffset": 7653, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7293, "endOffset": 7327, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7505, "endOffset": 7539, "count": 4}], "isBlockCoverage": true}, {"functionName": "getDescription", "ranges": [{"startOffset": 7657, "endOffset": 9012, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 9015, "endOffset": 15921, "count": 13}, {"startOffset": 9129, "endOffset": 9325, "count": 0}, {"startOffset": 9536, "endOffset": 9585, "count": 0}, {"startOffset": 9889, "endOffset": 9953, "count": 12}, {"startOffset": 9954, "endOffset": 9997, "count": 1}, {"startOffset": 10095, "endOffset": 10320, "count": 0}, {"startOffset": 10846, "endOffset": 10855, "count": 0}, {"startOffset": 10934, "endOffset": 11430, "count": 17}, {"startOffset": 10991, "endOffset": 11235, "count": 0}, {"startOffset": 11314, "endOffset": 11374, "count": 0}, {"startOffset": 11463, "endOffset": 11586, "count": 0}, {"startOffset": 11593, "endOffset": 11897, "count": 0}, {"startOffset": 11999, "endOffset": 14069, "count": 17}, {"startOffset": 12219, "endOffset": 12240, "count": 15}, {"startOffset": 12242, "endOffset": 12863, "count": 5}, {"startOffset": 12643, "endOffset": 12855, "count": 1}, {"startOffset": 12863, "endOffset": 13031, "count": 16}, {"startOffset": 13031, "endOffset": 13176, "count": 0}, {"startOffset": 13176, "endOffset": 14063, "count": 16}, {"startOffset": 13243, "endOffset": 13474, "count": 12}, {"startOffset": 13474, "endOffset": 13547, "count": 4}, {"startOffset": 13683, "endOffset": 13729, "count": 12}, {"startOffset": 13730, "endOffset": 13738, "count": 4}, {"startOffset": 14210, "endOffset": 14833, "count": 12}, {"startOffset": 14557, "endOffset": 14827, "count": 0}, {"startOffset": 14868, "endOffset": 15495, "count": 1}, {"startOffset": 14922, "endOffset": 15020, "count": 0}, {"startOffset": 15142, "endOffset": 15248, "count": 0}, {"startOffset": 15408, "endOffset": 15489, "count": 0}, {"startOffset": 15495, "endOffset": 15670, "count": 12}, {"startOffset": 15546, "endOffset": 15574, "count": 1}, {"startOffset": 15576, "endOffset": 15670, "count": 1}, {"startOffset": 15707, "endOffset": 15821, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10677, "endOffset": 10730, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10792, "endOffset": 10844, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12493, "endOffset": 12597, "count": 5}, {"startOffset": 12552, "endOffset": 12597, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14500, "endOffset": 14538, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14691, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15298, "endOffset": 15363, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "322", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 22}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 42}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "323", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/schemaValidator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 21}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 609, "endOffset": 1607, "count": 21}, {"startOffset": 840, "endOffset": 936, "count": 0}, {"startOffset": 1154, "endOffset": 1579, "count": 126}, {"startOffset": 1192, "endOffset": 1204, "count": 27}, {"startOffset": 1206, "endOffset": 1571, "count": 27}, {"startOffset": 1307, "endOffset": 1316, "count": 26}, {"startOffset": 1317, "endOffset": 1338, "count": 1}, {"startOffset": 1383, "endOffset": 1561, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "383", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 1}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3261, "endOffset": 3562, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 3564, "endOffset": 3727, "count": 1}, {"startOffset": 3648, "endOffset": 3691, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 3729, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 3890, "endOffset": 4049, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4050, "endOffset": 4347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 4435, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 4478, "endOffset": 4626, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 4636, "endOffset": 6391, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 6394, "endOffset": 7669, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "384", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/fileUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 16}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 17}, {"startOffset": 667, "endOffset": 675, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 16}, {"startOffset": 780, "endOffset": 788, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1290, "endOffset": 1470, "count": 16}, {"startOffset": 1444, "endOffset": 1458, "count": 15}, {"startOffset": 1459, "endOffset": 1467, "count": 1}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 1472, "endOffset": 2066, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 2068, "endOffset": 2935, "count": 24}, {"startOffset": 2275, "endOffset": 2353, "count": 2}, {"startOffset": 2353, "endOffset": 2618, "count": 22}, {"startOffset": 2618, "endOffset": 2631, "count": 0}, {"startOffset": 2631, "endOffset": 2703, "count": 22}, {"startOffset": 2703, "endOffset": 2850, "count": 208}, {"startOffset": 2732, "endOffset": 2744, "count": 0}, {"startOffset": 2806, "endOffset": 2844, "count": 0}, {"startOffset": 2850, "endOffset": 2904, "count": 22}, {"startOffset": 2904, "endOffset": 2933, "count": 0}], "isBlockCoverage": true}, {"functionName": "detectFileType", "ranges": [{"startOffset": 2937, "endOffset": 3773, "count": 33}, {"startOffset": 3148, "endOffset": 3188, "count": 31}, {"startOffset": 3190, "endOffset": 3215, "count": 4}, {"startOffset": 3215, "endOffset": 3240, "count": 29}, {"startOffset": 3240, "endOffset": 3281, "count": 27}, {"startOffset": 3283, "endOffset": 3306, "count": 5}, {"startOffset": 3306, "endOffset": 3669, "count": 24}, {"startOffset": 3669, "endOffset": 3695, "count": 0}, {"startOffset": 3695, "endOffset": 3727, "count": 24}, {"startOffset": 3727, "endOffset": 3753, "count": 0}, {"startOffset": 3753, "endOffset": 3772, "count": 24}], "isBlockCoverage": true}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 3775, "endOffset": 7818, "count": 16}, {"startOffset": 3926, "endOffset": 4068, "count": 0}, {"startOffset": 4167, "endOffset": 4331, "count": 0}, {"startOffset": 4530, "endOffset": 4746, "count": 0}, {"startOffset": 4753, "endOffset": 6550, "count": 12}, {"startOffset": 5073, "endOffset": 5080, "count": 0}, {"startOffset": 5866, "endOffset": 6062, "count": 0}, {"startOffset": 6100, "endOffset": 6258, "count": 0}, {"startOffset": 6405, "endOffset": 6420, "count": 0}, {"startOffset": 6557, "endOffset": 6570, "count": 2}, {"startOffset": 6577, "endOffset": 7068, "count": 4}, {"startOffset": 6917, "endOffset": 6946, "count": 0}, {"startOffset": 7075, "endOffset": 7366, "count": 0}, {"startOffset": 7377, "endOffset": 7816, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5401, "endOffset": 5647, "count": 12}, {"startOffset": 5468, "endOffset": 5613, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "389", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/metrics.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 16}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 16}, {"startOffset": 1020, "endOffset": 1028, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1588, "endOffset": 1749, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 1976, "endOffset": 2072, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2073, "endOffset": 2234, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2236, "endOffset": 4028, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4030, "endOffset": 4502, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4504, "endOffset": 4732, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 4734, "endOffset": 5196, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5198, "endOffset": 5684, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 5686, "endOffset": 6125, "count": 16}, {"startOffset": 5798, "endOffset": 5822, "count": 0}, {"startOffset": 5831, "endOffset": 6124, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "485", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/telemetry/constants.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "486", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 22}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 22}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 22}, {"startOffset": 1031, "endOffset": 1241, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1659, "endOffset": 1917, "count": 17}, {"startOffset": 1749, "endOffset": 1780, "count": 0}, {"startOffset": 1819, "endOffset": 1858, "count": 0}, {"startOffset": 1860, "endOffset": 1891, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 17}, {"startOffset": 2051, "endOffset": 2113, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 22}, {"startOffset": 2495, "endOffset": 2500, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "487", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 22}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 22}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 22}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 22}, {"startOffset": 1492, "endOffset": 1528, "count": 0}, {"startOffset": 1560, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 22}], "isBlockCoverage": true}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 22}], "isBlockCoverage": true}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 22}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "489", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 22}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 22}, {"startOffset": 735, "endOffset": 1054, "count": 154}, {"startOffset": 858, "endOffset": 888, "count": 0}, {"startOffset": 993, "endOffset": 1017, "count": 22}, {"startOffset": 1017, "endOffset": 1054, "count": 132}, {"startOffset": 1078, "endOffset": 1116, "count": 0}], "isBlockCoverage": true}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}