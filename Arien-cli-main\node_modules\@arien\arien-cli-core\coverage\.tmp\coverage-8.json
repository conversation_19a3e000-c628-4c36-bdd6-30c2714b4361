{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/modifiable-tool.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 406, "endOffset": 441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 501, "endOffset": 536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 591, "endOffset": 627, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 668, "endOffset": 710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1224, "endOffset": 12745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1416, "endOffset": 3027, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2223, "endOffset": 2373, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2562, "endOffset": 2574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2638, "endOffset": 2651, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2712, "endOffset": 2725, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2788, "endOffset": 2919, "count": 12}, {"startOffset": 2842, "endOffset": 2918, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3068, "endOffset": 3127, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3194, "endOffset": 7294, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3292, "endOffset": 6358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6447, "endOffset": 6874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6967, "endOffset": 7288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7378, "endOffset": 8299, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7452, "endOffset": 7674, "count": 2}, {"startOffset": 7506, "endOffset": 7673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8383, "endOffset": 9302, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8457, "endOffset": 8678, "count": 2}, {"startOffset": 8511, "endOffset": 8677, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9387, "endOffset": 9837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9924, "endOffset": 10596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10034, "endOffset": 10047, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10108, "endOffset": 10178, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10693, "endOffset": 11674, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11774, "endOffset": 12741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12803, "endOffset": 13382, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12905, "endOffset": 13118, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13219, "endOffset": 13378, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/modifiable-tool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 2}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 9}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1206, "endOffset": 1278, "count": 2}], "isBlockCoverage": true}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1280, "endOffset": 2318, "count": 9}, {"startOffset": 1570, "endOffset": 1650, "count": 1}], "isBlockCoverage": true}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2319, "endOffset": 3314, "count": 8}, {"startOffset": 2542, "endOffset": 2669, "count": 1}, {"startOffset": 2634, "endOffset": 2644, "count": 0}, {"startOffset": 2765, "endOffset": 2892, "count": 1}, {"startOffset": 2857, "endOffset": 2867, "count": 0}], "isBlockCoverage": true}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3315, "endOffset": 3648, "count": 9}, {"startOffset": 3427, "endOffset": 3502, "count": 1}, {"startOffset": 3571, "endOffset": 3646, "count": 1}], "isBlockCoverage": true}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 3649, "endOffset": 4328, "count": 9}, {"startOffset": 4134, "endOffset": 4274, "count": 8}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/diffOptions.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 8}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "321", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 964, "endOffset": 1047, "count": 2}], "isBlockCoverage": true}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1049, "endOffset": 1239, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1365, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 1882, "endOffset": 2057, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}