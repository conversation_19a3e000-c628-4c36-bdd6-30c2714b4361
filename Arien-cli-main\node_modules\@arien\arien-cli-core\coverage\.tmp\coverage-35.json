{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/generateContentResponseUtilities.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41421, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockTextPart", "ranges": [{"startOffset": 880, "endOffset": 900, "count": 26}], "isBlockCoverage": true}, {"functionName": "mockFunctionCallPart", "ranges": [{"startOffset": 931, "endOffset": 995, "count": 22}, {"startOffset": 985, "endOffset": 990, "count": 6}], "isBlockCoverage": true}, {"functionName": "mockResponse", "ranges": [{"startOffset": 1018, "endOffset": 1422, "count": 16}], "isBlockCoverage": true}, {"functionName": "minimalMockResponse", "ranges": [{"startOffset": 1452, "endOffset": 1641, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1714, "endOffset": 14543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1778, "endOffset": 3770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1864, "endOffset": 2005, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2095, "endOffset": 2232, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2308, "endOffset": 2471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2554, "endOffset": 2736, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2824, "endOffset": 3060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3133, "endOffset": 3411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3508, "endOffset": 3764, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3838, "endOffset": 5259, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3919, "endOffset": 4044, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4127, "endOffset": 4271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4359, "endOffset": 4579, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4652, "endOffset": 4916, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5013, "endOffset": 5253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5319, "endOffset": 7600, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5405, "endOffset": 5547, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5637, "endOffset": 5775, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5851, "endOffset": 6015, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6092, "endOffset": 6371, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6449, "endOffset": 6853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6917, "endOffset": 7266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7354, "endOffset": 7594, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7669, "endOffset": 9377, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7750, "endOffset": 7876, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7953, "endOffset": 8194, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8272, "endOffset": 8660, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8724, "endOffset": 9059, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9147, "endOffset": 9371, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9443, "endOffset": 10326, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9531, "endOffset": 10045, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10129, "endOffset": 10320, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10401, "endOffset": 11282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10500, "endOffset": 11003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11096, "endOffset": 11276, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11347, "endOffset": 12910, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11435, "endOffset": 11635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11741, "endOffset": 12099, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12194, "endOffset": 12631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12735, "endOffset": 12904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12984, "endOffset": 14539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13081, "endOffset": 13270, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13385, "endOffset": 13716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13820, "endOffset": 14262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14375, "endOffset": 14533, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/generateContentResponseUtilities.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 7}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 5}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 7}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 5}, {"startOffset": 616, "endOffset": 624, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 2}, {"startOffset": 725, "endOffset": 733, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 2}, {"startOffset": 852, "endOffset": 860, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 4}, {"startOffset": 959, "endOffset": 967, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 4}, {"startOffset": 1084, "endOffset": 1092, "count": 0}], "isBlockCoverage": true}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1510, "count": 11}, {"startOffset": 1257, "endOffset": 1262, "count": 10}, {"startOffset": 1262, "endOffset": 1278, "count": 9}, {"startOffset": 1294, "endOffset": 1318, "count": 2}, {"startOffset": 1318, "endOffset": 1451, "count": 9}, {"startOffset": 1451, "endOffset": 1475, "count": 4}, {"startOffset": 1475, "endOffset": 1509, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1353, "endOffset": 1372, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1381, "endOffset": 1415, "count": 12}], "isBlockCoverage": true}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1512, "endOffset": 1785, "count": 9}, {"startOffset": 1569, "endOffset": 1593, "count": 0}, {"startOffset": 1726, "endOffset": 1750, "count": 4}, {"startOffset": 1750, "endOffset": 1784, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1628, "endOffset": 1647, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1656, "endOffset": 1690, "count": 12}], "isBlockCoverage": true}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1787, "endOffset": 2098, "count": 13}, {"startOffset": 1860, "endOffset": 1865, "count": 12}, {"startOffset": 1865, "endOffset": 1881, "count": 11}, {"startOffset": 1897, "endOffset": 1921, "count": 2}, {"startOffset": 1921, "endOffset": 2067, "count": 11}, {"startOffset": 2067, "endOffset": 2086, "count": 6}, {"startOffset": 2087, "endOffset": 2095, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1964, "endOffset": 1993, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1999, "endOffset": 2026, "count": 8}], "isBlockCoverage": true}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2100, "endOffset": 2359, "count": 11}, {"startOffset": 2158, "endOffset": 2182, "count": 0}, {"startOffset": 2328, "endOffset": 2347, "count": 6}, {"startOffset": 2348, "endOffset": 2356, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2225, "endOffset": 2254, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2287, "count": 8}], "isBlockCoverage": true}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2361, "endOffset": 2555, "count": 6}, {"startOffset": 2479, "endOffset": 2554, "count": 3}], "isBlockCoverage": true}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2557, "endOffset": 2763, "count": 6}, {"startOffset": 2687, "endOffset": 2762, "count": 3}], "isBlockCoverage": true}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2765, "endOffset": 3146, "count": 4}, {"startOffset": 2937, "endOffset": 2957, "count": 2}, {"startOffset": 2959, "endOffset": 3014, "count": 1}, {"startOffset": 3014, "endOffset": 3035, "count": 3}, {"startOffset": 3035, "endOffset": 3064, "count": 1}, {"startOffset": 3064, "endOffset": 3091, "count": 2}, {"startOffset": 3091, "endOffset": 3145, "count": 1}], "isBlockCoverage": true}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3148, "endOffset": 3547, "count": 4}, {"startOffset": 3338, "endOffset": 3358, "count": 2}, {"startOffset": 3360, "endOffset": 3415, "count": 1}, {"startOffset": 3415, "endOffset": 3436, "count": 3}, {"startOffset": 3436, "endOffset": 3465, "count": 1}, {"startOffset": 3465, "endOffset": 3492, "count": 2}, {"startOffset": 3492, "endOffset": 3546, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}