{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/markdownUtilities.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7269, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7269, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 699, "endOffset": 2903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 770, "endOffset": 2899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 877, "endOffset": 1062, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1162, "endOffset": 1355, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1505, "endOffset": 1690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1828, "endOffset": 2027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2157, "endOffset": 2367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2455, "endOffset": 2601, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2715, "endOffset": 2893, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1028", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/markdownUtilities.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11882, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11882, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 7}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "isIndexInsideCodeBlock", "ranges": [{"startOffset": 437, "endOffset": 766, "count": 11}, {"startOffset": 545, "endOffset": 732, "count": 13}, {"startOffset": 629, "endOffset": 656, "count": 4}, {"startOffset": 658, "endOffset": 678, "count": 10}, {"startOffset": 678, "endOffset": 732, "count": 3}], "isBlockCoverage": true}, {"functionName": "findEnclosingCodeBlockStart", "ranges": [{"startOffset": 804, "endOffset": 1426, "count": 7}, {"startOffset": 893, "endOffset": 1425, "count": 0}], "isBlockCoverage": true}, {"functionName": "findLastSafeSplitPoint", "ranges": [{"startOffset": 1459, "endOffset": 2053, "count": 7}, {"startOffset": 1604, "endOffset": 1641, "count": 0}, {"startOffset": 1716, "endOffset": 2025, "count": 8}, {"startOffset": 1811, "endOffset": 1942, "count": 4}, {"startOffset": 1942, "endOffset": 1983, "count": 3}, {"startOffset": 1983, "endOffset": 2025, "count": 1}, {"startOffset": 2025, "endOffset": 2052, "count": 4}], "isBlockCoverage": true}], "startOffset": 209}]}