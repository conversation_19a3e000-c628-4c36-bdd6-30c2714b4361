{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/glob.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 12472, "count": 1}], "isBlockCoverage": true}, {"functionName": "getFileService", "ranges": [{"startOffset": 1322, "endOffset": 1387, "count": 9}], "isBlockCoverage": true}, {"functionName": "getFileFilteringRespectGitIgnore", "ranges": [{"startOffset": 1427, "endOffset": 1437, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1482, "endOffset": 2944, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2769, "endOffset": 2805, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2985, "endOffset": 3093, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3144, "endOffset": 8730, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3244, "endOffset": 3817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3919, "endOffset": 4439, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4543, "endOffset": 5021, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5143, "endOffset": 5666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5768, "endOffset": 6293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6401, "endOffset": 6935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7039, "endOffset": 7420, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7527, "endOffset": 7884, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7987, "endOffset": 8724, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8792, "endOffset": 12468, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8891, "endOffset": 9034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9132, "endOffset": 9288, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9403, "endOffset": 9611, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9712, "endOffset": 9903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9992, "endOffset": 10190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10279, "endOffset": 10480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10599, "endOffset": 10829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10959, "endOffset": 11202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11319, "endOffset": 11789, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11888, "endOffset": 12133, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12244, "endOffset": 12462, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12529, "endOffset": 17311, "count": 1}], "isBlockCoverage": true}, {"functionName": "createFileEntry", "ranges": [{"startOffset": 12693, "endOffset": 12788, "count": 17}], "isBlockCoverage": true}, {"functionName": "fullpath", "ranges": [{"startOffset": 12735, "endOffset": 12749, "count": 35}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12878, "endOffset": 14026, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13673, "endOffset": 13692, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14113, "endOffset": 14678, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14591, "endOffset": 14610, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14767, "endOffset": 15253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15153, "endOffset": 15172, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15319, "endOffset": 15509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15616, "endOffset": 16000, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15946, "endOffset": 15965, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16133, "endOffset": 16651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16455, "endOffset": 16474, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16545, "endOffset": 16564, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16738, "endOffset": 17307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17216, "endOffset": 17235, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "318", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/glob.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 7}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 21}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1133, "endOffset": 1734, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1262, "endOffset": 1706, "count": 25}, {"startOffset": 1303, "endOffset": 1307, "count": 0}, {"startOffset": 1338, "endOffset": 1342, "count": 0}, {"startOffset": 1494, "endOffset": 1506, "count": 15}, {"startOffset": 1508, "endOffset": 1545, "count": 12}, {"startOffset": 1545, "endOffset": 1702, "count": 13}, {"startOffset": 1566, "endOffset": 1590, "count": 3}, {"startOffset": 1590, "endOffset": 1702, "count": 10}, {"startOffset": 1611, "endOffset": 1634, "count": 2}, {"startOffset": 1634, "endOffset": 1702, "count": 8}], "isBlockCoverage": true}, {"functionName": "GlobTool", "ranges": [{"startOffset": 1943, "endOffset": 3299, "count": 21}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3302, "endOffset": 3322, "count": 1}], "isBlockCoverage": true}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 3390, "endOffset": 3924, "count": 17}, {"startOffset": 3762, "endOffset": 3778, "count": 0}, {"startOffset": 3878, "endOffset": 3919, "count": 6}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 3983, "endOffset": 5237, "count": 20}, {"startOffset": 4145, "endOffset": 4318, "count": 3}, {"startOffset": 4318, "endOffset": 4433, "count": 17}, {"startOffset": 4433, "endOffset": 4439, "count": 11}, {"startOffset": 4494, "endOffset": 4624, "count": 1}, {"startOffset": 4624, "endOffset": 4666, "count": 16}, {"startOffset": 4666, "endOffset": 4687, "count": 0}, {"startOffset": 4763, "endOffset": 4830, "count": 1}, {"startOffset": 4830, "endOffset": 4908, "count": 15}, {"startOffset": 4908, "endOffset": 4980, "count": 1}, {"startOffset": 4987, "endOffset": 5054, "count": 0}, {"startOffset": 5054, "endOffset": 5080, "count": 14}, {"startOffset": 5080, "endOffset": 5117, "count": 13}, {"startOffset": 5118, "endOffset": 5149, "count": 13}, {"startOffset": 5151, "endOffset": 5215, "count": 2}, {"startOffset": 5215, "endOffset": 5236, "count": 12}], "isBlockCoverage": true}, {"functionName": "getDescription", "ranges": [{"startOffset": 5299, "endOffset": 5714, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 5785, "endOffset": 9073, "count": 9}, {"startOffset": 5903, "endOffset": 6059, "count": 0}, {"startOffset": 6190, "endOffset": 6196, "count": 8}, {"startOffset": 7491, "endOffset": 7822, "count": 1}, {"startOffset": 7632, "endOffset": 7712, "count": 0}, {"startOffset": 7822, "endOffset": 8416, "count": 8}, {"startOffset": 8416, "endOffset": 8509, "count": 0}, {"startOffset": 8509, "endOffset": 8732, "count": 8}, {"startOffset": 8732, "endOffset": 9069, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6863, "endOffset": 6942, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7157, "endOffset": 7224, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7289, "endOffset": 7343, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8117, "endOffset": 8144, "count": 14}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "339", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/schemaValidator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 20}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 609, "endOffset": 1607, "count": 20}, {"startOffset": 840, "endOffset": 936, "count": 1}, {"startOffset": 944, "endOffset": 1018, "count": 19}, {"startOffset": 1020, "endOffset": 1585, "count": 19}, {"startOffset": 1154, "endOffset": 1579, "count": 73}, {"startOffset": 1192, "endOffset": 1204, "count": 30}, {"startOffset": 1206, "endOffset": 1571, "count": 30}, {"startOffset": 1307, "endOffset": 1316, "count": 0}, {"startOffset": 1383, "endOffset": 1561, "count": 2}, {"startOffset": 1579, "endOffset": 1585, "count": 17}, {"startOffset": 1585, "endOffset": 1606, "count": 17}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "340", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 21}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 40}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "341", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/paths.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 420, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 554, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 595, "endOffset": 639, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 869, "endOffset": 920, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1332, "endOffset": 1519, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1521, "endOffset": 3068, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 3070, "endOffset": 3424, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 3426, "endOffset": 3689, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 3691, "endOffset": 3766, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 3768, "endOffset": 3895, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 3897, "endOffset": 4101, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "342", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/core/arienRequest.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1365, "count": 1}, {"startOffset": 500, "endOffset": 1364, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "344", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/services/fileDiscoveryService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 9}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 799, "endOffset": 864, "count": 9}], "isBlockCoverage": true}, {"functionName": "FileDiscoveryService", "ranges": [{"startOffset": 868, "endOffset": 1457, "count": 9}, {"startOffset": 1031, "endOffset": 1241, "count": 0}, {"startOffset": 1392, "endOffset": 1414, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterFiles", "ranges": [{"startOffset": 1532, "endOffset": 1923, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1659, "endOffset": 1917, "count": 14}, {"startOffset": 1749, "endOffset": 1780, "count": 0}, {"startOffset": 1819, "endOffset": 1858, "count": 0}, {"startOffset": 1860, "endOffset": 1891, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldGitIgnoreFile", "ranges": [{"startOffset": 1989, "endOffset": 2136, "count": 14}, {"startOffset": 2051, "endOffset": 2113, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldArienIgnoreFile", "ranges": [{"startOffset": 2204, "endOffset": 2357, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArienIgnorePatterns", "ranges": [{"startOffset": 2419, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "345", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitIgnoreParser.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 9}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 770, "endOffset": 842, "count": 9}], "isBlockCoverage": true}, {"functionName": "GitIgnore<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 846, "endOffset": 943, "count": 9}], "isBlockCoverage": true}, {"functionName": "loadGitRepoPatterns", "ranges": [{"startOffset": 946, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPatterns", "ranges": [{"startOffset": 1254, "endOffset": 1682, "count": 9}, {"startOffset": 1528, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1583, "endOffset": 1598, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1644, "count": 0}], "isBlockCoverage": false}, {"functionName": "addPatterns", "ranges": [{"startOffset": 1685, "endOffset": 1776, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIgnored", "ranges": [{"startOffset": 1779, "endOffset": 2241, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPatterns", "ranges": [{"startOffset": 2244, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "347", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/gitUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 9}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 609, "endOffset": 1118, "count": 9}, {"startOffset": 735, "endOffset": 1054, "count": 63}, {"startOffset": 858, "endOffset": 888, "count": 0}, {"startOffset": 993, "endOffset": 1017, "count": 9}, {"startOffset": 1017, "endOffset": 1054, "count": 54}, {"startOffset": 1078, "endOffset": 1116, "count": 0}], "isBlockCoverage": true}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1120, "endOffset": 1629, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}