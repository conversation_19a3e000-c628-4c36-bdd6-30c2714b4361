{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/usePhraseCycler.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 984, "endOffset": 6836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1031, "endOffset": 1088, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1129, "endOffset": 1188, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1303, "endOffset": 1545, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1371, "endOffset": 1432, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1652, "endOffset": 2047, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 1737, "endOffset": 1828, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2146, "endOffset": 2543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2214, "endOffset": 2275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2312, "endOffset": 2430, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2653, "endOffset": 3521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2721, "endOffset": 2781, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2974, "endOffset": 3092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3284, "endOffset": 3402, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3652, "endOffset": 5260, "count": 1}, {"startOffset": 3724, "endOffset": 3745, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3840, "endOffset": 3976, "count": 3}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 4056, "endOffset": 4147, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4576, "endOffset": 4694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5080, "endOffset": 5147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5349, "endOffset": 5664, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5418, "endOffset": 5478, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5774, "endOffset": 6832, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 5859, "endOffset": 5950, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6210, "endOffset": 6328, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/usePhraseCycler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 18}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 428, "count": 5}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 474, "endOffset": 523, "count": 25}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "usePhraseCycler", "ranges": [{"startOffset": 6464, "endOffset": 7975, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6725, "endOffset": 7917, "count": 13}, {"startOffset": 6752, "endOffset": 6965, "count": 2}, {"startOffset": 6858, "endOffset": 6959, "count": 0}, {"startOffset": 6965, "endOffset": 7747, "count": 11}, {"startOffset": 6985, "endOffset": 7537, "count": 7}, {"startOffset": 7024, "endOffset": 7083, "count": 0}, {"startOffset": 7537, "endOffset": 7747, "count": 4}, {"startOffset": 7582, "endOffset": 7683, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7312, "endOffset": 7502, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7760, "endOffset": 7912, "count": 13}, {"startOffset": 7805, "endOffset": 7906, "count": 7}], "isBlockCoverage": true}], "startOffset": 209}]}