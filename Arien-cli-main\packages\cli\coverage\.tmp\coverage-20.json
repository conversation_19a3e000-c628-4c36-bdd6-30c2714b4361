{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/SessionContext.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27761, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27761, "count": 1}], "isBlockCoverage": true}, {"functionName": "TestHarness", "ranges": [{"startOffset": 1853, "endOffset": 1961, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2021, "endOffset": 10895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2104, "endOffset": 3263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3355, "endOffset": 4450, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4106, "endOffset": 4161, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4550, "endOffset": 6050, "count": 1}, {"startOffset": 5550, "endOffset": 5554, "count": 0}, {"startOffset": 5678, "endOffset": 5682, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5301, "endOffset": 5388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6154, "endOffset": 8600, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6907, "endOffset": 6962, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6999, "endOffset": 7086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7123, "endOffset": 7209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8689, "endOffset": 10375, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9442, "endOffset": 9529, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9779, "endOffset": 9865, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10109, "endOffset": 10164, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10485, "endOffset": 10891, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10584, "endOffset": 10597, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10649, "endOffset": 10761, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10702, "endOffset": 10751, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1854", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/SessionContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 5}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 357, "endOffset": 406, "count": 15}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "addTokens", "ranges": [{"startOffset": 1025, "endOffset": 1485, "count": 15}, {"startOffset": 1108, "endOffset": 1112, "count": 0}, {"startOffset": 1171, "endOffset": 1175, "count": 0}, {"startOffset": 1228, "endOffset": 1232, "count": 0}, {"startOffset": 1273, "endOffset": 1277, "count": 0}, {"startOffset": 1332, "endOffset": 1336, "count": 0}, {"startOffset": 1405, "endOffset": 1409, "count": 0}, {"startOffset": 1478, "endOffset": 1482, "count": 0}], "isBlockCoverage": true}, {"functionName": "SessionStatsProvider", "ranges": [{"startOffset": 1516, "endOffset": 4605, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2468, "endOffset": 3263, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2499, "endOffset": 3255, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3338, "endOffset": 4110, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3359, "endOffset": 4104, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4172, "endOffset": 4252, "count": 13}], "isBlockCoverage": true}, {"functionName": "useSessionStats", "ranges": [{"startOffset": 4631, "endOffset": 4863, "count": 15}, {"startOffset": 4742, "endOffset": 4842, "count": 2}, {"startOffset": 4842, "endOffset": 4862, "count": 13}], "isBlockCoverage": true}], "startOffset": 209}]}