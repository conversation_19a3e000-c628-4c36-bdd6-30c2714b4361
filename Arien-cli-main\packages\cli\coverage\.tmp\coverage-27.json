{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useTimer.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 911, "endOffset": 5572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 958, "endOffset": 1015, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1056, "endOffset": 1115, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1177, "endOffset": 1363, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1245, "endOffset": 1295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1447, "endOffset": 1740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1515, "endOffset": 1565, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1602, "endOffset": 1672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1832, "endOffset": 2293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1900, "endOffset": 1949, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1986, "endOffset": 2056, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2155, "endOffset": 2225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2409, "endOffset": 2985, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 2494, "endOffset": 2576, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2847, "endOffset": 2917, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3075, "endOffset": 3757, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 3160, "endOffset": 3242, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3341, "endOffset": 3411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3619, "endOffset": 3689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3859, "endOffset": 4267, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 3944, "endOffset": 4026, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4334, "endOffset": 4638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4403, "endOffset": 4452, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4776, "endOffset": 5568, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 4861, "endOffset": 4943, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5042, "endOffset": 5112, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5430, "endOffset": 5500, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useTimer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 22}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTimer", "ranges": [{"startOffset": 600, "endOffset": 1828, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 942, "endOffset": 1780, "count": 13}, {"startOffset": 1029, "endOffset": 1108, "count": 2}, {"startOffset": 1152, "endOffset": 1163, "count": 7}, {"startOffset": 1165, "endOffset": 1202, "count": 2}, {"startOffset": 1229, "endOffset": 1261, "count": 4}, {"startOffset": 1321, "endOffset": 1512, "count": 7}, {"startOffset": 1351, "endOffset": 1401, "count": 0}, {"startOffset": 1512, "endOffset": 1637, "count": 6}, {"startOffset": 1548, "endOffset": 1631, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1440, "endOffset": 1499, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1471, "endOffset": 1489, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1650, "endOffset": 1775, "count": 13}, {"startOffset": 1686, "endOffset": 1769, "count": 7}], "isBlockCoverage": true}], "startOffset": 209}]}