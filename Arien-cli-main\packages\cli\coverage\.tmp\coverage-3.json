{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/HistoryItemDisplay.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11629, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11629, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 481, "endOffset": 771, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToolGroupMessage", "ranges": [{"startOffset": 510, "endOffset": 768, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1447, "endOffset": 4887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1628, "endOffset": 2199, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2274, "endOffset": 3140, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3211, "endOffset": 3965, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4048, "endOffset": 4883, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/HistoryItemDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 4}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "HistoryItemDisplay", "ranges": [{"startOffset": 2386, "endOffset": 7334, "count": 4}, {"startOffset": 2631, "endOffset": 2940, "count": 1}, {"startOffset": 2971, "endOffset": 3285, "count": 0}, {"startOffset": 3311, "endOffset": 3735, "count": 0}, {"startOffset": 3769, "endOffset": 4200, "count": 0}, {"startOffset": 4225, "endOffset": 4534, "count": 0}, {"startOffset": 4560, "endOffset": 4870, "count": 0}, {"startOffset": 4896, "endOffset": 5449, "count": 1}, {"startOffset": 5475, "endOffset": 5905, "count": 1}, {"startOffset": 5930, "endOffset": 6276, "count": 1}, {"startOffset": 6307, "endOffset": 6779, "count": 0}, {"startOffset": 6811, "endOffset": 7140, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/UserMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "UserMessage", "ranges": [{"startOffset": 895, "endOffset": 2701, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 43}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 30}], "isBlockCoverage": true}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 4}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 1}], "isBlockCoverage": true}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 2}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 2}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 43}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2411, "endOffset": 2441, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 43}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2331, "endOffset": 2497, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2657, "endOffset": 6704, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11915, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11617, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/UserShellMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "UserShellMessage", "ranges": [{"startOffset": 910, "endOffset": 2012, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1578", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienMessage", "ranges": [{"startOffset": 1165, "endOffset": 3123, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1579", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/MarkdownDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapTextAtWordBoundaries", "ranges": [{"startOffset": 1582, "endOffset": 2463, "count": 0}], "isBlockCoverage": false}, {"functionName": "MarkdownDisplayInternal", "ranges": [{"startOffset": 2497, "endOffset": 13454, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderInlineInternal", "ranges": [{"startOffset": 13485, "endOffset": 20366, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderCodeBlockInternal", "ranges": [{"startOffset": 20479, "endOffset": 23477, "count": 0}], "isBlockCoverage": false}, {"functionName": "RenderListItemInternal", "ranges": [{"startOffset": 23595, "endOffset": 25863, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1580", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/CodeColorizer.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderHastNode", "ranges": [{"startOffset": 1534, "endOffset": 3600, "count": 0}], "isBlockCoverage": false}, {"functionName": "colorizeCode", "ranges": [{"startOffset": 3601, "endOffset": 8195, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1781", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45623, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 0}], "isBlockCoverage": false}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1813, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1884, "endOffset": 2611, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2632, "endOffset": 7251, "count": 0}], "isBlockCoverage": false}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 7253, "endOffset": 10048, "count": 0}], "isBlockCoverage": false}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 10049, "endOffset": 14127, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1782", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1783", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1212, "endOffset": 1276, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1305, "endOffset": 1371, "count": 0}], "isBlockCoverage": false}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1398, "endOffset": 3012, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1784", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/AnimatedIcon.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "AnimatedIcon", "ranges": [{"startOffset": 1088, "endOffset": 2004, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1785", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/InfoMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "InfoMessage", "ranges": [{"startOffset": 895, "endOffset": 3045, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1786", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ErrorMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7971, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7971, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "ErrorMessage", "ranges": [{"startOffset": 898, "endOffset": 4482, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1787", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/ArienMessageContent.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArienMessageContent", "ranges": [{"startOffset": 937, "endOffset": 2055, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1788", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/messages/CompressionMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "CompressionMessage", "ranges": [{"startOffset": 1251, "endOffset": 3681, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1791", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/AboutBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "AboutBox", "ranges": [{"startOffset": 1156, "endOffset": 15046, "count": 1}, {"startOffset": 12471, "endOffset": 12480, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1792", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/generated/git-commit.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 3}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1793", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/shared/Separator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 335, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 431, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "Separator", "ranges": [{"startOffset": 1081, "endOffset": 1853, "count": 1}], "isBlockCoverage": true}, {"functionName": "DottedSeparator", "ranges": [{"startOffset": 1879, "endOffset": 2723, "count": 0}], "isBlockCoverage": false}, {"functionName": "SectionSeparator", "ranges": [{"startOffset": 2750, "endOffset": 4381, "count": 1}, {"startOffset": 3209, "endOffset": 4380, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1794", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/StatsDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "StatsDisplay", "ranges": [{"startOffset": 1188, "endOffset": 6975, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1795", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5330, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 3}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1565, "count": 3}, {"startOffset": 850, "endOffset": 872, "count": 0}, {"startOffset": 937, "endOffset": 1564, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1796", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/Stats.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 5}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 372, "count": 3}, {"startOffset": 362, "endOffset": 370, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 417, "endOffset": 465, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatRow", "ranges": [{"startOffset": 1063, "endOffset": 2054, "count": 23}], "isBlockCoverage": true}, {"functionName": "StatsColumn", "ranges": [{"startOffset": 2076, "endOffset": 6609, "count": 3}, {"startOffset": 2176, "endOffset": 2200, "count": 2}, {"startOffset": 2201, "endOffset": 2307, "count": 2}, {"startOffset": 2308, "endOffset": 2345, "count": 1}, {"startOffset": 2382, "endOffset": 2407, "count": 2}, {"startOffset": 2408, "endOffset": 2450, "count": 2}, {"startOffset": 2451, "endOffset": 2459, "count": 1}], "isBlockCoverage": true}, {"functionName": "DurationColumn", "ranges": [{"startOffset": 6634, "endOffset": 8198, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1797", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/components/SessionSummaryDisplay.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "SessionSummaryDisplay", "ranges": [{"startOffset": 1382, "endOffset": 8525, "count": 1}, {"startOffset": 3837, "endOffset": 5014, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1819", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 392, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 572, "count": 3}, {"startOffset": 562, "endOffset": 570, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}