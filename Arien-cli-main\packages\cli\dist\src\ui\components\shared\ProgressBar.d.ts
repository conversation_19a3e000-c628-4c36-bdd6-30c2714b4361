/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
interface ProgressBarProps {
    progress: number;
    width?: number;
    showPercentage?: boolean;
    color?: string;
    backgroundColor?: string;
    character?: string;
}
export declare const ProgressBar: React.FC<ProgressBarProps>;
interface IndeterminateProgressProps {
    width?: number;
    color?: string;
    animationFrame?: number;
}
export declare const IndeterminateProgress: React.FC<IndeterminateProgressProps>;
export {};
