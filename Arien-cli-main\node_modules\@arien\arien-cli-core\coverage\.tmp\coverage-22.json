{"result": [{"scriptId": "315", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/test-setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1236, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "316", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/utils/testUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 587, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 842, "count": 1}, {"startOffset": 832, "endOffset": 840, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldSimulate429", "ranges": [{"startOffset": 1081, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetRequestCounter", "ranges": [{"startOffset": 1421, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "disableSimulationAfterFallback", "ranges": [{"startOffset": 1479, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSimulated429Error", "ranges": [{"startOffset": 1553, "endOffset": 1691, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetSimulationState", "ranges": [{"startOffset": 1693, "endOffset": 1781, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSimulate429", "ranges": [{"startOffset": 1783, "endOffset": 2020, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "317", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 930, "endOffset": 11487, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1196, "endOffset": 1469, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1510, "endOffset": 1651, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1713, "endOffset": 2962, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1828, "endOffset": 2034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2153, "endOffset": 2537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2612, "endOffset": 2956, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3043, "endOffset": 7952, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3326, "endOffset": 4185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4279, "endOffset": 4701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4781, "endOffset": 5424, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5510, "endOffset": 6128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6235, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6997, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7548, "endOffset": 7946, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8021, "endOffset": 11483, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8124, "endOffset": 8334, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8436, "endOffset": 9000, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9116, "endOffset": 10126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10205, "endOffset": 10726, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10813, "endOffset": 11477, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "319", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/memoryTool.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 416, "count": 15}, {"startOffset": 406, "endOffset": 414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 572, "endOffset": 624, "count": 18}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 739, "count": 6}, {"startOffset": 729, "endOffset": 737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 792, "endOffset": 848, "count": 1}, {"startOffset": 838, "endOffset": 846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 933, "count": 15}, {"startOffset": 923, "endOffset": 931, "count": 0}], "isBlockCoverage": true}, {"functionName": "setArienMdFilename", "ranges": [{"startOffset": 3261, "endOffset": 3562, "count": 18}, {"startOffset": 3338, "endOffset": 3454, "count": 1}, {"startOffset": 3454, "endOffset": 3560, "count": 17}, {"startOffset": 3476, "endOffset": 3504, "count": 16}, {"startOffset": 3506, "endOffset": 3560, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3421, "endOffset": 3442, "count": 2}], "isBlockCoverage": true}, {"functionName": "getCurrentArienMdFilename", "ranges": [{"startOffset": 3564, "endOffset": 3727, "count": 8}, {"startOffset": 3648, "endOffset": 3691, "count": 1}, {"startOffset": 3691, "endOffset": 3726, "count": 7}], "isBlockCoverage": true}, {"functionName": "getAllArienMdFilenames", "ranges": [{"startOffset": 3729, "endOffset": 3888, "count": 1}, {"startOffset": 3850, "endOffset": 3887, "count": 0}], "isBlockCoverage": true}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 3890, "endOffset": 4049, "count": 2}], "isBlockCoverage": true}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4050, "endOffset": 4347, "count": 3}, {"startOffset": 4146, "endOffset": 4223, "count": 0}, {"startOffset": 4229, "endOffset": 4310, "count": 0}, {"startOffset": 4316, "endOffset": 4346, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 4435, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 4478, "endOffset": 4626, "count": 4}], "isBlockCoverage": true}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 4636, "endOffset": 6391, "count": 7}, {"startOffset": 5062, "endOffset": 5071, "count": 6}, {"startOffset": 5071, "endOffset": 5091, "count": 1}, {"startOffset": 5189, "endOffset": 5335, "count": 3}, {"startOffset": 5335, "endOffset": 6034, "count": 4}, {"startOffset": 5544, "endOffset": 5601, "count": 3}, {"startOffset": 6102, "endOffset": 6109, "count": 6}, {"startOffset": 6109, "endOffset": 6387, "count": 1}, {"startOffset": 6355, "endOffset": 6370, "count": 0}], "isBlockCoverage": true}, {"functionName": "execute", "ranges": [{"startOffset": 6394, "endOffset": 7669, "count": 3}, {"startOffset": 6521, "endOffset": 6752, "count": 1}, {"startOffset": 6752, "endOffset": 6998, "count": 2}, {"startOffset": 6998, "endOffset": 7665, "count": 1}, {"startOffset": 7299, "endOffset": 7314, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "320", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/core/src/tools/tools.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseTool", "ranges": [{"startOffset": 973, "endOffset": 1317, "count": 4}], "isBlockCoverage": true}, {"functionName": "get schema", "ranges": [{"startOffset": 1418, "endOffset": 1555, "count": 3}], "isBlockCoverage": true}, {"functionName": "validateToolParams", "ranges": [{"startOffset": 1991, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDescription", "ranges": [{"startOffset": 2306, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldConfirmExecute", "ranges": [{"startOffset": 2583, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2716, "endOffset": 3178, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}