/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { WebSocketClientTransport } from '@modelcontextprotocol/sdk/client/websocket.js';
import { parse } from 'shell-quote';
import { MCPServerConfig } from '../config/config.js';
import { DiscoveredMCPTool } from './mcp-tool.js';
import { mcpToTool, } from '@google/genai';
export const MCP_DEFAULT_TIMEOUT_MSEC = 10 * 60 * 1000; // default to 10 minutes
/**
 * Enum representing the connection status of an MCP server
 */
export var MCPServerStatus;
(function (MCPServerStatus) {
    /** Server is disconnected or experiencing errors */
    MCPServerStatus["DISCONNECTED"] = "disconnected";
    /** Server is in the process of connecting */
    MCPServerStatus["CONNECTING"] = "connecting";
    /** Server is connected and ready to use */
    MCPServerStatus["CONNECTED"] = "connected";
})(MCPServerStatus || (MCPServerStatus = {}));
/**
 * Enum representing the overall MCP discovery state
 */
export var MCPDiscoveryState;
(function (MCPDiscoveryState) {
    /** Discovery has not started yet */
    MCPDiscoveryState["NOT_STARTED"] = "not_started";
    /** Discovery is currently in progress */
    MCPDiscoveryState["IN_PROGRESS"] = "in_progress";
    /** Discovery has completed (with or without errors) */
    MCPDiscoveryState["COMPLETED"] = "completed";
})(MCPDiscoveryState || (MCPDiscoveryState = {}));
/**
 * Map to track the status of each MCP server within the core package
 */
const mcpServerStatusesInternal = new Map();
/**
 * Track the overall MCP discovery state
 */
let mcpDiscoveryState = MCPDiscoveryState.NOT_STARTED;
const statusChangeListeners = [];
/**
 * Add a listener for MCP server status changes
 */
export function addMCPStatusChangeListener(listener) {
    statusChangeListeners.push(listener);
}
/**
 * Remove a listener for MCP server status changes
 */
export function removeMCPStatusChangeListener(listener) {
    const index = statusChangeListeners.indexOf(listener);
    if (index !== -1) {
        statusChangeListeners.splice(index, 1);
    }
}
/**
 * Update the status of an MCP server
 */
function updateMCPServerStatus(serverName, status) {
    mcpServerStatusesInternal.set(serverName, status);
    // Notify all listeners
    for (const listener of statusChangeListeners) {
        listener(serverName, status);
    }
}
/**
 * Get the current status of an MCP server
 */
export function getMCPServerStatus(serverName) {
    return (mcpServerStatusesInternal.get(serverName) || MCPServerStatus.DISCONNECTED);
}
/**
 * Get all MCP server statuses
 */
export function getAllMCPServerStatuses() {
    return new Map(mcpServerStatusesInternal);
}
/**
 * Get the current MCP discovery state
 */
export function getMCPDiscoveryState() {
    return mcpDiscoveryState;
}
export async function discoverMcpTools(mcpServers, mcpServerCommand, toolRegistry) {
    // Set discovery state to in progress
    mcpDiscoveryState = MCPDiscoveryState.IN_PROGRESS;
    try {
        if (mcpServerCommand) {
            const cmd = mcpServerCommand;
            const args = parse(cmd, process.env);
            if (args.some((arg) => typeof arg !== 'string')) {
                throw new Error('failed to parse mcpServerCommand: ' + cmd);
            }
            // use generic server name 'mcp'
            mcpServers['mcp'] = {
                command: args[0],
                args: args.slice(1),
            };
        }
        const discoveryPromises = Object.entries(mcpServers).map(([mcpServerName, mcpServerConfig]) => connectAndDiscover(mcpServerName, mcpServerConfig, toolRegistry));
        // Use Promise.allSettled to allow graceful degradation
        const results = await Promise.allSettled(discoveryPromises);
        // Log results and count successes/failures
        let successCount = 0;
        let failureCount = 0;
        const failedServers = [];
        results.forEach((result, index) => {
            const serverName = Object.keys(mcpServers)[index];
            if (result.status === 'fulfilled') {
                successCount++;
                console.debug(`✅ MCP server '${serverName}' connected successfully`);
            }
            else {
                failureCount++;
                failedServers.push(serverName);
                console.warn(`❌ MCP server '${serverName}' failed to connect: ${result.reason}`);
            }
        });
        // Provide summary of MCP server initialization
        if (successCount > 0) {
            console.log(`🔌 MCP servers initialized: ${successCount} successful, ${failureCount} failed`);
            if (failedServers.length > 0) {
                console.log(`   Failed servers: ${failedServers.join(', ')}`);
                console.log(`   The application will continue with available servers.`);
                // Enhanced debug logging for failed servers
                if (process.argv.includes('--debug') || process.argv.includes('-d')) {
                    console.debug('\n🔍 Debug information for failed MCP servers:');
                    failedServers.forEach(serverName => {
                        const status = getMCPServerStatus(serverName);
                        console.debug(`   • ${serverName}: Status = ${status}`);
                    });
                }
            }
        }
        else if (failureCount > 0) {
            console.warn(`⚠️ All ${failureCount} MCP servers failed to connect. The application will continue with built-in tools only.`);
            // Enhanced debug logging for complete failure
            if (process.argv.includes('--debug') || process.argv.includes('-d')) {
                console.debug('\n🔍 Debug information: All MCP servers failed to initialize');
                console.debug('   This may indicate:');
                console.debug('   • Missing dependencies (uvx, npx, git)');
                console.debug('   • Network connectivity issues');
                console.debug('   • Configuration problems');
                console.debug('   • Package installation failures');
            }
        }
        // Mark discovery as completed regardless of individual server failures
        mcpDiscoveryState = MCPDiscoveryState.COMPLETED;
    }
    catch (error) {
        // Still mark as completed even with errors
        mcpDiscoveryState = MCPDiscoveryState.COMPLETED;
        console.error(`Unexpected error during MCP server discovery: ${error}`);
        // Don't throw the error - allow the application to continue
    }
}
async function connectAndDiscover(mcpServerName, mcpServerConfig, toolRegistry) {
    return await connectAndDiscoverWithFallback(mcpServerName, mcpServerConfig, toolRegistry, false);
}
async function connectAndDiscoverWithFallback(mcpServerName, mcpServerConfig, toolRegistry, isRetryAttempt = false) {
    // Initialize the server status as connecting
    updateMCPServerStatus(mcpServerName, MCPServerStatus.CONNECTING);
    // Enhanced debug logging for connection attempts
    if (process.argv.includes('--debug') || process.argv.includes('-d')) {
        const attemptType = isRetryAttempt ? 'fallback' : 'primary';
        console.debug(`🔌 Attempting ${attemptType} connection to MCP server '${mcpServerName}'`);
        console.debug(`   Command: ${mcpServerConfig.command}`);
        console.debug(`   Args: ${JSON.stringify(mcpServerConfig.args)}`);
        console.debug(`   Timeout: ${mcpServerConfig.timeout || 'default'}`);
        console.debug(`   Working directory: ${mcpServerConfig.cwd || 'default'}`);
    }
    let transport;
    if (mcpServerConfig.httpUrl) {
        transport = new StreamableHTTPClientTransport(new URL(mcpServerConfig.httpUrl));
    }
    else if (mcpServerConfig.url) {
        transport = new SSEClientTransport(new URL(mcpServerConfig.url));
    }
    else if (mcpServerConfig.tcp) {
        transport = new WebSocketClientTransport(new URL(mcpServerConfig.tcp));
    }
    else if (mcpServerConfig.command) {
        transport = new StdioClientTransport({
            command: mcpServerConfig.command,
            args: mcpServerConfig.args || [],
            env: {
                ...process.env,
                ...(mcpServerConfig.env || {}),
            },
            cwd: mcpServerConfig.cwd,
            stderr: 'pipe',
        });
    }
    else {
        console.error(`MCP server '${mcpServerName}' has invalid configuration: missing httpUrl (for Streamable HTTP), url (for SSE), tcp (for WebSocket), and command (for stdio). Skipping.`);
        // Update status to disconnected
        updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
        return;
    }
    let mcpClient = new Client({
        name: 'arien-cli-mcp-client',
        version: '0.0.1',
    });
    // patch Client.callTool to use request timeout as genai McpCallTool.callTool does not do it
    // TODO: remove this hack once GenAI SDK does callTool with request options
    if ('callTool' in mcpClient) {
        const origCallTool = mcpClient.callTool.bind(mcpClient);
        mcpClient.callTool = function (params, resultSchema, options) {
            return origCallTool(params, resultSchema, {
                ...options,
                timeout: mcpServerConfig.timeout ?? MCP_DEFAULT_TIMEOUT_MSEC,
            });
        };
    }
    // Add a small delay for stdio transports to help with timing issues
    if (mcpServerConfig.command) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    try {
        await mcpClient.connect(transport, {
            timeout: mcpServerConfig.timeout ?? MCP_DEFAULT_TIMEOUT_MSEC,
        });
        // Connection successful
        updateMCPServerStatus(mcpServerName, MCPServerStatus.CONNECTED);
    }
    catch (error) {
        // For stdio transport, try a retry with a small delay for timing issues
        if (mcpServerConfig.command && String(error).includes('Connection closed')) {
            console.debug(`MCP server '${mcpServerName}' connection closed, attempting retry...`);
            try {
                // Wait a bit and try again
                await new Promise(resolve => setTimeout(resolve, 1000));
                // Create a new transport for the retry
                const retryTransport = new StdioClientTransport({
                    command: mcpServerConfig.command,
                    args: mcpServerConfig.args || [],
                    env: {
                        ...process.env,
                        ...(mcpServerConfig.env || {}),
                    },
                    cwd: mcpServerConfig.cwd,
                    stderr: 'pipe',
                });
                const retryClient = new Client({
                    name: 'arien-cli-mcp-client',
                    version: '0.0.1',
                });
                await retryClient.connect(retryTransport, {
                    timeout: mcpServerConfig.timeout ?? MCP_DEFAULT_TIMEOUT_MSEC,
                });
                // Retry successful, replace the original client and transport
                transport = retryTransport;
                mcpClient = retryClient;
                updateMCPServerStatus(mcpServerName, MCPServerStatus.CONNECTED);
                console.debug(`MCP server '${mcpServerName}' retry connection successful`);
            }
            catch (retryError) {
                console.debug(`MCP server '${mcpServerName}' retry failed: ${retryError}`);
                // Fall through to original error handling
            }
        }
        // If we're still not connected, try fallback configurations
        if (getMCPServerStatus(mcpServerName) !== MCPServerStatus.CONNECTED) {
            const fallbackConfig = await tryGetFallbackConfiguration(mcpServerName, mcpServerConfig, error);
            if (fallbackConfig) {
                console.debug(`MCP server '${mcpServerName}' attempting fallback configuration...`);
                try {
                    // Recursively try with the fallback configuration
                    await connectAndDiscoverWithFallback(mcpServerName, fallbackConfig, toolRegistry, true);
                    return; // Success with fallback, exit early
                }
                catch (fallbackError) {
                    console.debug(`MCP server '${mcpServerName}' fallback failed: ${fallbackError}`);
                    // Continue to original error handling
                }
            }
        }
        // If we're still not connected, handle the error
        if (getMCPServerStatus(mcpServerName) !== MCPServerStatus.CONNECTED) {
            // Create a safe config object that excludes sensitive information
            const safeConfig = {
                command: mcpServerConfig.command,
                url: mcpServerConfig.url,
                cwd: mcpServerConfig.cwd,
                timeout: mcpServerConfig.timeout,
                trust: mcpServerConfig.trust,
                // Exclude args and env which may contain sensitive data
            };
            // Provide helpful error messages based on the error type
            let errorString = `failed to start or connect to MCP server '${mcpServerName}' ${JSON.stringify(safeConfig)}`;
            let troubleshootingTips = [];
            // Analyze the error to provide specific guidance
            const errorMessage = String(error).toLowerCase();
            if (errorMessage.includes('command not found') || errorMessage.includes('is not recognized')) {
                if (mcpServerConfig.command === 'uvx') {
                    troubleshootingTips.push('The "uvx" command is not installed. Install uv from: https://docs.astral.sh/uv/getting-started/installation/');
                    troubleshootingTips.push('Alternative: This server may have an npx-based fallback available');
                }
                else if (mcpServerConfig.command === 'npx') {
                    troubleshootingTips.push('The "npx" command is not installed. Install Node.js from: https://nodejs.org/');
                }
                else {
                    troubleshootingTips.push(`The "${mcpServerConfig.command}" command is not installed or not in PATH`);
                }
            }
            else if (errorMessage.includes('connection closed') || errorMessage.includes('econnrefused')) {
                troubleshootingTips.push('The MCP server process started but closed unexpectedly');
                troubleshootingTips.push('This may indicate a missing dependency or configuration issue');
                if (mcpServerConfig.command === 'uvx') {
                    troubleshootingTips.push('Try installing the package manually: uvx --from mcp-server-git --help');
                }
            }
            else if (errorMessage.includes('timeout')) {
                troubleshootingTips.push('The MCP server took too long to start');
                troubleshootingTips.push('This may indicate network issues or slow package installation');
            }
            // Add general troubleshooting tips
            troubleshootingTips.push('Check that all required dependencies are installed');
            troubleshootingTips.push('Verify the server configuration in your settings');
            if (process.env.SANDBOX) {
                troubleshootingTips.push('Make sure the server is available in the sandbox environment');
            }
            errorString += `\nError: ${error}`;
            if (troubleshootingTips.length > 0) {
                errorString += '\n\nTroubleshooting tips:';
                troubleshootingTips.forEach(tip => {
                    errorString += `\n  • ${tip}`;
                });
            }
            console.error(errorString);
            // Update status to disconnected
            updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
            return;
        }
    }
    mcpClient.onerror = (error) => {
        console.error(`MCP ERROR (${mcpServerName}):`, error.toString());
        // Update status to disconnected on error
        updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
    };
    if (transport instanceof StdioClientTransport && transport.stderr) {
        transport.stderr.on('data', (data) => {
            const stderrStr = data.toString();
            // Filter out verbose INFO logs from some MCP servers
            if (!stderrStr.includes('] INFO')) {
                console.debug(`MCP STDERR (${mcpServerName}):`, stderrStr);
            }
        });
    }
    try {
        const mcpCallableTool = mcpToTool(mcpClient);
        const discoveredToolFunctions = await mcpCallableTool.tool();
        if (!discoveredToolFunctions ||
            !Array.isArray(discoveredToolFunctions.functionDeclarations)) {
            console.error(`MCP server '${mcpServerName}' did not return valid tool function declarations. Skipping.`);
            if (transport instanceof StdioClientTransport ||
                transport instanceof SSEClientTransport ||
                transport instanceof StreamableHTTPClientTransport) {
                await transport.close();
            }
            // Update status to disconnected
            updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
            return;
        }
        for (const funcDecl of discoveredToolFunctions.functionDeclarations) {
            if (!funcDecl.name) {
                console.warn(`Discovered a function declaration without a name from MCP server '${mcpServerName}'. Skipping.`);
                continue;
            }
            let toolNameForModel = funcDecl.name;
            // Replace invalid characters (based on 400 error message from Arien AI API) with underscores
            toolNameForModel = toolNameForModel.replace(/[^a-zA-Z0-9_.-]/g, '_');
            const existingTool = toolRegistry.getTool(toolNameForModel);
            if (existingTool) {
                toolNameForModel = mcpServerName + '__' + toolNameForModel;
            }
            // If longer than 63 characters, replace middle with '___'
            // (Arien AI API says max length 64, but actual limit seems to be 63)
            if (toolNameForModel.length > 63) {
                toolNameForModel =
                    toolNameForModel.slice(0, 28) + '___' + toolNameForModel.slice(-32);
            }
            sanitizeParameters(funcDecl.parameters);
            // Ensure parameters is a valid JSON schema object, default to empty if not.
            const parameterSchema = funcDecl.parameters && typeof funcDecl.parameters === 'object'
                ? { ...funcDecl.parameters }
                : { type: 'object', properties: {} };
            toolRegistry.registerTool(new DiscoveredMCPTool(mcpCallableTool, mcpServerName, toolNameForModel, funcDecl.description ?? '', parameterSchema, funcDecl.name, mcpServerConfig.timeout ?? MCP_DEFAULT_TIMEOUT_MSEC, mcpServerConfig.trust));
        }
    }
    catch (error) {
        console.error(`Failed to list or register tools for MCP server '${mcpServerName}': ${error}`);
        // Ensure transport is cleaned up on error too
        if (transport instanceof StdioClientTransport ||
            transport instanceof SSEClientTransport ||
            transport instanceof StreamableHTTPClientTransport) {
            await transport.close();
        }
        // Update status to disconnected
        updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
    }
    // If no tools were registered from this MCP server, the following 'if' block
    // will close the connection. This is done to conserve resources and prevent
    // an orphaned connection to a server that isn't providing any usable
    // functionality. Connections to servers that did provide tools are kept
    // open, as those tools will require the connection to function.
    if (toolRegistry.getToolsByServer(mcpServerName).length === 0) {
        console.log(`No tools registered from MCP server '${mcpServerName}'. Closing connection.`);
        if (transport instanceof StdioClientTransport ||
            transport instanceof SSEClientTransport ||
            transport instanceof StreamableHTTPClientTransport) {
            await transport.close();
            // Update status to disconnected
            updateMCPServerStatus(mcpServerName, MCPServerStatus.DISCONNECTED);
        }
    }
}
export function sanitizeParameters(schema) {
    if (!schema) {
        return;
    }
    if (schema.anyOf) {
        // Vertex AI gets confused if both anyOf and default are set.
        schema.default = undefined;
        for (const item of schema.anyOf) {
            sanitizeParameters(item);
        }
    }
    if (schema.items) {
        sanitizeParameters(schema.items);
    }
    if (schema.properties) {
        for (const item of Object.values(schema.properties)) {
            sanitizeParameters(item);
        }
    }
}
/**
 * Attempts to get a fallback configuration for an MCP server when the primary configuration fails
 */
async function tryGetFallbackConfiguration(serverName, originalConfig, error) {
    const errorMessage = String(error).toLowerCase();
    // Enhanced debug logging for fallback attempts
    if (process.argv.includes('--debug') || process.argv.includes('-d')) {
        console.debug(`🔄 Attempting fallback for MCP server '${serverName}'`);
        console.debug(`   Original command: ${originalConfig.command}`);
        console.debug(`   Error: ${errorMessage}`);
    }
    // Only attempt fallbacks for uvx-related failures
    if (originalConfig.command !== 'uvx') {
        if (process.argv.includes('--debug') || process.argv.includes('-d')) {
            console.debug(`   No fallback available for command: ${originalConfig.command}`);
        }
        return null;
    }
    // Check if npx is available as a fallback
    try {
        const { exec } = await import('child_process');
        const { promisify } = await import('util');
        const execAsync = promisify(exec);
        await execAsync('npx --version', { timeout: 5000 });
    }
    catch (npxError) {
        console.debug('npx not available for fallback');
        return null;
    }
    // Special handling for Git server
    if (serverName === 'Git') {
        // Try multiple Git server options in order of preference
        const gitServerOptions = [
            {
                args: ['-y', '@cyanheads/git-mcp-server', '--repository', '.'],
                description: 'Git MCP server for version control operations (cyanheads fallback)'
            },
            {
                args: ['-y', '@modelcontextprotocol/server-git', '--repository', '.'],
                description: 'Git MCP server for version control operations (official fallback)'
            }
        ];
        // Return the first available option
        for (const option of gitServerOptions) {
            return new MCPServerConfig('npx', option.args, originalConfig.env, originalConfig.cwd || process.cwd(), undefined, undefined, undefined, 15000, // Longer timeout for package installation and startup
            originalConfig.trust, option.description);
        }
    }
    // Generic fallback for other uvx servers
    const fallbackMappings = {
        'MongoDB': {
            args: ['-y', '@modelcontextprotocol/server-mongodb'],
            description: 'MongoDB MCP server (npx fallback)'
        },
        'PostgreSQL': {
            args: ['-y', '@modelcontextprotocol/server-postgres'],
            description: 'PostgreSQL MCP server (npx fallback)'
        }
    };
    const fallback = fallbackMappings[serverName];
    if (fallback) {
        return new MCPServerConfig('npx', fallback.args, originalConfig.env, originalConfig.cwd || process.cwd(), undefined, undefined, undefined, 10000, originalConfig.trust, fallback.description);
    }
    return null;
}
//# sourceMappingURL=mcp-client.js.map