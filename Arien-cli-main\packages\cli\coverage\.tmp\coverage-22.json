{"result": [{"scriptId": "1026", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useConsoleMessages.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 5793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 775, "endOffset": 957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 843, "endOffset": 895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 993, "endOffset": 1466, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1061, "endOffset": 1113, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1244, "endOffset": 1305, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1342, "endOffset": 1380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1527, "endOffset": 2048, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1595, "endOffset": 1647, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1778, "endOffset": 1887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1924, "endOffset": 1962, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2101, "endOffset": 2772, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2169, "endOffset": 2221, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2454, "endOffset": 2565, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2602, "endOffset": 2640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2836, "endOffset": 3503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2904, "endOffset": 2956, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3185, "endOffset": 3296, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3333, "endOffset": 3371, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3544, "endOffset": 4148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3612, "endOffset": 3664, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3795, "endOffset": 3856, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3893, "endOffset": 3931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4028, "endOffset": 4086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4212, "endOffset": 4756, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4280, "endOffset": 4332, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4463, "endOffset": 4524, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4561, "endOffset": 4619, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4656, "endOffset": 4694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4818, "endOffset": 5362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4886, "endOffset": 4938, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5069, "endOffset": 5130, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5167, "endOffset": 5225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5262, "endOffset": 5300, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5407, "endOffset": 5789, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5484, "endOffset": 5536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5667, "endOffset": 5728, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1324", "url": "file:///C:/Users/<USER>/OneDrive/Documents/Arien-AI/Arien-cli-main/packages/cli/src/ui/hooks/useConsoleMessages.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 17}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "useConsoleMessages", "ranges": [{"startOffset": 617, "endOffset": 2811, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 950, "endOffset": 1718, "count": 5}, {"startOffset": 1004, "endOffset": 1025, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1138, "endOffset": 1669, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1234, "endOffset": 1635, "count": 8}, {"startOffset": 1290, "endOffset": 1356, "count": 3}, {"startOffset": 1357, "endOffset": 1429, "count": 1}, {"startOffset": 1431, "endOffset": 1550, "count": 1}, {"startOffset": 1530, "endOffset": 1534, "count": 0}, {"startOffset": 1550, "endOffset": 1627, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1797, "endOffset": 1964, "count": 11}, {"startOffset": 1854, "endOffset": 1960, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2060, "endOffset": 2158, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2264, "endOffset": 2492, "count": 3}, {"startOffset": 2349, "endOffset": 2453, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2542, "endOffset": 2726, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2584, "endOffset": 2720, "count": 9}, {"startOffset": 2645, "endOffset": 2712, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}